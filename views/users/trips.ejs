<%# views/users/trips.ejs %>

<%# views/users/activities.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">
    <main class="user-main-content">
        <div class="content-header">
            <h1><%= pageTitle %></h1>
            <div class="content-actions">
                <a href="/user/trip/new" class="button button-primary">Neue Reise erstellen</a>
            </div>
        </div>

        <% if (trips && trips.length > 0) { %>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Titel</th>
                            <th>Zeitraum</th>
                            <th>Aktivitäten</th>
                            <th>Geplante Routen</th>
                            <th>POIs</th>
                            <th>Gesamt-Statistiken</th>
                            <th>Status</th>
                            <th>Aktionen</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% trips.forEach(trip => { %>
                            <tr>
                                <td>
                                    <strong><%= trip.title %></strong>
                                    <% if (trip.description) { %>
                                        <br><small class="text-muted"><%= trip.description.substring(0, 100) %><%= trip.description.length > 100 ? '...' : '' %></small>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (trip.start_date || trip.end_date) { %>
                                        <% if (trip.start_date && trip.end_date) { %>
                                            <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                                        <% } else if (trip.start_date) { %>
                                            ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                                        <% } else { %>
                                            bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                                        <% } %>
                                    <% } else { %>
                                        <span class="text-muted">Kein Datum</span>
                                    <% } %>
                                </td>
                                <td class="number">
                                    <%= trip.activities_count || 0 %>
                                </td>
                                <td class="number">
                                    <%= trip.planned_routes_count || 0 %>
                                </td>
                                <td class="number">
                                    <%= trip.pois_count || 0 %>
                                </td>
                                <td class="number">
                                    <% if (trip.total_distance > 0) { %>
                                        <%= (trip.total_distance / 1000).toFixed(1).replace('.', ',') %> km<br>
                                        <small class="text-muted">
                                            <%= Math.round(trip.total_elevation_gain || 0) %> hm
                                            <% if (trip.total_moving_time > 0) { %>
                                                | <%= Math.floor((trip.total_moving_time || 0) / 3600) %>h <%= Math.floor(((trip.total_moving_time || 0) % 3600) / 60) %>m
                                            <% } %>
                                        </small>
                                    <% } else { %>
                                        <span class="text-muted">-</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (trip.is_public) { %>
                                        <span class="status-public" title="Öffentlich sichtbar">🌐 Öffentlich</span>
                                    <% } else { %>
                                        <span class="status-private" title="Nur für Sie sichtbar">🔒 Privat</span>
                                    <% } %>
                                </td>
                                <td class="actions">
                                    <a href="/user/trip/<%= trip.id %>" class="button-link-small button" title="Details ansehen">Details</a>
                                    <a href="/user/trip/<%= trip.id %>/edit" class="button-link-small button-secondary" title="Bearbeiten">Bearbeiten</a>
                                    <% if (trip.is_public) { %>
                                        <a href="/show/trip/<%= trip.share_uuid %>" class="button-link-small button-info" title="Öffentlichen Link öffnen" target="_blank">Öffentlich</a>
                                    <% } %>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="empty-state">
                <h3>Noch keine Reisen erstellt</h3>
                <p>Erstellen Sie Ihre erste Reise und verknüpfen Sie Aktivitäten, geplante Routen und POIs damit.</p>
                <a href="/user/trip/new" class="button button-primary">Erste Reise erstellen</a>
            </div>
        <% } %>
    </main>

    <style>
        .status-public {
            color: #28a745;
            font-weight: bold;
        }
        .status-private {
            color: #6c757d;
            font-weight: bold;
        }
        .text-muted {
            color: #6c757d;
            font-size: 0.9em;
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .empty-state h3 {
            margin-bottom: 1rem;
            color: #495057;
        }
        .empty-state p {
            margin-bottom: 2rem;
            color: #6c757d;
        }
    </style>
</body>
</html>
