<%# views/users/dashboard.ejs %>
<%# Das Layout 'layouts/main_layout' wird verwendet. %>

<% pageTitle = 'Mein Dashboard' %>
<% pageSpecificClass = 'user-dashboard-page' %>



<div class="page-specific-content">
    <p style="margin-bottom: 25px;">Dies ist dein persönlicher Bereich. Hier findest du eine schnelle Übersicht und Zugriff auf deine wichtigsten Funktionen.</p>

    <div class="dashboard-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">

        <section class="content-section quick-links-section">
            <h3>Schnellzugriff</h3>
            <ul style="list-style: none; padding: 0;">
                <% if (locals.currentUser && currentUser.username) { %>
                    <li style="margin-bottom: 10px;"><a href="/user/<%= encodeURIComponent(currentUser.username) %>/map" class="button button-link" style="display: block;">🗺️ Meine Ka<PERSON> anzeigen</a></li>
                    <li style="margin-bottom: 10px;"><a href="/user/activities" class="button button-link" style="display: block;">🚴 Meine Aktivitäten verwalten</a></li>
                    <li style="margin-bottom: 10px;"><a href="/user/<%= encodeURIComponent(currentUser.username) %>/stats" class="button button-link" style="display: block;">📊 Meine Statistiken einsehen</a></li>
                <% } %>
                <li style="margin-bottom: 10px;"><a href="/user/equipment" class="button button-link" style="display: block;">👟 Meine Ausrüstung</a></li>
                <li style="margin-bottom: 10px;"><a href="/user/friends" class="button button-link" style="display: block;">🧑‍🤝‍🧑 Meine Freunde</a></li>
                <% if (currentUser && currentUser.garmin_connected) { %>
                <li style="margin-bottom: 10px;"><a href="/user/garmin/activities" class="button button-link" style="display: block;">⌚ Garmin-Aktivitäten</a></li>
                <% } %>
                <li style="margin-bottom: 10px;"><a href="/user/settings" class="button button-link button-secondary" style="display: block;">⚙️ Einstellungen</a></li>
            </ul>
        </section>

        <section class="content-section latest-activities-section">
            <h3>Neueste Aktivitäten</h3>
            <% if (locals.latestActivities && latestActivities.length > 0) { %>
                <ul style="list-style: none; padding: 0;">
                    <% latestActivities.forEach(activity => { %>
                        <li style="padding: 8px 0; border-bottom: 1px dotted #eee;">
                            <a href="/user/activity/<%= activity.id %>" title="Details für '<%= activity.activity_name || '(Unbenannt)' %>'"><%= activity.activity_name || '(Unbenannt)' %></a>
                            <small style="display: block; color: #6c757d;">
                                <%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleDateString('de-DE', {day:'2-digit', month:'short', year:'numeric'}) : '?' %>
                                - <%= activity.sport_type %>
                                <% if (!activity.is_owned_by_user) { %>
                                    <em>(Geteilt)</em>
                                <% } %>
                            </small>
                        </li>
                    <% }); %>
                </ul>
                <a href="/user/my-activities" class="button-link-small button-secondary" style="display:inline-block; margin-top:15px;">Alle meine Aktivitäten anzeigen</a>
            <% } else { %>
                <p>Noch keine Aktivitäten vorhanden.</p>
                <% if (locals.currentUser && currentUser.strava_access_token) { %>
                    <form action="/user/sync/strava/latest" method="POST" style="display: inline-block;">
                        <input type="hidden" name="count" value="10">
                        <button type="submit" class="button button-success button-link-small">Jetzt neueste Strava-Aktivitäten holen</button>
                    </form>
                <% } else if (locals.currentUser) { %>
                    <a href="/user/settings" class="button button-link-small">Strava verbinden, um Aktivitäten zu synchronisieren</a>
                <% } %>
            <% } %>
        </section>

        <section class="content-section latest-notifications-section">
            <h3>Neueste Benachrichtigungen</h3>
            <% if (locals.latestUnreadNotifications && latestUnreadNotifications.length > 0) { %>
                <ul style="list-style: none; padding: 0;">
                    <% latestUnreadNotifications.forEach(notif => { %>
                        <li style="padding: 8px 0; border-bottom: 1px dotted #eee; <%= notif.is_read ? 'opacity:0.7;' : '' %>">
                            <% if (notif.link) { %>
                                <a href="<%= notif.link %>" title="Benachrichtigung ansehen" class="mark-and-go-btn" data-id="<%= notif.id %>" data-read="<%= notif.is_read.toString() %>"><%- notif.message %></a>
                            <% } else { %>
                                <span><%- notif.message %></span>
                            <% } %>
                            <small style="display: block; color: #6c757d;">
                                <%= new Date(notif.created_at).toLocaleString('de-DE', {dateStyle:'short', timeStyle:'short'}) %>
                            </small>
                        </li>
                    <% }); %>
                </ul>
                <a href="/user/notifications" class="button-link-small button-secondary" style="display:inline-block; margin-top:15px;">Alle Benachrichtigungen anzeigen</a>
            <% } else { %>
                <p>Keine neuen ungelesenen Benachrichtigungen.</p>
            <% } %>
        </section>

        <%# Optional: Mini-Statistik Sektion %>
        <% if (locals.sevenDayStats) { %>
        <section class="content-section mini-stats-section">
        <h3>Aktivitäten der letzten 7 Tage</h3>
        <p>Anzahl: <%= sevenDayStats.count %></p>
        <p>Distanz: <%= (sevenDayStats.distance / 1000).toFixed(1) %> km</p>
        </section>
        <% } %>

    </div> <%# Ende .dashboard-grid %>
</div>

<%_ contentFor('pageScripts') %>
    <script>
        // Client-seitiges JS für das Dashboard, falls nötig (z.B. für "mark-and-go-btn")
        document.addEventListener('DOMContentLoaded', () => {
            console.log("Dashboard-spezifisches JavaScript geladen.");

            // Event-Listener für "Details anzeigen"-Links bei Benachrichtigungen,
            // die auch als gelesen markieren sollen (ähnlich wie in notifications.ejs)
            document.querySelectorAll('.mark-and-go-btn').forEach(link => {
                link.addEventListener('click', async (event) => {
                    const notifId = link.dataset.id;
                    const isAlreadyRead = link.dataset.read === 'true'; // Kommt als String
                    const linkHref = link.href;

                    if (!isAlreadyRead && notifId) {
                        event.preventDefault();
                        try {
                            const response = await fetch(`/user/api/notifications/${notifId}/read`, { method: 'POST' });
                            if (response.ok) {
                                const data = await response.json();
                                if (data.success && typeof updateUserNotificationsBadge === 'function') {
                                    updateUserNotificationsBadge(data.newUnreadCount);
                                }
                            }
                        } catch (e) { console.error("Fehler beim Markieren als gelesen vor Navigation:", e); }
                        window.location.href = linkHref;
                    }
                });
            });
        });
    </script>
<%_ %>
