<%# views/users/activity_planned_detail.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = locals.pageTitle || 'Geplante Aktivität Details' %>
<% pageSpecificClass = 'user-activity-planned-detail-page' %>

<%# Seitenspezifische Stylesheets (optional) %>
<% contentFor('pageStylesheets') %>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="/css/activity_map_with_charts.css" />
    <link rel="stylesheet" href="/css/planned-route-responsive.css" />
    <style>
        .route-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .route-info {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .route-info h3 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            flex-grow: 1;
        }
        .route-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        .source-icon {
            width: 24px;
            height: 24px;
            vertical-align: middle;
            margin-right: 5px;
        }
        .action-links p {
            margin-bottom: 15px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .form-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .route-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .route-actions {
                flex-direction: column;
                gap: 8px;
            }

            .route-actions .button,
            .route-actions button {
                width: 100%;
                text-align: center;
                margin-bottom: 8px;
            }

            .route-info {
                padding: 15px;
            }

            .info-label {
                width: 100px;
                font-size: 14px;
            }

            .info-value {
                font-size: 14px;
            }
        }
    </style>
<%_ %>

<h2 class="page-main-title"><%= plannedRoute.name || 'Unbenannte Route' %></h2>

<div class="page-specific-content">
    <div class="action-links">
        <p>
            <% if (locals.currentUser && currentUser.username) { %>
                <a href="/user/<%= encodeURIComponent(currentUser.username) %>/map" class="button-link-small button-secondary">&laquo; Meine Karte</a> |
                <a href="/user/activities/planned" class="button-link-small button-secondary">&laquo; Meine geplanten Aktivitäten</a>
            <% } %>
        </p>
    </div>

    <section class="content-section activity-map-section">
        <h3>Routenkarte</h3>
        <%- include('../partials/activity_map_with_charts', { activity: activity, showPhotos: false }) %>
    </section>

    <div class="route-details">
        <div class="route-info">
            <h3>Routeninformationen</h3>
            <div class="info-row">
                <div class="info-label">Name:</div>
                <div class="info-value"><%= plannedRoute.name || '(Unbenannt)' %></div>
            </div>
            <div class="info-row">
                <div class="info-label">Sportart:</div>
                <div class="info-value"><%= plannedRoute.sport_type || 'Nicht angegeben' %></div>
            </div>
            <div class="info-row">
                <div class="info-label">Distanz:</div>
                <div class="info-value"><%= plannedRoute.distance_m ? (plannedRoute.distance_m / 1000).toFixed(1).replace('.',',') + ' km' : 'Nicht verfügbar' %></div>
            </div>
            <div class="info-row">
                <div class="info-label">Höhenmeter ↗:</div>
                <div class="info-value"><%= plannedRoute.elevation_gain_m ? Math.round(plannedRoute.elevation_gain_m) + ' m' : 'Nicht verfügbar' %></div>
            </div>
            <div class="info-row">
                <div class="info-label">Höhenmeter ↘:</div>
                <div class="info-value"><%= plannedRoute.elevation_loss ? Math.round(plannedRoute.elevation_loss) + ' m' : 'Nicht verfügbar' %></div>
            </div>
            <% if (plannedRoute.elevation_min !== null && plannedRoute.elevation_max !== null) { %>
            <div class="info-row">
                <div class="info-label">Höhe min/max:</div>
                <div class="info-value"><%= Math.round(plannedRoute.elevation_min) %> m / <%= Math.round(plannedRoute.elevation_max) %> m</div>
            </div>
            <% } %>
            <div class="info-row">
                <div class="info-label">Erstellt am:</div>
                <div class="info-value"><%= plannedRoute.uploaded_at ? new Date(plannedRoute.uploaded_at).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : 'Unbekannt' %></div>
            </div>
            <div class="info-row">
                <div class="info-label">Quelle:</div>
                <div class="info-value">
                    <% if (source === 'komoot') { %>
                        Komoot
                    <% } else if (source === 'google_drive') { %>
                        Google Drive
                    <% } else if (source === 'url_scraper') { %>
                        URL Import
                    <% } else { %>
                        GPX Upload
                    <% } %>
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">GPX-Datei:</div>
                <div class="info-value"><%= plannedRoute.gpx_filename %></div>
            </div>
        </div>

        <div class="route-info">
            <h3>Beschreibung</h3>
            <% if (plannedRoute.description) { %>
                <p><%= plannedRoute.description %></p>
            <% } else { %>
                <p><em>Keine Beschreibung verfügbar</em></p>
            <% } %>

            <div class="route-actions">
                <a href="/user/activity/planned/<%= plannedRoute.id %>/edit" class="button">Bearbeiten</a>
                <a href="/gpx_files/<%= plannedRoute.gpx_filename %>" class="button button-secondary" download>GPX herunterladen</a>
                <a href="#" class="button button-success" id="sharePlannedRouteBtn">Mit Freunden teilen</a>
                <a href="#" class="button button-info" id="addPlannedRouteToTripBtn">Zu Reise hinzufügen</a>
                <form action="/user/activities/planned/delete/<%= plannedRoute.id %>" method="POST" style="display: inline;" onsubmit="return confirm('Geplante Route \'<%= plannedRoute.name || plannedRoute.id %>\' wirklich löschen?');">
                    <button type="submit" class="button button-delete">Löschen</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Bilder -->
    <% if (images && images.length > 0) { %>
    <section class="content-section">
        <h3>Bilder</h3>
        <div class="image-gallery" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 15px;">
            <% images.forEach(function(image) { %>
                <div class="image-item" style="text-align: center;">
                    <img src="/uploads/planned_routes/<%= plannedRoute.id %>/<%= image.filename %>"
                         alt="<%= image.caption || 'Bild' %>"
                         style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <% if (image.caption) { %>
                        <p style="margin: 10px 0 0 0; font-style: italic; color: #666;"><%= image.caption %></p>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </section>
    <% } %>

    <!-- URLs -->
    <% if (urls && urls.length > 0) { %>
    <section class="content-section">
        <h3>Weiterführende Links</h3>
        <div style="margin-top: 15px;">
            <% urls.forEach(function(url) { %>
                <div style="margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                    <strong><%= url.title || 'Link' %></strong><br>
                    <a href="<%= url.url %>" target="_blank" style="color: #007bff; text-decoration: none; word-break: break-all;"><%= url.url %></a>
                    <% if (url.description) { %>
                        <br><small style="color: #666; margin-top: 5px; display: block;"><%= url.description %></small>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </section>
    <% } %>

    <div class="back-link">
        <a href="/user/activities/planned" class="button button-secondary">« Zurück zur Übersicht</a>
    </div>
</div>

<%# Modal für das Teilen mit Freunden %>
<div id="sharePlannedRouteModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Geplante Route mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du diese geplante Route teilen möchtest:</p>
        <form id="sharePlannedRouteForm" action="/user/activity/planned/<%= plannedRoute.id %>/share" method="POST">
            <div id="friendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="button button-success">Teilen</button>
                <button type="button" class="button button-secondary" id="cancelShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<% contentFor('pageScripts') %>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.2/dist/chartjs-plugin-annotation.min.js"></script>
    <script>
      // Prüfe, ob die Plugins korrekt geladen wurden
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Chart.js loaded:', typeof Chart !== 'undefined');
        console.log('Chart.js version:', Chart.version);
        console.log('Annotation plugin loaded:', typeof ChartAnnotation !== 'undefined' || (Chart && Chart.Annotation));
      });
    </script>
    <script src="/js/activityMapWithCharts.js"></script>
    <script>
        // Sharing-Modal Funktionalität
        document.addEventListener('DOMContentLoaded', function() {
            const shareBtn = document.getElementById('sharePlannedRouteBtn');
            const shareModal = document.getElementById('sharePlannedRouteModal');
            const shareCloseBtn = shareModal.querySelector('.close');
            const shareCancelBtn = document.getElementById('cancelShareBtn');
            const friendsList = document.getElementById('friendsList');

            // Teilen-Modal öffnen und Freundesliste laden
            shareBtn.addEventListener('click', function(e) {
                e.preventDefault();
                shareModal.style.display = 'block';

                // Freundesliste laden
                fetch('/user/api/friends')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API Response:', data); // Debug-Log

                        if (!data.success) {
                            throw new Error(data.message || 'API returned success: false');
                        }

                        const friends = data.friends || [];

                        if (friends.length === 0) {
                            friendsList.innerHTML = '<p>Du hast noch keine Freunde hinzugefügt.</p>';
                        } else {
                            const friendsHtml = friends.map(friend => `
                                <div style="margin-bottom: 10px;">
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="checkbox" name="friendIds" value="${friend.friend_user_id}" style="margin-right: 10px;">
                                        <span>${friend.friend_username}</span>
                                    </label>
                                </div>
                            `).join('');
                            friendsList.innerHTML = friendsHtml;
                        }
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Freundesliste:', error);
                        friendsList.innerHTML = `<p>Fehler beim Laden der Freundesliste: ${error.message}</p>`;
                    });
            });

            // Teilen-Modal schließen
            shareCloseBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            shareCancelBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            // Modal schließen, wenn außerhalb geklickt wird
            window.addEventListener('click', function(event) {
                if (event.target === shareModal) {
                    shareModal.style.display = 'none';
                }
            });

            // Geplante Route zu Reise hinzufügen Modal
            const addPlannedRouteToTripBtn = document.getElementById('addPlannedRouteToTripBtn');
            if (addPlannedRouteToTripBtn) {
                addPlannedRouteToTripBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    showAddPlannedRouteToTripModal();
                });
            }

            function showAddPlannedRouteToTripModal() {
                // Erstelle Modal dynamisch
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.display = 'block';
                modal.innerHTML = `
                    <div class="modal-content">
                        <span class="close">&times;</span>
                        <h3>Geplante Route zu Reise hinzufügen</h3>
                        <div id="tripsListPlannedRoute">
                            <p>Lade Reisen...</p>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="button button-secondary" onclick="this.closest('.modal').remove()">Abbrechen</button>
                            <button type="button" class="button button-primary" id="confirmAddPlannedRouteToTrip">Hinzufügen</button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Modal schließen
                modal.querySelector('.close').addEventListener('click', function() {
                    modal.remove();
                });

                // Reisen laden
                loadTripsForPlannedRoute();

                // Hinzufügen bestätigen
                document.getElementById('confirmAddPlannedRouteToTrip').addEventListener('click', function() {
                    const selectedTrip = modal.querySelector('input[name="tripId"]:checked');
                    if (!selectedTrip) {
                        alert('Bitte wählen Sie eine Reise aus.');
                        return;
                    }

                    const tripId = selectedTrip.value;
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/user/trip/${tripId}/add-planned-route`;

                    const plannedRouteInput = document.createElement('input');
                    plannedRouteInput.type = 'hidden';
                    plannedRouteInput.name = 'plannedRouteId';
                    plannedRouteInput.value = '<%= plannedRoute.id %>';
                    form.appendChild(plannedRouteInput);

                    document.body.appendChild(form);
                    form.submit();
                });
            }

            function loadTripsForPlannedRoute() {
                const tripsList = document.getElementById('tripsListPlannedRoute');

                fetch('/user/api/trips')
                    .then(response => response.json())
                    .then(trips => {
                        if (trips.length === 0) {
                            tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                            return;
                        }

                        let html = '<div class="trips-selection">';
                        trips.forEach(trip => {
                            html += `
                                <div class="trip-item" style="padding: 10px; border-bottom: 1px solid #eee;">
                                    <label style="cursor: pointer;">
                                        <input type="radio" name="tripId" value="${trip.id}" style="margin-right: 8px;">
                                        <strong>${trip.title}</strong>
                                        ${trip.start_date || trip.end_date ?
                                            `<br><small style="color: #666;">${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                            : ''}
                                    </label>
                                </div>
                            `;
                        });
                        html += '</div>';
                        tripsList.innerHTML = html;
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Reisen:', error);
                        tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
                    });
            }
        });
    </script>
<%_ %>
