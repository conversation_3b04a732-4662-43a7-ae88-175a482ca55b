<%# views/users/trip_detail.ejs %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

    <main class="user-main-content">
        <div class="content-header">
            <h1><%= trip.title %></h1>
            <div class="content-actions">
                <a href="/user/trip" class="button button-secondary">Zurück zur Übersicht</a>
                <% if (isOwner) { %>
                    <a href="/user/trip/<%= trip.id %>/edit" class="button button-primary">Bearbeiten</a>
                <% } %>
                <% if (trip.is_public) { %>
                    <a href="/show/trip/<%= trip.share_uuid %>" class="button button-info" target="_blank">Öffentlich anzeigen</a>
                <% } %>
            </div>
        </div>

        <!-- Reise-Informationen -->
        <div class="trip-info-card">
            <div class="trip-header">
                <div class="trip-meta">
                    <% if (trip.start_date || trip.end_date) { %>
                        <div class="trip-dates">
                            <strong>Zeitraum:</strong>
                            <% if (trip.start_date && trip.end_date) { %>
                                <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } else if (trip.start_date) { %>
                                ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                            <% } else { %>
                                bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } %>
                        </div>
                    <% } %>

                    <div class="trip-status">
                        <% if (trip.is_public) { %>
                            <span class="status-badge status-public">🌐 Öffentlich</span>
                        <% } else { %>
                            <span class="status-badge status-private">🔒 Privat</span>
                        <% } %>
                    </div>
                </div>

                <% if (trip.description) { %>
                    <div class="trip-description">
                        <p><%= trip.description %></p>
                    </div>
                <% } %>
            </div>

            <!-- Statistiken -->
            <div class="trip-stats">
                <div class="stat-item">
                    <div class="stat-number"><%= stats.activities_count %></div>
                    <div class="stat-label">Aktivitäten</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.planned_routes_count %></div>
                    <div class="stat-label">Geplante Routen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.pois_count %></div>
                    <div class="stat-label">POIs</div>
                </div>
                <% if (stats.total_distance > 0) { %>
                    <div class="stat-item">
                        <div class="stat-number"><%= (stats.total_distance / 1000).toFixed(1).replace('.', ',') %> km</div>
                        <div class="stat-label">Gesamtdistanz</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><%= Math.round(stats.total_elevation_gain) %> m</div>
                        <div class="stat-label">Höhenmeter</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <%= Math.floor(stats.total_moving_time / 3600) %>h <%= Math.floor((stats.total_moving_time % 3600) / 60) %>m
                        </div>
                        <div class="stat-label">Bewegungszeit</div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Karte und Bilder -->
        <% if ((geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) || (images && images.length > 0)) { %>
            <div class="trip-visual-section">
                <div class="visual-grid">
                    <!-- Karte -->
                    <% if (geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) { %>
                        <div class="map-container">
                            <h3>Karte der Reise</h3>
                            <div id="tripMap" style="height: 400px; border-radius: 8px; overflow: hidden;"></div>
                            <div class="map-legend" style="margin-top: 10px; font-size: 0.9em; color: #666;">
                                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                                    <% if (geoData.activities.length > 0) { %>
                                        <span>🏃‍♂️ Aktivitäten (<%= geoData.activities.length %>)</span>
                                    <% } %>
                                    <% if (geoData.plannedRoutes.length > 0) { %>
                                        <span>🗺️ Geplante Routen (<%= geoData.plannedRoutes.length %>)</span>
                                    <% } %>
                                    <% if (geoData.pois.length > 0) { %>
                                        <span>📍 POIs (<%= geoData.pois.length %>)</span>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } %>

                    <!-- Bilder -->
                    <% if (images && images.length > 0) { %>
                        <div class="images-container">
                            <h3>Bilder der Reise (<%= images.length %>)</h3>
                            <div class="images-grid">
                                <% images.forEach((image, index) => { %>
                                    <div class="image-item" onclick="openImageModal(<%- index %>)">
                                        <img src="/uploads/activity_photos/<%= image.activity_id %>/<%= image.base_filename %>_m.jpeg" alt="<%= image.caption || image.activity_name %>" loading="lazy">
                                        <div class="image-overlay">
                                            <div class="image-info">
                                                <div class="image-activity"><%= image.activity_name %></div>
                                                <% if (image.caption) { %>
                                                    <div class="image-caption"><%= image.caption %></div>
                                                <% } %>
                                                <div class="image-date"><%= new Date(image.start_date_local).toLocaleDateString('de-DE') %></div>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>

        <!-- Aktivitäten -->
        <% if (activities && activities.length > 0) { %>
            <div class="trip-section">
                <h2>Erledigte Aktivitäten (<%= activities.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Datum</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Zeit</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% activities.forEach(activity => { %>
                                <tr>
                                    <td>
                                        <a href="/user/activity/<%= activity.activity_id %>"><%= activity.activity_name %></a>
                                    </td>
                                    <td><%= activity.sport_type %></td>
                                    <td><%= new Date(activity.start_date_local).toLocaleDateString('de-DE') %></td>
                                    <td class="number"><%= (activity.distance / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(activity.total_elevation_gain) %> m</td>
                                    <td class="number">
                                        <%= Math.floor(activity.moving_time / 3600) %>h <%= Math.floor((activity.moving_time % 3600) / 60) %>m
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-activity" style="display: inline;">
                                                <input type="hidden" name="activityId" value="<%= activity.activity_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('Aktivität von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- Geplante Routen -->
        <% if (plannedRoutes && plannedRoutes.length > 0) { %>
            <div class="trip-section">
                <h2>Geplante Routen (<%= plannedRoutes.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Dauer</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% plannedRoutes.forEach(route => { %>
                                <tr>
                                    <td>
                                        <a href="/user/activity/planned/<%= route.planned_route_id %>"><%= route.name %></a>
                                    </td>
                                    <td><%= route.sport_type %></td>
                                    <td class="number"><%= (route.distance_m / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(route.elevation_gain_m) %> m</td>
                                    <td class="number">
                                        <% if (route.duration) { %>
                                            <%= Math.floor(route.duration / 3600) %>h <%= Math.floor((route.duration % 3600) / 60) %>m
                                        <% } else { %>
                                            -
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-planned-route" style="display: inline;">
                                                <input type="hidden" name="plannedRouteId" value="<%= route.planned_route_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('Geplante Route von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- POIs -->
        <% if (pois && pois.length > 0) { %>
            <div class="trip-section">
                <h2>Points of Interest (<%= pois.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Titel</th>
                                <th>Typ</th>
                                <th>Beschreibung</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% pois.forEach(poi => { %>
                                <tr>
                                    <td>
                                        <a href="/user/poi/<%= poi.poi_id %>"><%= poi.title %></a>
                                    </td>
                                    <td><%= poi.poi_type %></td>
                                    <td>
                                        <% if (poi.description) { %>
                                            <%= poi.description.substring(0, 100) %><%= poi.description.length > 100 ? '...' : '' %>
                                        <% } else { %>
                                            <span class="text-muted">Keine Beschreibung</span>
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-poi" style="display: inline;">
                                                <input type="hidden" name="poiId" value="<%= poi.poi_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('POI von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- Leere Zustände -->
        <% if ((!activities || activities.length === 0) && (!plannedRoutes || plannedRoutes.length === 0) && (!pois || pois.length === 0)) { %>
            <div class="empty-state">
                <h3>Noch keine Inhalte verknüpft</h3>
                <p>Fügen Sie Aktivitäten, geplante Routen oder POIs zu Ihrer Reise hinzu.</p>
                <% if (isOwner) { %>
                    <div class="empty-state-actions">
                        <a href="/user/activities" class="button button-primary">Aktivitäten verknüpfen</a>
                        <a href="/user/activities/planned" class="button button-secondary">Geplante Routen verknüpfen</a>
                        <a href="/user/pois" class="button button-secondary">POIs verknüpfen</a>
                    </div>
                <% } %>
            </div>
        <% } %>
    </main>

    <style>
        .trip-info-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .trip-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .trip-dates {
            color: #6c757d;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .status-public {
            background: #d4edda;
            color: #155724;
        }

        .status-private {
            background: #f8d7da;
            color: #721c24;
        }

        .trip-description {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .trip-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .trip-section {
            margin-bottom: 2rem;
        }

        .trip-section h2 {
            margin-bottom: 1rem;
            color: #495057;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .empty-state-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .text-muted {
            color: #6c757d;
            font-style: italic;
        }

        .trip-visual-section {
            margin-bottom: 2rem;
        }

        .visual-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .map-container, .images-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .map-container h3, .images-container h3 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #495057;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .image-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-item:hover {
            transform: scale(1.05);
        }

        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 1rem;
            transform: translateY(100%);
            transition: transform 0.2s ease;
        }

        .image-item:hover .image-overlay {
            transform: translateY(0);
        }

        .image-activity {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .image-caption {
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .image-date {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Image Modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .image-modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            height: 90%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .image-modal img {
            max-width: 100%;
            max-height: 80%;
            object-fit: contain;
        }

        .image-modal-info {
            color: white;
            text-align: center;
            margin-top: 1rem;
        }

        .image-modal-close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-modal-close:hover {
            color: #bbb;
        }

        @media (max-width: 768px) {
            .trip-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .trip-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .empty-state-actions {
                flex-direction: column;
                align-items: center;
            }

            .visual-grid {
                grid-template-columns: 1fr;
            }

            .images-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
        }
    </style>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img id="modalImage" src="" alt="">
            <div class="image-modal-info">
                <div id="modalImageActivity"></div>
                <div id="modalImageCaption"></div>
                <div id="modalImageDate"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <script>
        // Globale Variablen
        let tripMap;
        const images = <%- JSON.stringify(images || []) %>;
        const geoData = <%- JSON.stringify(geoData || {activities: [], plannedRoutes: [], pois: []}) %>;

        // Karte initialisieren
        <% if (geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) { %>
        document.addEventListener('DOMContentLoaded', function() {
            initTripMap();
        });

        function initTripMap() {
            // Karte erstellen
            tripMap = L.map('tripMap').setView([51.1657, 10.4515], 6); // Deutschland Zentrum

            // Tile Layer hinzufügen
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(tripMap);

            const allBounds = [];

            // Aktivitäten laden
            geoData.activities.forEach(activity => {
                if (activity.gpx) {
                    fetch(`/gpx_files/${activity.id}.gpx`)
                        .then(response => response.text())
                        .then(gpxText => {
                            const parser = new DOMParser();
                            const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                            const trackPoints = gpxDoc.querySelectorAll('trkpt');

                            if (trackPoints.length > 0) {
                                const coordinates = Array.from(trackPoints).map(point => [
                                    parseFloat(point.getAttribute('lat')),
                                    parseFloat(point.getAttribute('lon'))
                                ]);

                                const polyline = L.polyline(coordinates, {
                                    color: '#007bff',
                                    weight: 3,
                                    opacity: 0.8
                                }).addTo(tripMap);

                                polyline.bindPopup(`
                                    <strong>${activity.activity_name}</strong><br>
                                    Sport: ${activity.sport_type}<br>
                                    Distanz: ${(activity.distance / 1000).toFixed(1)} km<br>
                                    Datum: ${new Date(activity.start_date_local).toLocaleDateString('de-DE')}
                                `);

                                allBounds.push(...coordinates);
                            }
                        })
                        .catch(error => console.error('Error loading GPX:', error));
                }
            });

            // Geplante Routen laden
            geoData.plannedRoutes.forEach(route => {
                if (route.gpx_filename) {
                    fetch(`/gpx_files/planned/${route.gpx_filename}`)
                        .then(response => response.text())
                        .then(gpxText => {
                            const parser = new DOMParser();
                            const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                            const trackPoints = gpxDoc.querySelectorAll('trkpt, rtept');

                            if (trackPoints.length > 0) {
                                const coordinates = Array.from(trackPoints).map(point => [
                                    parseFloat(point.getAttribute('lat')),
                                    parseFloat(point.getAttribute('lon'))
                                ]);

                                const polyline = L.polyline(coordinates, {
                                    color: '#28a745',
                                    weight: 3,
                                    opacity: 0.8,
                                    dashArray: '5, 5'
                                }).addTo(tripMap);

                                polyline.bindPopup(`
                                    <strong>${route.name}</strong><br>
                                    Sport: ${route.sport_type}<br>
                                    Distanz: ${(route.distance_m / 1000).toFixed(1)} km<br>
                                    <em>Geplante Route</em>
                                `);

                                allBounds.push(...coordinates);
                            }
                        })
                        .catch(error => console.error('Error loading GPX:', error));
                }
            });

            // POIs hinzufügen
            geoData.pois.forEach(poi => {
                const marker = L.marker([poi.latitude, poi.longitude]).addTo(tripMap);
                marker.bindPopup(`
                    <strong>${poi.title}</strong><br>
                    Typ: ${poi.poi_type}<br>
                    ${poi.description ? poi.description.substring(0, 100) + '...' : ''}
                `);
                allBounds.push([poi.latitude, poi.longitude]);
            });

            // Karte an alle Punkte anpassen
            if (allBounds.length > 0) {
                setTimeout(() => {
                    tripMap.fitBounds(allBounds, { padding: [20, 20] });
                }, 1000);
            }
        }
        <% } %>

        // Bild-Modal Funktionen
        function openImageModal(index) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalActivity = document.getElementById('modalImageActivity');
            const modalCaption = document.getElementById('modalImageCaption');
            const modalDate = document.getElementById('modalImageDate');

            const image = images[index];

            modalImage.src = `/uploads/activity_photos/${image.activity_id}/${image.base_filename}_o.jpeg`;
            modalImage.alt = image.caption || image.activity_name;
            modalActivity.textContent = image.activity_name;
            modalCaption.textContent = image.caption || '';
            modalDate.textContent = new Date(image.start_date_local).toLocaleDateString('de-DE');

            modal.style.display = 'block';
        }

        // Modal schließen
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');

            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    modal.style.display = 'none';
                });
            }

            // Modal schließen bei Klick außerhalb
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });

            // ESC-Taste zum Schließen
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    modal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
