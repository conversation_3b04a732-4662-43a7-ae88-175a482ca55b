<%# views/users/trip_detail.ejs %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

    <main class="user-main-content">
        <div class="content-header">
            <h1><%= trip.title %></h1>
            <div class="content-actions">
                <a href="/user/trip" class="button button-secondary">Zurück zur Übersicht</a>
                <% if (isOwner) { %>
                    <a href="/user/trip/<%= trip.id %>/edit" class="button button-primary">Bearbeiten</a>
                <% } %>
                <% if (trip.is_public) { %>
                    <a href="/show/trip/<%= trip.share_uuid %>" class="button button-info" target="_blank">Öffentlich anzeigen</a>
                <% } %>
            </div>
        </div>

        <!-- Reise-Informationen -->
        <div class="trip-info-card">
            <div class="trip-header">
                <div class="trip-meta">
                    <% if (trip.start_date || trip.end_date) { %>
                        <div class="trip-dates">
                            <strong>Zeitraum:</strong>
                            <% if (trip.start_date && trip.end_date) { %>
                                <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } else if (trip.start_date) { %>
                                ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                            <% } else { %>
                                bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } %>
                        </div>
                    <% } %>
                    
                    <div class="trip-status">
                        <% if (trip.is_public) { %>
                            <span class="status-badge status-public">🌐 Öffentlich</span>
                        <% } else { %>
                            <span class="status-badge status-private">🔒 Privat</span>
                        <% } %>
                    </div>
                </div>
                
                <% if (trip.description) { %>
                    <div class="trip-description">
                        <p><%= trip.description %></p>
                    </div>
                <% } %>
            </div>

            <!-- Statistiken -->
            <div class="trip-stats">
                <div class="stat-item">
                    <div class="stat-number"><%= stats.activities_count %></div>
                    <div class="stat-label">Aktivitäten</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.planned_routes_count %></div>
                    <div class="stat-label">Geplante Routen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.pois_count %></div>
                    <div class="stat-label">POIs</div>
                </div>
                <% if (stats.total_distance > 0) { %>
                    <div class="stat-item">
                        <div class="stat-number"><%= (stats.total_distance / 1000).toFixed(1).replace('.', ',') %> km</div>
                        <div class="stat-label">Gesamtdistanz</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><%= Math.round(stats.total_elevation_gain) %> m</div>
                        <div class="stat-label">Höhenmeter</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <%= Math.floor(stats.total_moving_time / 3600) %>h <%= Math.floor((stats.total_moving_time % 3600) / 60) %>m
                        </div>
                        <div class="stat-label">Bewegungszeit</div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Aktivitäten -->
        <% if (activities && activities.length > 0) { %>
            <div class="trip-section">
                <h2>Erledigte Aktivitäten (<%= activities.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Datum</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Zeit</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% activities.forEach(activity => { %>
                                <tr>
                                    <td>
                                        <a href="/user/activity/<%= activity.activity_id %>"><%= activity.activity_name %></a>
                                    </td>
                                    <td><%= activity.sport_type %></td>
                                    <td><%= new Date(activity.start_date_local).toLocaleDateString('de-DE') %></td>
                                    <td class="number"><%= (activity.distance / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(activity.total_elevation_gain) %> m</td>
                                    <td class="number">
                                        <%= Math.floor(activity.moving_time / 3600) %>h <%= Math.floor((activity.moving_time % 3600) / 60) %>m
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-activity" style="display: inline;">
                                                <input type="hidden" name="activityId" value="<%= activity.activity_id %>">
                                                <button type="submit" class="button-link-small button-danger" 
                                                        onclick="return confirm('Aktivität von der Reise entfernen?')" 
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- Geplante Routen -->
        <% if (plannedRoutes && plannedRoutes.length > 0) { %>
            <div class="trip-section">
                <h2>Geplante Routen (<%= plannedRoutes.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Dauer</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% plannedRoutes.forEach(route => { %>
                                <tr>
                                    <td>
                                        <a href="/user/activity/planned/<%= route.planned_route_id %>"><%= route.name %></a>
                                    </td>
                                    <td><%= route.sport_type %></td>
                                    <td class="number"><%= (route.distance_m / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(route.elevation_gain_m) %> m</td>
                                    <td class="number">
                                        <% if (route.duration) { %>
                                            <%= Math.floor(route.duration / 3600) %>h <%= Math.floor((route.duration % 3600) / 60) %>m
                                        <% } else { %>
                                            -
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-planned-route" style="display: inline;">
                                                <input type="hidden" name="plannedRouteId" value="<%= route.planned_route_id %>">
                                                <button type="submit" class="button-link-small button-danger" 
                                                        onclick="return confirm('Geplante Route von der Reise entfernen?')" 
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- POIs -->
        <% if (pois && pois.length > 0) { %>
            <div class="trip-section">
                <h2>Points of Interest (<%= pois.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Titel</th>
                                <th>Typ</th>
                                <th>Beschreibung</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% pois.forEach(poi => { %>
                                <tr>
                                    <td>
                                        <a href="/user/poi/<%= poi.poi_id %>"><%= poi.title %></a>
                                    </td>
                                    <td><%= poi.poi_type %></td>
                                    <td>
                                        <% if (poi.description) { %>
                                            <%= poi.description.substring(0, 100) %><%= poi.description.length > 100 ? '...' : '' %>
                                        <% } else { %>
                                            <span class="text-muted">Keine Beschreibung</span>
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-poi" style="display: inline;">
                                                <input type="hidden" name="poiId" value="<%= poi.poi_id %>">
                                                <button type="submit" class="button-link-small button-danger" 
                                                        onclick="return confirm('POI von der Reise entfernen?')" 
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- Leere Zustände -->
        <% if ((!activities || activities.length === 0) && (!plannedRoutes || plannedRoutes.length === 0) && (!pois || pois.length === 0)) { %>
            <div class="empty-state">
                <h3>Noch keine Inhalte verknüpft</h3>
                <p>Fügen Sie Aktivitäten, geplante Routen oder POIs zu Ihrer Reise hinzu.</p>
                <% if (isOwner) { %>
                    <div class="empty-state-actions">
                        <a href="/user/activities" class="button button-primary">Aktivitäten verknüpfen</a>
                        <a href="/user/activities/planned" class="button button-secondary">Geplante Routen verknüpfen</a>
                        <a href="/user/pois" class="button button-secondary">POIs verknüpfen</a>
                    </div>
                <% } %>
            </div>
        <% } %>
    </main>

    <style>
        .trip-info-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .trip-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .trip-dates {
            color: #6c757d;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .status-public {
            background: #d4edda;
            color: #155724;
        }
        
        .status-private {
            background: #f8d7da;
            color: #721c24;
        }
        
        .trip-description {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .trip-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .trip-section {
            margin-bottom: 2rem;
        }
        
        .trip-section h2 {
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 2rem 0;
        }
        
        .empty-state-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .text-muted {
            color: #6c757d;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .trip-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .trip-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .empty-state-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</body>
</html>
