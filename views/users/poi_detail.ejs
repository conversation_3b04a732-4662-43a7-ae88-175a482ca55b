<%# views/users/poi_detail.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = locals.pageTitle || 'POI Details' %>
<% pageSpecificClass = 'user-poi-detail-page' %>

<%# Seitenspezifische Stylesheets %>
<% contentFor('pageStylesheets') %>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        .poi-detail-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .poi-header {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .poi-title {
            font-size: 28px;
            font-weight: 600;
            color: #212529;
            margin: 0 0 15px 0;
        }

        .poi-type-badge {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 20px;
        }

        .poi-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .meta-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .meta-value {
            color: #212529;
            font-family: monospace;
            font-size: 14px;
        }

        .poi-description {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .description-content {
            margin: 0;
            line-height: 1.6;
            color: #495057;
            font-size: 16px;
        }

        .description-content strong {
            color: #212529;
            font-weight: 600;
        }

        .poi-map {
            height: 400px;
            width: 100%;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .poi-images {
            margin-bottom: 30px;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .image-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-item img:hover {
            transform: scale(1.05);
        }

        .poi-urls {
            margin-bottom: 30px;
        }

        .urls-list {
            list-style: none;
            padding: 0;
            margin-top: 15px;
        }

        .url-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .url-info {
            flex: 1;
        }

        .url-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .url-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            word-break: break-all;
        }

        .url-link:hover {
            text-decoration: underline;
        }

        .poi-actions {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
            color: white;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            display: none; /* Standardmäßig ausgeblendet */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            margin-top: -10px;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .friend-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .friend-item:hover {
            background-color: #f8f9fa;
        }

        .friend-item:last-child {
            border-bottom: none;
        }

        .friend-item label {
            display: block;
            cursor: pointer;
        }

        .friend-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .form-actions {
            margin-top: 15px;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        @media (max-width: 768px) {
            .poi-detail-container {
                padding: 15px;
            }

            .poi-header {
                padding: 20px;
            }

            .poi-title {
                font-size: 24px;
            }

            .poi-meta {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .poi-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
<%_ %>

<div class="poi-detail-container">
    <!-- POI Header -->
    <div class="poi-header">
        <h1 class="poi-title"><%= poi.title %></h1>
        <div class="poi-type-badge">
            <span style="margin-right: 8px;"><%-
                (function(type) {
                    const emojiMap = {
                        'restaurant': '🍽️', 'cafe_bistro': '☕', 'bar_kneipe': '🍺', 'schnellimbiss': '🍔', 'essen_sonstiges': '🍽️',
                        'lebensmittel': '🛒', 'shopping_center': '🏬', 'fachgeschaeft': '🏪',
                        'oeffentlicher_nahverkehr': '🚌', 'autofahrer': '⛽', 'fahrrad': '🚲', 'verkehr_sonstiges': '🚗',
                        'sehenswuerdigkeit': '🏛️', 'kultur_unterhaltung': '🎭', 'natur_erholung': '🌳', 'sport_aktivitaeten': '⚽',
                        'notfall': '🚨', 'arzt_praxis': '🏥', 'apotheke': '💊', 'therapie_wellness': '💆',
                        'hotel': '🏨', 'alternative_unterkunft': '🏠', 'camping_wohnmobil': '🏕️',
                        'finanzen': '🏦', 'oeffentliche_verwaltung': '🏢', 'bildung': '🎓', 'sonstige_dienstleistungen': '🔧',
                        // Geocaching-Typen
                        'traditional_cache': '🗺️', 'multi_cache': '🔗', 'mystery_cache': '❓', 'letterbox_cache': '📮',
                        'earthcache': '🌍', 'wherigo_cache': '📱', 'event_cache': '📅', 'mega_event': '🎪',
                        'giga_event': '🏟️', 'cito_event': '🧹', 'virtual_cache': '👻', 'webcam_cache': '📷', 'unknown_cache': '🗺️'
                    };
                    return emojiMap[type] || '📍';
                })(poi.poi_type)
            %></span>
            <%-
                (function(type) {
                    const typeMap = {
                        'restaurant': 'Restaurant', 'cafe_bistro': 'Café & Bistro', 'bar_kneipe': 'Bar & Kneipe',
                        'schnellimbiss': 'Schnellimbiss & To-Go', 'essen_sonstiges': 'Essen & Trinken (Sonstiges)',
                        'lebensmittel': 'Lebensmittel', 'shopping_center': 'Shopping-Center & Kaufhäuser', 'fachgeschaeft': 'Fachgeschäft',
                        'oeffentlicher_nahverkehr': 'Öffentlicher Nahverkehr', 'autofahrer': 'Für Autofahrer', 'fahrrad': 'Fahrrad & Co.',
                        'verkehr_sonstiges': 'Verkehr & Mobilität (Sonstiges)', 'sehenswuerdigkeit': 'Sehenswürdigkeit',
                        'kultur_unterhaltung': 'Kultur & Unterhaltung', 'natur_erholung': 'Natur & Erholung',
                        'sport_aktivitaeten': 'Sport & Aktivitäten', 'notfall': 'Notfall', 'arzt_praxis': 'Arzt & Praxis',
                        'apotheke': 'Apotheke', 'therapie_wellness': 'Therapie & Wellness', 'hotel': 'Hotel',
                        'alternative_unterkunft': 'Alternative Unterkunft', 'camping_wohnmobil': 'Camping & Wohnmobil',
                        'finanzen': 'Finanzen', 'oeffentliche_verwaltung': 'Öffentliche Verwaltung', 'bildung': 'Bildung',
                        'sonstige_dienstleistungen': 'Sonstige Dienstleistungen',
                        // Geocaching-Typen
                        'traditional_cache': 'Traditional Cache', 'multi_cache': 'Multi-Cache', 'mystery_cache': 'Mystery/Puzzle Cache',
                        'letterbox_cache': 'Letterbox Hybrid', 'earthcache': 'EarthCache', 'wherigo_cache': 'Wherigo Cache',
                        'event_cache': 'Event Cache', 'mega_event': 'Mega-Event Cache', 'giga_event': 'Giga-Event Cache',
                        'cito_event': 'CITO Event', 'virtual_cache': 'Virtual Cache', 'webcam_cache': 'Webcam Cache',
                        'unknown_cache': 'Unbekannter Cache-Typ'
                    };
                    return typeMap[type] || type;
                })(poi.poi_type)
            %>
        </div>

        <div class="poi-meta">
            <div class="meta-item">
                <span class="meta-label">Koordinaten</span>
                <span class="meta-value"><%= parseFloat(poi.latitude).toFixed(6) %>, <%= parseFloat(poi.longitude).toFixed(6) %></span>
            </div>
            <div class="meta-item">
                <span class="meta-label">Erstellt am</span>
                <span class="meta-value"><%= new Date(poi.created_at).toLocaleDateString('de-DE') %></span>
            </div>
            <% if (poi.source) { %>
                <div class="meta-item">
                    <span class="meta-label">Quelle</span>
                    <span class="meta-value">
                        <% if (poi.source === 'GC-GPX' && poi.source_url) { %>
                            🗺️ <a href="<%= poi.source_url %>" target="_blank" rel="noopener noreferrer" style="color: #007bff; text-decoration: none;">
                                Geocaching.com
                            </a>
                        <% } else if (poi.source_url) { %>
                            <a href="<%= poi.source_url %>" target="_blank" rel="noopener noreferrer" style="color: #007bff; text-decoration: none;">
                                <%= poi.source %>
                            </a>
                        <% } else { %>
                            <%= poi.source %>
                        <% } %>
                    </span>
                </div>
            <% } %>
            <% if (poi.updated_at && poi.updated_at !== poi.created_at) { %>
                <div class="meta-item">
                    <span class="meta-label">Zuletzt geändert</span>
                    <span class="meta-value"><%= new Date(poi.updated_at).toLocaleDateString('de-DE') %></span>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Beschreibung -->
    <% if (poi.description) { %>
        <div class="poi-description">
            <h2 class="section-title">Beschreibung</h2>
            <div class="description-content">
                <%- poi.description.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') %>
            </div>
        </div>
    <% } %>

    <!-- Karte -->
    <div class="poi-map" id="poi-map"></div>

    <!-- Bilder -->
    <% if (images && images.length > 0) { %>
        <div class="poi-images">
            <h2 class="section-title">Bilder (<%= images.length %>)</h2>
            <div class="images-grid">
                <% images.forEach(function(image) { %>
                    <div class="image-item">
                        <img src="/uploads/pois/<%= poi.id %>/<%= image.thumbnail_filename || image.filename %>"
                             alt="<%= image.caption || 'POI Bild' %>"
                             onclick="openImageModal('/uploads/pois/<%= poi.id %>/<%= image.filename %>', '<%= image.caption || '' %>')">
                    </div>
                <% }); %>
            </div>
        </div>
    <% } %>

    <!-- URLs -->
    <% if (urls && urls.length > 0) { %>
        <div class="poi-urls">
            <h2 class="section-title">Links (<%= urls.length %>)</h2>
            <ul class="urls-list">
                <% urls.forEach(function(url) { %>
                    <li class="url-item">
                        <div class="url-info">
                            <% if (url.title) { %>
                                <div class="url-title"><%= url.title %></div>
                            <% } %>
                            <a href="<%= url.url %>" target="_blank" class="url-link"><%= url.url %></a>
                            <% if (url.description) { %>
                                <div class="url-description"><%= url.description %></div>
                            <% } %>
                        </div>
                    </li>
                <% }); %>
            </ul>
        </div>
    <% } %>

    <!-- Aktionen -->
    <div class="poi-actions">
        <a href="/user/pois" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Zurück zur Übersicht
        </a>
        <a href="/user/poi/<%= poi.id %>/edit" class="btn btn-primary">
            <i class="fas fa-edit"></i> Bearbeiten
        </a>
        <a href="#" class="btn btn-success" id="sharePOIBtn">
            <i class="fas fa-share"></i> Mit Freunden teilen
        </a>
        <a href="#" class="btn btn-info" id="addPOIToTripBtn">
            <i class="fas fa-route"></i> Zu Reise hinzufügen
        </a>
        <form action="/user/poi/<%= poi.id %>/delete" method="POST" style="display: inline;"
              onsubmit="return confirm('POI \'<%= poi.title %>\' wirklich löschen?');">
            <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash"></i> Löschen
            </button>
        </form>
    </div>
</div>

<%# Modal für das Teilen mit Freunden %>
<div id="sharePOIModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>POI mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du diesen POI teilen möchtest:</p>
        <form id="sharePOIForm" action="/user/poi/<%= poi.id %>/share" method="POST">
            <div id="friendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-success">Teilen</button>
                <button type="button" class="btn btn-secondary" id="cancelShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<% contentFor('pageScripts') %>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // POI Emoji-Icon Funktionen
        function createPOIEmojiIcon(poiType) {
            const emoji = getPOITypeEmoji(poiType);

            return L.divIcon({
                html: `<div style="
                    background: white;
                    border: 2px solid #007bff;
                    border-radius: 50%;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                ">${emoji}</div>`,
                className: 'poi-emoji-marker',
                iconSize: [32, 32],
                iconAnchor: [16, 16],
                popupAnchor: [0, -16]
            });
        }

        function getPOITypeEmoji(poiType) {
            const emojiMap = {
                'restaurant': '🍽️', 'cafe_bistro': '☕', 'bar_kneipe': '🍺', 'schnellimbiss': '🍔', 'essen_sonstiges': '🍽️',
                'lebensmittel': '🛒', 'shopping_center': '🏬', 'fachgeschaeft': '🏪',
                'oeffentlicher_nahverkehr': '🚌', 'autofahrer': '⛽', 'fahrrad': '🚲', 'verkehr_sonstiges': '🚗',
                'sehenswuerdigkeit': '🏛️', 'kultur_unterhaltung': '🎭', 'natur_erholung': '🌳', 'sport_aktivitaeten': '⚽',
                'notfall': '🚨', 'arzt_praxis': '🏥', 'apotheke': '💊', 'therapie_wellness': '💆',
                'hotel': '🏨', 'alternative_unterkunft': '🏠', 'camping_wohnmobil': '🏕️',
                'finanzen': '🏦', 'oeffentliche_verwaltung': '🏢', 'bildung': '🎓', 'sonstige_dienstleistungen': '🔧',
                // Geocaching-Typen
                'traditional_cache': '🗺️', 'multi_cache': '🔗', 'mystery_cache': '❓', 'letterbox_cache': '📮',
                'earthcache': '🌍', 'wherigo_cache': '📱', 'event_cache': '📅', 'mega_event': '🎪',
                'giga_event': '🏟️', 'cito_event': '🧹', 'virtual_cache': '👻', 'webcam_cache': '📷', 'unknown_cache': '🗺️'
            };
            return emojiMap[poiType] || '📍';
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Karte initialisieren
            const map = L.map('poi-map').setView([<%= parseFloat(poi.latitude) %>, <%= parseFloat(poi.longitude) %>], 15);

            // OpenStreetMap-Layer hinzufügen
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // POI-Marker mit Emoji-Icon hinzufügen
            const poiIcon = createPOIEmojiIcon('<%= poi.poi_type %>');
            const marker = L.marker([<%= parseFloat(poi.latitude) %>, <%= parseFloat(poi.longitude) %>], {
                icon: poiIcon
            }).addTo(map);
            marker.bindPopup('<strong><%= poi.title %></strong><br><%= poi.poi_type %>').openPopup();
        });

        // Bild-Modal-Funktion
        function openImageModal(imageSrc, caption) {
            // Einfache Modal-Implementierung
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 1000; display: flex;
                justify-content: center; align-items: center; cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = 'max-width: 90%; max-height: 90%; border-radius: 8px;';

            modal.appendChild(img);
            document.body.appendChild(modal);

            modal.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        }

        // Sharing-Modal Funktionalität
        document.addEventListener('DOMContentLoaded', function() {
            const shareBtn = document.getElementById('sharePOIBtn');
            const shareModal = document.getElementById('sharePOIModal');
            const shareCloseBtn = shareModal.querySelector('.close');
            const shareCancelBtn = document.getElementById('cancelShareBtn');
            const friendsList = document.getElementById('friendsList');

            // Teilen-Modal öffnen und Freundesliste laden
            shareBtn.addEventListener('click', function(e) {
                e.preventDefault();
                shareModal.style.display = 'block';

                // Freundesliste laden
                fetch('/user/api/friends')
                    .then(response => response.json())
                    .then(data => {
                        if (data.friends && data.friends.length > 0) {
                            let html = '';
                            data.friends.forEach(friend => {
                                html += `
                                    <div class="friend-item">
                                        <label>
                                            <input type="checkbox" name="friendIds" value="${friend.friend_user_id}">
                                            ${friend.friend_username}
                                        </label>
                                    </div>
                                `;
                            });
                            friendsList.innerHTML = html;
                        } else {
                            friendsList.innerHTML = '<p>Du hast noch keine Freunde, mit denen du diesen POI teilen könntest.</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Freundesliste:', error);
                        friendsList.innerHTML = '<p class="error">Fehler beim Laden der Freundesliste.</p>';
                    });
            });

            // Teilen-Modal schließen
            shareCloseBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            shareCancelBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            // Modal schließen, wenn außerhalb geklickt wird
            window.addEventListener('click', function(event) {
                if (event.target === shareModal) {
                    shareModal.style.display = 'none';
                }
            });
        });

        // POI zu Reise hinzufügen Modal
        const addPOIToTripBtn = document.getElementById('addPOIToTripBtn');
        if (addPOIToTripBtn) {
            addPOIToTripBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showAddToTripModal();
            });
        }

        function showAddToTripModal() {
            // Erstelle Modal dynamisch
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h3>POI zu Reise hinzufügen</h3>
                    <div id="tripsListPOI">
                        <p>Lade Reisen...</p>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Abbrechen</button>
                        <button type="button" class="btn btn-primary" id="confirmAddPOIToTrip">Hinzufügen</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Modal schließen
            modal.querySelector('.close').addEventListener('click', function() {
                modal.remove();
            });

            // Reisen laden
            loadTripsForPOI();

            // Hinzufügen bestätigen
            document.getElementById('confirmAddPOIToTrip').addEventListener('click', function() {
                const selectedTrip = modal.querySelector('input[name="tripId"]:checked');
                if (!selectedTrip) {
                    alert('Bitte wählen Sie eine Reise aus.');
                    return;
                }

                const tripId = selectedTrip.value;
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user/trip/${tripId}/add-poi`;

                const poiInput = document.createElement('input');
                poiInput.type = 'hidden';
                poiInput.name = 'poiId';
                poiInput.value = '<%= poi.id %>';
                form.appendChild(poiInput);

                document.body.appendChild(form);
                form.submit();
            });
        }

        function loadTripsForPOI() {
            const tripsList = document.getElementById('tripsListPOI');

            fetch('/user/api/trips')
                .then(response => response.json())
                .then(trips => {
                    if (trips.length === 0) {
                        tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                        return;
                    }

                    let html = '<div class="trips-selection">';
                    trips.forEach(trip => {
                        html += `
                            <div class="trip-item" style="padding: 10px; border-bottom: 1px solid #eee;">
                                <label style="cursor: pointer;">
                                    <input type="radio" name="tripId" value="${trip.id}" style="margin-right: 8px;">
                                    <strong>${trip.title}</strong>
                                    ${trip.start_date || trip.end_date ?
                                        `<br><small style="color: #666;">${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                        : ''}
                                </label>
                            </div>
                        `;
                    });
                    html += '</div>';
                    tripsList.innerHTML = html;
                })
                .catch(error => {
                    console.error('Fehler beim Laden der Reisen:', error);
                    tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
                });
        }
    </script>
<%_ %>
