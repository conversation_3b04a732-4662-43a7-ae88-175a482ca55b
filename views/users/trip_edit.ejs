<%# views/users/trip_edit.ejs %>

    <main class="user-main-content">
        <div class="content-header">
            <h1><%= pageTitle %></h1>
            <div class="content-actions">
                <% if (isEdit && trip) { %>
                    <a href="/user/trip/<%= trip.id %>" class="button button-secondary">Zurück zu Details</a>
                <% } else { %>
                    <a href="/user/trip" class="button button-secondary">Zurück zur Übersicht</a>
                <% } %>
            </div>
        </div>

        <div class="form-container">
            <form method="POST" action="<%= isEdit ? `/user/trip/${trip.id}/edit` : '/user/trip' %>" class="trip-form">
                <div class="form-group">
                    <label for="title">Titel der Reise *</label>
                    <input 
                        type="text" 
                        id="title" 
                        name="title" 
                        value="<%= trip ? trip.title : '' %>" 
                        required 
                        maxlength="255"
                        placeholder="z.B. Alpenüberquerung 2024"
                    >
                </div>

                <div class="form-group">
                    <label for="description">Beschreibung</label>
                    <textarea 
                        id="description" 
                        name="description" 
                        rows="4" 
                        placeholder="Beschreiben Sie Ihre Reise..."
                    ><%= trip ? (trip.description || '') : '' %></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="start_date">Startdatum</label>
                        <input 
                            type="date" 
                            id="start_date" 
                            name="start_date" 
                            value="<%= trip && trip.start_date ? trip.start_date : '' %>"
                        >
                    </div>

                    <div class="form-group">
                        <label for="end_date">Enddatum</label>
                        <input 
                            type="date" 
                            id="end_date" 
                            name="end_date" 
                            value="<%= trip && trip.end_date ? trip.end_date : '' %>"
                        >
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input 
                            type="checkbox" 
                            id="is_public" 
                            name="is_public" 
                            <%= trip && trip.is_public ? 'checked' : '' %>
                        >
                        <label for="is_public">
                            Reise öffentlich teilen
                            <small class="help-text">Wenn aktiviert, kann die Reise über einen öffentlichen Link geteilt werden.</small>
                        </label>
                    </div>
                </div>

                <% if (isEdit && trip && trip.is_public) { %>
                    <div class="form-group">
                        <label>Öffentlicher Link</label>
                        <div class="public-link-display">
                            <input 
                                type="text" 
                                value="<%= `${process.env.BASE_URL || 'http://localhost:3000'}/show/trip/${trip.share_uuid}` %>" 
                                readonly 
                                class="public-link-input"
                                id="publicLinkInput"
                            >
                            <button type="button" class="button button-small" onclick="copyPublicLink()">Kopieren</button>
                        </div>
                        <small class="help-text">Dieser Link kann geteilt werden, um die Reise öffentlich anzuzeigen.</small>
                    </div>
                <% } %>

                <div class="form-actions">
                    <button type="submit" class="button button-primary">
                        <%= isEdit ? 'Reise aktualisieren' : 'Reise erstellen' %>
                    </button>
                    <a href="<%= isEdit && trip ? `/user/trip/${trip.id}` : '/user/trip' %>" class="button button-secondary">Abbrechen</a>
                </div>
            </form>

            <% if (isEdit && trip) { %>
                <div class="danger-zone">
                    <h3>Gefahrenbereich</h3>
                    <p>Das Löschen einer Reise kann nicht rückgängig gemacht werden. Alle Verknüpfungen zu Aktivitäten, geplanten Routen und POIs werden entfernt.</p>
                    <form method="POST" action="/user/trip/<%= trip.id %>/delete" onsubmit="return confirm('Sind Sie sicher, dass Sie diese Reise löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.');">
                        <button type="submit" class="button button-danger">Reise löschen</button>
                    </form>
                </div>
            <% } %>
        </div>
    </main>

    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .trip-form {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-top: 0.2rem;
        }
        
        .checkbox-group label {
            margin-bottom: 0;
        }
        
        .help-text {
            display: block;
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .public-link-display {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .public-link-input {
            flex: 1;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .danger-zone {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .danger-zone h3 {
            color: #c53030;
            margin-bottom: 0.5rem;
        }
        
        .danger-zone p {
            color: #742a2a;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .public-link-display {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>

    <script>
        function copyPublicLink() {
            const input = document.getElementById('publicLinkInput');
            input.select();
            input.setSelectionRange(0, 99999); // Für mobile Geräte
            
            try {
                document.execCommand('copy');
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Kopiert!';
                button.classList.add('button-success');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('button-success');
                }, 2000);
            } catch (err) {
                console.error('Fehler beim Kopieren:', err);
                alert('Link konnte nicht kopiert werden. Bitte manuell kopieren.');
            }
        }
        
        // Datum-Validierung
        document.addEventListener('DOMContentLoaded', function() {
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            
            function validateDates() {
                if (startDateInput.value && endDateInput.value) {
                    if (startDateInput.value > endDateInput.value) {
                        endDateInput.setCustomValidity('Das Enddatum muss nach dem Startdatum liegen.');
                    } else {
                        endDateInput.setCustomValidity('');
                    }
                } else {
                    endDateInput.setCustomValidity('');
                }
            }
            
            startDateInput.addEventListener('change', validateDates);
            endDateInput.addEventListener('change', validateDates);
        });
    </script>
</body>
</html>
