<%# views/users/trip_activity_suggestions.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = locals.pageTitle || 'Aktivitäten-Vorschläge' %>
<% pageSpecificClass = 'user-trip-activity-suggestions-page' %>

<h2 class="page-main-title"><%= pageTitle %></h2>

<div class="page-specific-content">
    <div class="content-header">
        <div class="content-actions">
            <a href="/user/trip/<%= trip.id %>" class="button button-secondary">Zurück zur Reise</a>
            <a href="/user/trip" class="button button-secondary">Alle Reisen</a>
        </div>
    </div>

    <!-- Reise-Info -->
    <div class="trip-info-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3><%= trip.title %></h3>
        <% if (trip.start_date || trip.end_date) { %>
            <p><strong>Zeitraum:</strong>
                <% if (trip.start_date && trip.end_date) { %>
                    <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                <% } else if (trip.start_date) { %>
                    ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                <% } else { %>
                    bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                <% } %>
            </p>
        <% } %>
        <% if (trip.description) { %>
            <p><%= trip.description %></p>
        <% } %>
    </div>

    <% if (suggestedActivities && suggestedActivities.length > 0) { %>
        <div class="suggestions-section">
            <h3>Aktivitäten im Reisezeitraum (<%= suggestedActivities.length %>)</h3>
            <p>Folgende Aktivitäten wurden im Zeitraum Ihrer Reise durchgeführt und können hinzugefügt werden:</p>

            <!-- Bulk-Aktionen -->
            <div class="bulk-actions" style="margin: 20px 0; padding: 15px; background: #e8f4fd; border-radius: 8px;">
                <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                    <label style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="selectAllSuggestions">
                        <strong>Alle auswählen</strong>
                    </label>
                    <span id="selectedSuggestionsCount" style="font-weight: bold;">0 Aktivitäten ausgewählt</span>
                    <button type="button" id="addSelectedSuggestionsBtn" class="button button-primary" disabled>Ausgewählte hinzufügen</button>
                    <button type="button" id="clearSuggestionsSelectionBtn" class="button button-secondary">Auswahl aufheben</button>
                </div>
            </div>

            <!-- Aktivitäten-Liste -->
            <div class="suggestions-list">
                <% suggestedActivities.forEach(activity => { %>
                    <div class="suggestion-item" style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: white;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <input type="checkbox" class="suggestion-checkbox" value="<%= activity.id %>" data-activity-name="<%= activity.activity_name || '(Unbenannt)' %>">
                            
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                    <h4 style="margin: 0; color: #333;">
                                        <a href="/user/activity/<%= activity.id %>" style="text-decoration: none; color: #007bff;">
                                            <%= activity.activity_name || '(Unbenannt)' %>
                                        </a>
                                    </h4>
                                    <span style="color: #666; font-size: 0.9em;">
                                        <%= new Date(activity.start_date_local).toLocaleDateString('de-DE') %>
                                    </span>
                                </div>
                                
                                <div style="display: flex; gap: 20px; color: #666; font-size: 0.9em;">
                                    <span><strong>Sport:</strong> <%= activity.sport_type %></span>
                                    <span><strong>Distanz:</strong> <%= activity.distance_m ? (activity.distance_m / 1000).toFixed(1).replace('.', ',') + ' km' : '-' %></span>
                                    <span><strong>Höhenmeter:</strong> <%= activity.elevation_gain_m ? Math.round(activity.elevation_gain_m) + ' m' : '-' %></span>
                                    <% if (activity.photo_count_from_db > 0) { %>
                                        <span><strong>Fotos:</strong> <%= activity.photo_count_from_db %></span>
                                    <% } %>
                                </div>
                            </div>
                            
                            <div style="display: flex; gap: 8px;">
                                <a href="/user/activity/<%= activity.id %>" class="button button-small button-secondary">Details</a>
                                <button type="button" class="button button-small button-primary add-single-activity" data-activity-id="<%= activity.id %>" data-activity-name="<%= activity.activity_name || '(Unbenannt)' %>">
                                    Hinzufügen
                                </button>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    <% } else { %>
        <div class="no-suggestions" style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px;">
            <h3>Keine Aktivitäten gefunden</h3>
            <% if (trip.start_date || trip.end_date) { %>
                <p>Im Zeitraum Ihrer Reise wurden keine Aktivitäten gefunden.</p>
            <% } else { %>
                <p>Bitte definieren Sie ein Start- und/oder Enddatum für Ihre Reise, um Aktivitäten-Vorschläge zu erhalten.</p>
                <a href="/user/trip/<%= trip.id %>/edit" class="button button-primary">Reise bearbeiten</a>
            <% } %>
        </div>
    <% } %>
</div>

<% contentFor('pageScripts') %>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllSuggestions');
    const suggestionCheckboxes = document.querySelectorAll('.suggestion-checkbox');
    const selectedCount = document.getElementById('selectedSuggestionsCount');
    const addSelectedBtn = document.getElementById('addSelectedSuggestionsBtn');
    const clearSelectionBtn = document.getElementById('clearSuggestionsSelectionBtn');
    const addSingleBtns = document.querySelectorAll('.add-single-activity');

    // Alle auswählen/abwählen
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            suggestionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Einzelne Checkboxen
    suggestionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            
            // "Alle auswählen" Checkbox aktualisieren
            const checkedCount = document.querySelectorAll('.suggestion-checkbox:checked').length;
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === suggestionCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < suggestionCheckboxes.length;
            }
        });
    });

    // Auswahl aufheben
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function() {
            suggestionCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
            updateBulkActions();
        });
    }

    // Ausgewählte hinzufügen
    if (addSelectedBtn) {
        addSelectedBtn.addEventListener('click', function() {
            const selectedActivities = Array.from(document.querySelectorAll('.suggestion-checkbox:checked'));
            if (selectedActivities.length === 0) {
                alert('Bitte wählen Sie mindestens eine Aktivität aus.');
                return;
            }
            
            bulkAddActivitiesToTrip(selectedActivities.map(cb => cb.value));
        });
    }

    // Einzelne Aktivität hinzufügen
    addSingleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const activityId = this.dataset.activityId;
            const activityName = this.dataset.activityName;
            
            if (confirm(`Aktivität "${activityName}" zur Reise hinzufügen?`)) {
                addSingleActivityToTrip(activityId);
            }
        });
    });

    function updateBulkActions() {
        const checkedCount = document.querySelectorAll('.suggestion-checkbox:checked').length;
        selectedCount.textContent = `${checkedCount} Aktivität${checkedCount !== 1 ? 'en' : ''} ausgewählt`;
        addSelectedBtn.disabled = checkedCount === 0;
    }

    function addSingleActivityToTrip(activityId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/user/trip/<%= trip.id %>/add-activity`;
        
        const activityInput = document.createElement('input');
        activityInput.type = 'hidden';
        activityInput.name = 'activityId';
        activityInput.value = activityId;
        form.appendChild(activityInput);
        
        document.body.appendChild(form);
        form.submit();
    }

    function bulkAddActivitiesToTrip(activityIds) {
        // Zeige Loading-Indikator
        const loadingDiv = document.createElement('div');
        loadingDiv.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 10000;';
        loadingDiv.innerHTML = '<p>Füge Aktivitäten zur Reise hinzu...</p>';
        document.body.appendChild(loadingDiv);

        // Sequenziell hinzufügen (um Server nicht zu überlasten)
        let completed = 0;

        activityIds.forEach((activityId, index) => {
            setTimeout(() => {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user/trip/<%= trip.id %>/add-activity`;
                form.style.display = 'none';
                
                const activityInput = document.createElement('input');
                activityInput.type = 'hidden';
                activityInput.name = 'activityId';
                activityInput.value = activityId;
                form.appendChild(activityInput);
                
                document.body.appendChild(form);
                
                completed++;
                if (completed === activityIds.length) {
                    // Nach dem letzten Submit zur Reise-Detailseite weiterleiten
                    setTimeout(() => {
                        window.location.href = `/user/trip/<%= trip.id %>`;
                    }, 500);
                }
                
                form.submit();
            }, index * 200); // 200ms Verzögerung zwischen Requests
        });
    }
});
</script>
<%_ %>
