<%# views/users/activities_planned.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = 'Meine geplanten Aktivitäten' %>
<% pageSpecificClass = 'user-activities-planned-page' %>

<%# Seitenspezifische Stylesheets (optional, falls user_layout.css nicht alles abdeckt) %>
<%# contentFor('pageStylesheets') %>
    <%# <link rel="stylesheet" href="/css/activities_planned_specific.css"> %>
<%# _%>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">

    <%# --- Filter Formular --- %>
    <form class="filter-form" method="GET" action="/user/activities/planned">
        <div class="filter-grid">
            <div class="form-group">
                <label for="search">Name/ID suchen:</label>
                <input type="text" id="search" name="search" value="<%= locals.filters && filters.search ? filters.search : '' %>" placeholder="Routenname oder ID">
            </div>
            <div class="form-group">
                <label for="type">Sportart:</label>
                <select id="type" name="type">
                    <option value="">-- Alle Sportarten --</option>
                    <% if (locals.sportTypesForFilter && sportTypesForFilter.length > 0) { %>
                        <% sportTypesForFilter.forEach(sportType => { %>
                            <option value="<%= sportType %>" <%= locals.filters && filters.type === sportType ? 'selected' : '' %>><%= sportType %></option>
                        <% }); %>
                    <% } %>
                </select>
            </div>
            <div class="form-group">
                <label for="date_from">Datum von:</label>
                <input type="date" id="date_from" name="date_from" value="<%= locals.filters && filters.date_from ? filters.date_from : '' %>">
            </div>
            <div class="form-group">
                <label for="date_to">Datum bis:</label>
                <input type="date" id="date_to" name="date_to" value="<%= locals.filters && filters.date_to ? filters.date_to : '' %>">
            </div>
            <div class="form-group">
                <label for="source">Quelle:</label>
                <select id="source" name="source">
                    <option value="">-- Alle Quellen --</option>
                    <option value="komoot" <%= locals.filters && filters.source === 'komoot' ? 'selected' : '' %>>Komoot</option>
                    <option value="google_drive" <%= locals.filters && filters.source === 'google_drive' ? 'selected' : '' %>>Google Drive</option>
                    <option value="gpx_upload" <%= locals.filters && filters.source === 'gpx_upload' ? 'selected' : '' %>>GPX Upload</option>
                    <option value="url_scraper" <%= locals.filters && filters.source === 'url_scraper' ? 'selected' : '' %>>URL Scraper</option>
                </select>
            </div>
            <div class="form-group">
                <label for="sharing_status">Geteilt:</label>
                <select id="sharing_status" name="sharing_status">
                    <option value="">-- Alle --</option>
                    <option value="shared" <%= locals.filters && filters.sharing_status === 'shared' ? 'selected' : '' %>>Geteilt</option>
                    <option value="not_shared" <%= locals.filters && filters.sharing_status === 'not_shared' ? 'selected' : '' %>>Nicht geteilt</option>
                </select>
            </div>
            <div class="form-group">
                <label for="dist_min">Distanz Min (km):</label>
                <input type="number" id="dist_min" name="dist_min" value="<%= locals.filters && filters.dist_min ? filters.dist_min : '' %>" step="0.1" placeholder="z.B. 5">
            </div>
            <div class="form-group">
                <label for="dist_max">Distanz Max (km):</label>
                <input type="number" id="dist_max" name="dist_max" value="<%= locals.filters && filters.dist_max ? filters.dist_max : '' %>" step="0.1" placeholder="z.B. 100">
            </div>
            <div class="form-group">
                <label for="elev_min">Höhe Min (m):</label>
                <input type="number" id="elev_min" name="elev_min" value="<%= locals.filters && filters.elev_min ? filters.elev_min : '' %>" step="50" placeholder="z.B. 100">
            </div>
            <div class="form-group">
                <label for="elev_max">Höhe Max (m):</label>
                <input type="number" id="elev_max" name="elev_max" value="<%= locals.filters && filters.elev_max ? filters.elev_max : '' %>" step="50" placeholder="z.B. 1000">
            </div>
        </div>
        <div class="actions" style="grid-column: 1 / -1; margin-top: 15px;">
            <%# Versteckte Felder für aktuelle Sortierung, damit sie beim Filtern erhalten bleibt %>
            <input type="hidden" name="sort" value="<%= locals.sort && sort.by ? sort.by : 'uploaded_at' %>">
            <input type="hidden" name="order" value="<%= locals.sort && sort.order ? sort.order : 'DESC' %>">
            <button type="submit" class="button">Filter anwenden</button>
            <a href="/user/activities/planned" class="button button-secondary" style="margin-left: 10px;">Filter zurücksetzen</a>
        </div>
    </form>

    <% if (locals.pagination && pagination.filteredPlannedRoutes !== undefined && pagination.totalPlannedRoutes !== undefined) { %>
        <div class="total-count" style="margin-top: 20px; margin-bottom: 10px; font-weight: bold;">
            <% if (pagination.filteredPlannedRoutes === pagination.totalPlannedRoutes) { %>
                <%= pagination.totalPlannedRoutes %> Geplante Aktivität<%= pagination.totalPlannedRoutes !== 1 ? 'en' : '' %> gefunden.
            <% } else { %>
                <%= pagination.filteredPlannedRoutes %> von <%= pagination.totalPlannedRoutes %> Geplante<%= pagination.totalPlannedRoutes !== 1 ? 'n' : '' %> Aktivität<%= pagination.totalPlannedRoutes !== 1 ? 'en' : '' %> werden angezeigt.
            <% } %>
        </div>
    <% } %>

    <% if (locals.plannedRoutes && plannedRoutes.length > 0) { %>
        <%
            // Hilfsfunktion, um Query-Parameter für Sortierlinks zu erstellen
            // Behält alle aktuellen Filter bei, außer Paginierung
            const createSortParams = (currentFilters, currentSortBy, currentSortOrder, newSortBy) => {
                const params = new URLSearchParams();
                if (currentFilters) {
                    if (currentFilters.search) params.set('search', currentFilters.search);
                    if (currentFilters.type) params.set('type', currentFilters.type);
                    if (currentFilters.date_from) params.set('date_from', currentFilters.date_from);
                    if (currentFilters.date_to) params.set('date_to', currentFilters.date_to);
                    if (currentFilters.source) params.set('source', currentFilters.source);
                    if (currentFilters.dist_min) params.set('dist_min', currentFilters.dist_min);
                    if (currentFilters.dist_max) params.set('dist_max', currentFilters.dist_max);
                    if (currentFilters.elev_min) params.set('elev_min', currentFilters.elev_min);
                    if (currentFilters.elev_max) params.set('elev_max', currentFilters.elev_max);
                    if (currentFilters.sharing_status) params.set('sharing_status', currentFilters.sharing_status);
                }
                params.set('sort', newSortBy);
                params.set('order', (currentSortBy === newSortBy && currentSortOrder === 'ASC') ? 'DESC' : 'ASC');
                return params.toString();
            };
        %>
        <!-- Bulk-Aktionen -->
        <div class="bulk-actions" style="margin-bottom: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; display: none;">
            <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                <span style="font-weight: bold;">Ausgewählte Routen:</span>
                <button type="button" id="bulkDeleteBtn" class="button button-delete">Löschen</button>
                <button type="button" id="bulkShareBtn" class="button button-success">Teilen</button>
                <button type="button" id="bulkAddToTripBtn" class="button button-primary">Zu Reise hinzufügen</button>
                <span id="selectedCount" style="margin-left: auto; font-style: italic;">0 ausgewählt</span>
            </div>
        </div>

        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 40px;"><input type="checkbox" id="selectAll" title="Alle auswählen/abwählen"></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'name') %>" class="<%= sort.by === 'name' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Name</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'uploaded_at') %>" class="<%= sort.by === 'uploaded_at' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Datum</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'sport_type') %>" class="<%= sort.by === 'sport_type' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Sportart</a></th>
                    <th class="number"><a href="?<%= createSortParams(filters, sort.by, sort.order, 'distance_m') %>" class="<%= sort.by === 'distance_m' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Distanz</a></th>
                    <th class="number"><a href="?<%= createSortParams(filters, sort.by, sort.order, 'elevation_gain_m') %>" class="<%= sort.by === 'elevation_gain_m' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Höhe</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'source') %>" class="<%= sort.by === 'source' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Quelle</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'sharing_status') %>" class="<%= sort.by === 'sharing_status' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Geteilt</a></th>
                    <th>Aktionen</th>
                </tr>
            </thead>
            <tbody>
                <% plannedRoutes.forEach(route => { %>
                    <tr>
                        <td>
                            <input type="checkbox" class="route-checkbox" value="<%= route.id %>" data-route-name="<%= route.name || '(Unbenannt)' %>">
                        </td>
                        <td>
                            <a href="/user/activity/planned/<%= route.id %>" title="Details für '<%= route.name || '(Unbenannt)' %>'"><%= route.name || '(Unbenannt)' %></a>
                        </td>
                        <td><%= route.uploaded_at ? new Date(route.uploaded_at).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : '?' %></td>
                        <td><%= route.sport_type || '-' %></td>
                        <td class="number"><%= route.distance_m != null ? (route.distance_m / 1000).toFixed(1).replace('.',',') + ' km' : '-' %></td>
                        <td class="number"><%= route.elevation_gain_m != null ? Math.round(route.elevation_gain_m) + ' m' : '-' %></td>
                        <td><%= route.source || '-' %></td>
                        <td>
                            <% if (route.sharing_status === 'shared') { %>
                                <span class="status-shared" title="Mit <%= route.shared_count %> Freund<%= route.shared_count !== 1 ? 'en' : '' %> geteilt">✓ Geteilt (<%= route.shared_count %>)</span>
                            <% } else { %>
                                <span class="status-not-shared">Nicht geteilt</span>
                            <% } %>
                        </td>
                        <td class="actions">
                            <a href="/user/activity/planned/<%= route.id %>" class="button-link-small button" title="Details ansehen">Details</a>
                            <form action="/user/activities/planned/delete/<%= route.id %>" method="POST" style="display: inline;" onsubmit="return confirm('Geplante Route \'<%= route.name || route.id %>\' wirklich löschen?');">
                                <button type="submit" class="button-delete button-link-small">Löschen</button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>

        <%# Mobile Card Layout für Planned Activities mit Bulk-Funktionen %>
        <div class="mobile-cards-container">
            <%# Mobile Bulk-Aktionen Header %>
            <div style="background: #f5f5f5; padding: 10px; margin-bottom: 15px; border-radius: 5px; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 5px; font-weight: bold;">
                    <input type="checkbox" id="selectAllMobile" title="Alle auswählen/abwählen" style="margin: 0;">
                    Alle auswählen
                </label>
            </div>
            <% plannedRoutes.forEach(route => { %>
                <div class="mobile-card owned">
                    <div class="mobile-card-header">
                        <div style="display: flex; align-items: center; gap: 10px; flex: 1;">
                            <input type="checkbox" class="route-checkbox" value="<%= route.id %>" data-route-name="<%= route.name || '(Unbenannt)' %>" style="margin: 0;">
                            <a href="/user/activity/planned/<%= route.id %>" class="mobile-card-title" title="Details für '<%= route.name || '(Unbenannt)' %>' " style="flex: 1;">
                                <%= route.name || '(Unbenannt)' %>
                            </a>
                        </div>
                        <div class="mobile-card-date">
                            <%= route.uploaded_at ? new Date(route.uploaded_at).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : '?' %>
                        </div>
                    </div>

                    <div class="mobile-card-meta">
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Sportart</div>
                            <div class="mobile-card-meta-value"><%= route.sport_type || '-' %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Distanz</div>
                            <div class="mobile-card-meta-value"><%= route.distance_m != null ? (route.distance_m / 1000).toFixed(1).replace('.',',') + ' km' : '-' %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Höhenmeter</div>
                            <div class="mobile-card-meta-value"><%= route.elevation_gain_m != null ? Math.round(route.elevation_gain_m) + ' m' : '-' %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Quelle</div>
                            <div class="mobile-card-meta-value"><%= route.source || '-' %></div>
                        </div>
                    </div>

                    <div class="mobile-card-meta" style="margin-bottom: 8px;">
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Status</div>
                            <div class="mobile-card-meta-value">
                                <% if (route.sharing_status === 'shared') { %>
                                    <span class="status-shared" title="Mit <%= route.shared_count %> Freund<%= route.shared_count !== 1 ? 'en' : '' %> geteilt">✓ Geteilt (<%= route.shared_count %>)</span>
                                <% } else { %>
                                    <span class="status-not-shared">Nicht geteilt</span>
                                <% } %>
                            </div>
                        </div>
                    </div>

                    <div class="mobile-card-actions">
                        <a href="/user/activity/planned/<%= route.id %>" class="button-primary" title="Details ansehen">Details</a>
                        <form action="/user/activities/planned/delete/<%= route.id %>" method="POST" style="display: inline;" onsubmit="return confirm('Geplante Route \'<%= route.name || route.id %>\' wirklich löschen?');">
                            <button type="submit" class="button-delete">Löschen</button>
                        </form>
                    </div>
                </div>
            <% }); %>
        </div>

        <%# Paginierung %>
        <% if (locals.pagination && pagination.totalPages > 1) { %>
            <div class="pagination">
                <%
                    const createPageParams = (currentFilters, currentSort, currentOrder, pageNum) => {
                        const params = new URLSearchParams();
                        if (currentFilters) {
                            if (currentFilters.search) params.set('search', currentFilters.search);
                            if (currentFilters.type) params.set('type', currentFilters.type);
                            if (currentFilters.date_from) params.set('date_from', currentFilters.date_from);
                            if (currentFilters.date_to) params.set('date_to', currentFilters.date_to);
                            if (currentFilters.source) params.set('source', currentFilters.source);
                            if (currentFilters.dist_min) params.set('dist_min', currentFilters.dist_min);
                            if (currentFilters.dist_max) params.set('dist_max', currentFilters.dist_max);
                            if (currentFilters.elev_min) params.set('elev_min', currentFilters.elev_min);
                            if (currentFilters.elev_max) params.set('elev_max', currentFilters.elev_max);
                            if (currentFilters.sharing_status) params.set('sharing_status', currentFilters.sharing_status);
                        }
                        if (currentSort && currentSort.by) params.set('sort', currentSort.by);
                        if (currentSort && currentSort.order) params.set('order', currentSort.order);
                        if (pageNum) params.set('page', pageNum);
                        return params.toString();
                    };
                %>
                <% if (pagination.currentPage > 1) { %>
                    <a href="?<%= createPageParams(filters, sort, sort.order, pagination.currentPage - 1) %>">« Zurück</a>
                <% } else { %>
                    <span class="disabled">« Zurück</span>
                <% } %>

                <%# Vereinfachte Seitenzahlen-Anzeige %>
                <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                    <% if (i === pagination.currentPage) { %>
                        <span class="current"><%= i %></span>
                    <% } else { %>
                        <a href="?<%= createPageParams(filters, sort, sort.order, i) %>"><%= i %></a>
                    <% } %>
                <% } %>

                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <a href="?<%= createPageParams(filters, sort, sort.order, pagination.currentPage + 1) %>">Weiter »</a>
                <% } else { %>
                    <span class="disabled">Weiter »</span>
                <% } %>
            </div>
        <% } %>

    <% } else if (locals.plannedRoutes) { %>
        <p class="no-activities" style="margin-top:20px; text-align:center;">Keine geplanten Aktivitäten gefunden, die den Filterkriterien entsprechen.</p>
    <% } %>
</div>

<%# Modals für Bulk-Aktionen %>
<div id="bulkShareModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Ausgewählte Routen mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du die ausgewählten Routen teilen möchtest:</p>
        <form id="bulkShareForm" action="/user/activities/planned/bulk-share" method="POST">
            <div id="bulkShareRoutesList" style="margin: 15px 0; max-height: 150px; overflow-y: auto; background: #f9f9f9; padding: 10px; border-radius: 5px;">
                <!-- Wird dynamisch gefüllt -->
            </div>
            <div id="bulkShareFriendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="button button-success">Teilen</button>
                <button type="button" class="button button-secondary" id="cancelBulkShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 10px;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Status Styles */
.status-shared {
    color: #28a745;
    font-weight: bold;
}

.status-not-shared {
    color: #6c757d;
    font-style: italic;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllMobileCheckbox = document.getElementById('selectAllMobile');
    const routeCheckboxes = document.querySelectorAll('.route-checkbox');
    const bulkActionsDiv = document.querySelector('.bulk-actions');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkShareBtn = document.getElementById('bulkShareBtn');
    const bulkAddToTripBtn = document.getElementById('bulkAddToTripBtn');
    const bulkShareModal = document.getElementById('bulkShareModal');
    const bulkShareForm = document.getElementById('bulkShareForm');
    const bulkShareRoutesList = document.getElementById('bulkShareRoutesList');
    const bulkShareFriendsList = document.getElementById('bulkShareFriendsList');
    const cancelBulkShareBtn = document.getElementById('cancelBulkShareBtn');
    const closeBtn = bulkShareModal.querySelector('.close');

    // Funktion zum Aktualisieren der Bulk-Aktionen-Anzeige
    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.route-checkbox:checked');
        const count = selectedCheckboxes.length;

        if (count > 0) {
            bulkActionsDiv.style.display = 'block';
            selectedCountSpan.textContent = `${count} ausgewählt`;
        } else {
            bulkActionsDiv.style.display = 'none';
        }

        // "Alle auswählen" Checkbox Status aktualisieren (Desktop und Mobile)
        const updateSelectAllCheckboxes = (count) => {
            const checkboxes = [selectAllCheckbox, selectAllMobileCheckbox].filter(cb => cb);
            checkboxes.forEach(checkbox => {
                if (count === 0) {
                    checkbox.indeterminate = false;
                    checkbox.checked = false;
                } else if (count === routeCheckboxes.length) {
                    checkbox.indeterminate = false;
                    checkbox.checked = true;
                } else {
                    checkbox.indeterminate = true;
                }
            });
        };

        updateSelectAllCheckboxes(count);
    }

    // Event Listener für "Alle auswählen" (Desktop und Mobile)
    const handleSelectAllChange = function() {
        const isChecked = this.checked;
        routeCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateBulkActions();
    };

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', handleSelectAllChange);
    }
    if (selectAllMobileCheckbox) {
        selectAllMobileCheckbox.addEventListener('change', handleSelectAllChange);
    }

    // Event Listener für einzelne Checkboxen
    routeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // Bulk-Löschen
    bulkDeleteBtn.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.route-checkbox:checked');
        const routeNames = Array.from(selectedCheckboxes).map(cb => cb.dataset.routeName).join(', ');

        if (confirm(`Möchten Sie wirklich ${selectedCheckboxes.length} geplante Route(n) löschen?\n\nRouten: ${routeNames}`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/user/activities/planned/bulk-delete';

            selectedCheckboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'routeIds';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // Bulk zu Reise hinzufügen
    if (bulkAddToTripBtn) {
        bulkAddToTripBtn.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.route-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                alert('Bitte wählen Sie mindestens eine geplante Route aus.');
                return;
            }

            showBulkAddPlannedRoutesToTripModal(selectedCheckboxes);
        });
    }

    function showBulkAddPlannedRoutesToTripModal(selectedRoutes) {
        // Erstelle Modal dynamisch
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = 'display: block; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);';
        modal.innerHTML = `
            <div style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; border-radius: 8px; width: 80%; max-width: 500px; position: relative;">
                <span style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; position: absolute; right: 15px; top: 10px;">&times;</span>
                <h3>Geplante Routen zu Reise hinzufügen</h3>
                <p>Folgende ${selectedRoutes.length} geplante Route${selectedRoutes.length !== 1 ? 'n' : ''} zu einer Reise hinzufügen:</p>
                <ul style="max-height: 150px; overflow-y: auto; margin: 10px 0;">
                    ${Array.from(selectedRoutes).map(cb => `<li>${cb.dataset.routeName}</li>`).join('')}
                </ul>
                <div id="tripsListBulkPlannedRoutes">
                    <p>Lade Reisen...</p>
                </div>
                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="button button-secondary" onclick="this.closest('.modal').remove()">Abbrechen</button>
                    <button type="button" class="button button-primary" id="confirmBulkAddPlannedRoutesToTrip">Hinzufügen</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Modal schließen
        modal.querySelector('span').addEventListener('click', function() {
            modal.remove();
        });

        // Reisen laden
        loadTripsForBulkAddPlannedRoutes();

        // Hinzufügen bestätigen
        document.getElementById('confirmBulkAddPlannedRoutesToTrip').addEventListener('click', function() {
            const selectedTrip = modal.querySelector('input[name="tripId"]:checked');
            if (!selectedTrip) {
                alert('Bitte wählen Sie eine Reise aus.');
                return;
            }

            const tripId = selectedTrip.value;
            const routeIds = Array.from(selectedRoutes).map(cb => cb.value);

            // Bulk-Add durchführen
            bulkAddPlannedRoutesToTrip(tripId, routeIds);
            modal.remove();
        });
    }

    function loadTripsForBulkAddPlannedRoutes() {
        const tripsList = document.getElementById('tripsListBulkPlannedRoutes');

        fetch('/user/api/trips')
            .then(response => response.json())
            .then(trips => {
                if (trips.length === 0) {
                    tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                    return;
                }

                let html = '<div class="trips-selection">';
                trips.forEach(trip => {
                    html += `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <label style="cursor: pointer;">
                                <input type="radio" name="tripId" value="${trip.id}" style="margin-right: 8px;">
                                <strong>${trip.title}</strong>
                                ${trip.start_date || trip.end_date ?
                                    `<br><small style="color: #666;">${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                    : ''}
                            </label>
                        </div>
                    `;
                });
                html += '</div>';
                tripsList.innerHTML = html;
            })
            .catch(error => {
                console.error('Fehler beim Laden der Reisen:', error);
                tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
            });
    }

    function bulkAddPlannedRoutesToTrip(tripId, routeIds) {
        // Zeige Loading-Indikator
        const loadingDiv = document.createElement('div');
        loadingDiv.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 10000;';
        loadingDiv.innerHTML = '<p>Füge geplante Routen zur Reise hinzu...</p>';
        document.body.appendChild(loadingDiv);

        // Sequenziell hinzufügen (um Server nicht zu überlasten)
        let completed = 0;

        routeIds.forEach((routeId, index) => {
            setTimeout(() => {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user/trip/${tripId}/add-planned-route`;
                form.style.display = 'none';

                const routeInput = document.createElement('input');
                routeInput.type = 'hidden';
                routeInput.name = 'plannedRouteId';
                routeInput.value = routeId;
                form.appendChild(routeInput);

                document.body.appendChild(form);

                completed++;
                if (completed === routeIds.length) {
                    // Nach dem letzten Submit zur Reise-Detailseite weiterleiten
                    setTimeout(() => {
                        window.location.href = `/user/trip/${tripId}`;
                    }, 500);
                }

                form.submit();
            }, index * 200); // 200ms Verzögerung zwischen Requests
        });
    }

    // Bulk-Teilen Modal öffnen
    bulkShareBtn.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.route-checkbox:checked');

        // Liste der ausgewählten Routen anzeigen
        const routesList = Array.from(selectedCheckboxes).map(cb =>
            `<div>• ${cb.dataset.routeName}</div>`
        ).join('');
        bulkShareRoutesList.innerHTML = `<strong>Ausgewählte Routen (${selectedCheckboxes.length}):</strong><br>${routesList}`;

        // Hidden Inputs für Route IDs hinzufügen
        const existingInputs = bulkShareForm.querySelectorAll('input[name="routeIds"]');
        existingInputs.forEach(input => input.remove());

        selectedCheckboxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'routeIds';
            input.value = checkbox.value;
            bulkShareForm.appendChild(input);
        });

        // Freundesliste laden
        fetch('/user/api/friends')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message || 'API returned success: false');
                }

                const friends = data.friends || [];

                if (friends.length === 0) {
                    bulkShareFriendsList.innerHTML = '<p>Du hast noch keine Freunde hinzugefügt.</p>';
                } else {
                    const friendsHtml = friends.map(friend => `
                        <div style="margin-bottom: 10px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="checkbox" name="friendIds" value="${friend.friend_user_id}" style="margin-right: 10px;">
                                <span>${friend.friend_username}</span>
                            </label>
                        </div>
                    `).join('');
                    bulkShareFriendsList.innerHTML = friendsHtml;
                }
            })
            .catch(error => {
                console.error('Fehler beim Laden der Freundesliste:', error);
                bulkShareFriendsList.innerHTML = `<p>Fehler beim Laden der Freundesliste: ${error.message}</p>`;
            });

        bulkShareModal.style.display = 'block';
    });

    // Modal schließen
    function closeBulkShareModal() {
        bulkShareModal.style.display = 'none';
    }

    closeBtn.addEventListener('click', closeBulkShareModal);
    cancelBulkShareBtn.addEventListener('click', closeBulkShareModal);

    // Modal schließen, wenn außerhalb geklickt wird
    window.addEventListener('click', function(event) {
        if (event.target === bulkShareModal) {
            closeBulkShareModal();
        }
    });
});
</script>

<%# Seitenspezifische Skripte (optional) %>
<%# contentFor('pageScripts') %>
    <%# <script> console.log("Meine geplanten Aktivitäten Seite geladen."); </script> %>
<%# _%>
