<%# views/users/activities.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = 'Meine Aktivitäten' %>
<% pageSpecificClass = 'user-my-activities-page' %>

<%# Seitenspezifische Stylesheets (optional, falls user_layout.css nicht alles abdeckt) %>
<%# contentFor('pageStylesheets') %>
    <%# <link rel="stylesheet" href="/css/my_activities_specific.css"> %>
<%# _%>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">

    <%# --- Filter Formular --- %>
    <form class="filter-form" method="GET" action="/user/activities">
        <div class="filter-grid">
            <div class="form-group">
                <label for="search">Name/ID suchen:</label>
                <input type="text" id="search" name="search" value="<%= locals.filters && filters.search ? filters.search : '' %>" placeholder="Aktivitätsname oder Strava ID">
            </div>
            <div class="form-group">
                <label for="type">Sportart:</label>
                <select id="type" name="type">
                    <option value="">-- Alle Sportarten --</option>
                    <% if (locals.activityTypesForFilter && activityTypesForFilter.length > 0) { %>
                        <% activityTypesForFilter.forEach(actType => { %>
                            <option value="<%= actType %>" <%= locals.filters && filters.activityType === actType ? 'selected' : '' %>><%= actType %></option>
                        <% }); %>
                    <% } %>
                </select>
            </div>
            <div class="form-group">
                <label for="date_from">Datum von:</label>
                <input type="date" id="date_from" name="date_from" value="<%= locals.filters && filters.dateFrom ? filters.dateFrom : '' %>">
            </div>
            <div class="form-group">
                <label for="date_to">Datum bis:</label>
                <input type="date" id="date_to" name="date_to" value="<%= locals.filters && filters.dateTo ? filters.dateTo : '' %>">
            </div>
            <div class="form-group">
                <label for="participation">Teilnahme:</label>
                <select id="participation" name="participation">
                    <option value="all" <%= locals.filters && filters.participation === 'all' ? 'selected' : '' %>>Alle meine relevanten</option>
                    <option value="owned" <%= locals.filters && filters.participation === 'owned' ? 'selected' : '' %>>Nur meine Eigenen</option>
                    <option value="shared_with_me" <%= locals.filters && filters.participation === 'shared_with_me' ? 'selected' : '' %>>Mit mir Geteilte</option>
                </select>
            </div>
            <div class="form-group">
                <label for="dist_min">Distanz Min (km):</label>
                <input type="number" id="dist_min" name="dist_min" value="<%= locals.filters && filters.distMin ? (filters.distMin / 1000) : '' %>" step="0.1" placeholder="z.B. 5">
            </div>
            <div class="form-group">
                <label for="dist_max">Distanz Max (km):</label>
                <input type="number" id="dist_max" name="dist_max" value="<%= locals.filters && filters.distMax ? (filters.distMax / 1000) : '' %>" step="0.1" placeholder="z.B. 100">
            </div>
            <div class="form-group">
                <label for="elev_min">Höhe Min (m):</label>
                <input type="number" id="elev_min" name="elev_min" value="<%= locals.filters && filters.elevMin ? filters.elevMin : '' %>" step="50" placeholder="z.B. 100">
            </div>
            <div class="form-group">
                <label for="elev_max">Höhe Max (m):</label>
                <input type="number" id="elev_max" name="elev_max" value="<%= locals.filters && filters.elevMax ? filters.elevMax : '' %>" step="50" placeholder="z.B. 1000">
            </div>
        </div>
        <div class="actions" style="grid-column: 1 / -1; margin-top: 15px;">
            <%# Versteckte Felder für aktuelle Sortierung, damit sie beim Filtern erhalten bleibt %>
            <input type="hidden" name="sort" value="<%= locals.sort && sort.by ? sort.by : 'date' %>">
            <input type="hidden" name="order" value="<%= locals.sort && sort.order ? sort.order : 'DESC' %>">
            <button type="submit" class="button">Filter anwenden</button>
            <a href="/user/activities" class="button button-secondary" style="margin-left: 10px;">Filter zurücksetzen</a>
        </div>
    </form>

    <% if (locals.pagination && pagination.totalActivities !== undefined) { %>
        <div class="total-count" style="margin-top: 20px; margin-bottom: 10px; font-weight: bold;">
            <%= pagination.totalActivities %> Aktivität<%= pagination.totalActivities !== 1 ? 'en' : '' %> gefunden.
        </div>
    <% } %>

    <!-- Bulk-Aktionen für Reise-Verknüpfung -->
    <div class="bulk-actions" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; display: none;">
        <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
            <span id="selectedCount" style="font-weight: bold;">0 Aktivitäten ausgewählt</span>
            <button type="button" id="addSelectedToTripBtn" class="button button-primary" disabled>Ausgewählte zu Reise hinzufügen</button>
            <button type="button" id="clearSelectionBtn" class="button button-secondary">Auswahl aufheben</button>
        </div>
    </div>

    <% if (locals.activities && activities.length > 0) { %>
        <%
            // Hilfsfunktion, um Query-Parameter für Sortierlinks zu erstellen
            // Behält alle aktuellen Filter bei, außer Paginierung
            const createSortParams = (currentFilters, currentSortBy, currentSortOrder, newSortBy) => {
                const params = new URLSearchParams();
                if (currentFilters) {
                    if (currentFilters.search) params.set('search', currentFilters.search);
                    if (currentFilters.type) params.set('type', currentFilters.type);
                    if (currentFilters.date_from) params.set('date_from', currentFilters.date_from);
                    if (currentFilters.date_to) params.set('date_to', currentFilters.date_to);
                    if (currentFilters.participation) params.set('participation', currentFilters.participation);
                    if (currentFilters.dist_min) params.set('dist_min', currentFilters.dist_min); // Controller erwartet Meter
                    if (currentFilters.dist_max) params.set('dist_max', currentFilters.dist_max);
                    if (currentFilters.elev_min) params.set('elev_min', currentFilters.elev_min);
                    if (currentFilters.elev_max) params.set('elev_max', currentFilters.elev_max);
                }
                params.set('sort', newSortBy);
                params.set('order', (currentSortBy === newSortBy && currentSortOrder === 'ASC') ? 'DESC' : 'ASC');
                return params.toString();
            };
        %>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 40px;">
                        <input type="checkbox" id="selectAllActivities" title="Alle auswählen/abwählen">
                    </th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'name') %>" class="<%= sort.by === 'name' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Name</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'date') %>" class="<%= sort.by === 'date' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Datum</a></th>
                    <th><a href="?<%= createSortParams(filters, sort.by, sort.order, 'type') %>" class="<%= sort.by === 'type' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Typ</a></th>
                    <th class="number"><a href="?<%= createSortParams(filters, sort.by, sort.order, 'distance') %>" class="<%= sort.by === 'distance' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Distanz</a></th>
                    <th class="number"><a href="?<%= createSortParams(filters, sort.by, sort.order, 'elevation') %>" class="<%= sort.by === 'elevation' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Höhe</a></th>
                    <th class="number"><a href="?<%= createSortParams(filters, sort.by, sort.order, 'photos') %>" class="<%= sort.by === 'photos' ? (sort.order === 'ASC' ? 'sort-asc' : 'sort-desc') : '' %>">Fotos</a></th>
                    <th>Aktionen</th>
                </tr>
            </thead>
            <tbody>
                <% activities.forEach(activity => { %>
                    <tr class="owned-<%= activity.is_owned_by_user %>">
                        <td>
                            <% if (activity.is_owned_by_user) { %>
                                <input type="checkbox" class="activity-checkbox" value="<%= activity.id %>" data-activity-name="<%= activity.activity_name || '(Unbenannt)' %>">
                            <% } %>
                        </td>
                        <td>
                            <a href="/user/activity/<%= activity.id %>" title="Details für '<%= activity.activity_name || '(Unbenannt)' %>'"><%= activity.activity_name || '(Unbenannt)' %></a>
                            <% if (!activity.is_owned_by_user) { %>
                                <em style="font-size:0.8em; color:#666;">(Geteilt)</em>
                            <% } %>
                        </td>
                        <td><%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : '?' %></td>
                        <td><%= activity.sport_type %></td>
                        <td class="number"><%= activity.distance_m != null ? (activity.distance_m / 1000).toFixed(1).replace('.',',') + ' km' : '-' %></td>
                        <td class="number"><%= activity.elevation_gain_m != null ? Math.round(activity.elevation_gain_m) + ' m' : '-' %></td>
                        <td class="number"><%= activity.photo_count_from_db %></td>
                        <td class="actions">
                            <a href="/user/activity/<%= activity.id %>" title="Details ansehen">Details</a>
                            <% if (activity.strava_id) { %>
                                <a href="/show/activity_pi_control/<%= activity.id %>" class="button-link-small" target="_blank" title="3D Animation">3D</a>
                            <% } %>
                            <% if (activity.gpx_status == 1) { %>
                                <a href="/user/activity/<%= activity.id %>" class="button-link-small" target="_blank" title="Aktivitätsdetails ansehen">Details</a>
                            <% } %>
                            <% if (activity.is_owned_by_user) { %>
                                <form action="/user/activities/delete/<%= activity.id %>" method="POST" style="display: inline;" onsubmit="return confirm('Aktivität \'<%= activity.activity_name || activity.strava_id %>\' wirklich löschen?');">
                                    <button type="submit" class="button-delete button-link-small">Löschen</button>
                                </form>
                            <% } %>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>

        <%# Mobile Card Layout (wird nur auf mobilen Geräten angezeigt) %>
        <div class="mobile-cards-container">
            <% activities.forEach(activity => { %>
                <div class="mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>">
                    <div class="mobile-card-header">
                        <a href="/user/activity/<%= activity.id %>" class="mobile-card-title" title="Details für '<%= activity.activity_name || '(Unbenannt)' %>'">
                            <%= activity.activity_name || '(Unbenannt)' %>
                        </a>
                        <div class="mobile-card-date">
                            <%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : '?' %>
                        </div>
                    </div>

                    <% if (!activity.is_owned_by_user) { %>
                        <div class="mobile-card-indicator shared">Geteilt von anderem Nutzer</div>
                    <% } %>

                    <div class="mobile-card-meta">
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Sportart</div>
                            <div class="mobile-card-meta-value"><%= activity.sport_type %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Distanz</div>
                            <div class="mobile-card-meta-value"><%= activity.distance_m != null ? (activity.distance_m / 1000).toFixed(1).replace('.',',') + ' km' : '-' %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Höhenmeter</div>
                            <div class="mobile-card-meta-value"><%= activity.elevation_gain_m != null ? Math.round(activity.elevation_gain_m) + ' m' : '-' %></div>
                        </div>
                        <div class="mobile-card-meta-item">
                            <div class="mobile-card-meta-label">Fotos</div>
                            <div class="mobile-card-meta-value"><%= activity.photo_count_from_db %></div>
                        </div>
                    </div>

                    <div class="mobile-card-actions">
                        <a href="/user/activity/<%= activity.id %>" class="button-primary" title="Details ansehen">Details</a>
                        <% if (activity.strava_id) { %>
                            <a href="/show/activity_pi_control/<%= activity.id %>" target="_blank" title="3D Animation">3D</a>
                        <% } %>
                        <% if (activity.is_owned_by_user) { %>
                            <form action="/user/activities/delete/<%= activity.id %>" method="POST" style="display: inline;" onsubmit="return confirm('Aktivität \'<%= activity.activity_name || activity.strava_id %>\' wirklich löschen?');">
                                <button type="submit" class="button-delete">Löschen</button>
                            </form>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        </div>

        <%# Paginierung %>
        <% if (locals.pagination && pagination.totalPages > 1) { %>
            <div class="pagination">
                <%
                    const createPageParams = (currentFilters, currentSort, currentOrder, pageNum) => {
                        const params = new URLSearchParams();
                        if (currentFilters) {
                            if (currentFilters.search) params.set('search', currentFilters.search);
                            if (currentFilters.type) params.set('type', currentFilters.type);
                            if (currentFilters.date_from) params.set('date_from', currentFilters.date_from);
                            if (currentFilters.date_to) params.set('date_to', currentFilters.date_to);
                            if (currentFilters.participation) params.set('participation', currentFilters.participation);
                            if (currentFilters.dist_min) params.set('dist_min', currentFilters.dist_min);
                            if (currentFilters.dist_max) params.set('dist_max', currentFilters.dist_max);
                            if (currentFilters.elev_min) params.set('elev_min', currentFilters.elev_min);
                            if (currentFilters.elev_max) params.set('elev_max', currentFilters.elev_max);
                        }
                        if (currentSort && currentSort.by) params.set('sort', currentSort.by);
                        if (currentSort && currentSort.order) params.set('order', currentSort.order);
                        if (pageNum) params.set('page', pageNum);
                        return params.toString();
                    };
                %>
                <% if (pagination.currentPage > 1) { %>
                    <a href="?<%= createPageParams(filters, sort, sort.order, pagination.currentPage - 1) %>">« Zurück</a>
                <% } else { %>
                    <span class="disabled">« Zurück</span>
                <% } %>

                <%# Vereinfachte Seitenzahlen-Anzeige %>
                <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                    <% if (i === pagination.currentPage) { %>
                        <span class="current"><%= i %></span>
                    <% } else { %>
                        <a href="?<%= createPageParams(filters, sort, sort.order, i) %>"><%= i %></a>
                    <% } %>
                <% } %>

                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <a href="?<%= createPageParams(filters, sort, sort.order, pagination.currentPage + 1) %>">Weiter »</a>
                <% } else { %>
                    <span class="disabled">Weiter »</span>
                <% } %>
            </div>
        <% } %>

    <% } else if (locals.activities) { %>
        <p class="no-activities" style="margin-top:20px; text-align:center;">Keine Aktivitäten gefunden, die den Filterkriterien entsprechen.</p>
    <% } %>
</div>

<%# Seitenspezifische Skripte %>
<% contentFor('pageScripts') %>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllActivities');
    const activityCheckboxes = document.querySelectorAll('.activity-checkbox');
    const bulkActions = document.querySelector('.bulk-actions');
    const selectedCount = document.getElementById('selectedCount');
    const addSelectedToTripBtn = document.getElementById('addSelectedToTripBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    // Alle auswählen/abwählen
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            activityCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Einzelne Checkboxen
    activityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();

            // "Alle auswählen" Checkbox aktualisieren
            const checkedCount = document.querySelectorAll('.activity-checkbox:checked').length;
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === activityCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < activityCheckboxes.length;
            }
        });
    });

    // Auswahl aufheben
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function() {
            activityCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
            updateBulkActions();
        });
    }

    // Zu Reise hinzufügen
    if (addSelectedToTripBtn) {
        addSelectedToTripBtn.addEventListener('click', function() {
            const selectedActivities = Array.from(document.querySelectorAll('.activity-checkbox:checked'));
            if (selectedActivities.length === 0) {
                alert('Bitte wählen Sie mindestens eine Aktivität aus.');
                return;
            }

            showBulkAddToTripModal(selectedActivities);
        });
    }

    function updateBulkActions() {
        const checkedCount = document.querySelectorAll('.activity-checkbox:checked').length;

        if (checkedCount > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${checkedCount} Aktivität${checkedCount !== 1 ? 'en' : ''} ausgewählt`;
            addSelectedToTripBtn.disabled = false;
        } else {
            bulkActions.style.display = 'none';
            addSelectedToTripBtn.disabled = true;
        }
    }

    function showBulkAddToTripModal(selectedActivities) {
        // Erstelle Modal dynamisch
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>Aktivitäten zu Reise hinzufügen</h3>
                <p>Folgende ${selectedActivities.length} Aktivität${selectedActivities.length !== 1 ? 'en' : ''} zu einer Reise hinzufügen:</p>
                <ul style="max-height: 150px; overflow-y: auto; margin: 10px 0;">
                    ${selectedActivities.map(cb => `<li>${cb.dataset.activityName}</li>`).join('')}
                </ul>
                <div id="tripsListBulk">
                    <p>Lade Reisen...</p>
                </div>
                <div class="form-actions">
                    <button type="button" class="button button-secondary" onclick="this.closest('.modal').remove()">Abbrechen</button>
                    <button type="button" class="button button-primary" id="confirmBulkAddToTrip">Hinzufügen</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Modal schließen
        modal.querySelector('.close').addEventListener('click', function() {
            modal.remove();
        });

        // Reisen laden
        loadTripsForBulkAdd();

        // Hinzufügen bestätigen
        document.getElementById('confirmBulkAddToTrip').addEventListener('click', function() {
            const selectedTrip = modal.querySelector('input[name="tripId"]:checked');
            if (!selectedTrip) {
                alert('Bitte wählen Sie eine Reise aus.');
                return;
            }

            const tripId = selectedTrip.value;
            const activityIds = selectedActivities.map(cb => cb.value);

            // Bulk-Add durchführen
            bulkAddActivitiesToTrip(tripId, activityIds);
            modal.remove();
        });
    }

    function loadTripsForBulkAdd() {
        const tripsList = document.getElementById('tripsListBulk');

        fetch('/user/api/trips')
            .then(response => response.json())
            .then(trips => {
                if (trips.length === 0) {
                    tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                    return;
                }

                let html = '<div class="trips-selection">';
                trips.forEach(trip => {
                    html += `
                        <div class="trip-item" style="padding: 10px; border-bottom: 1px solid #eee;">
                            <label style="cursor: pointer;">
                                <input type="radio" name="tripId" value="${trip.id}" style="margin-right: 8px;">
                                <strong>${trip.title}</strong>
                                ${trip.start_date || trip.end_date ?
                                    `<br><small style="color: #666;">${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                    : ''}
                            </label>
                        </div>
                    `;
                });
                html += '</div>';
                tripsList.innerHTML = html;
            })
            .catch(error => {
                console.error('Fehler beim Laden der Reisen:', error);
                tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
            });
    }

    function bulkAddActivitiesToTrip(tripId, activityIds) {
        // Zeige Loading-Indikator
        const loadingDiv = document.createElement('div');
        loadingDiv.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 10000;';
        loadingDiv.innerHTML = '<p>Füge Aktivitäten zur Reise hinzu...</p>';
        document.body.appendChild(loadingDiv);

        // Sequenziell hinzufügen (um Server nicht zu überlasten)
        let completed = 0;
        let errors = 0;

        activityIds.forEach((activityId, index) => {
            setTimeout(() => {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user/trip/${tripId}/add-activity`;
                form.style.display = 'none';

                const activityInput = document.createElement('input');
                activityInput.type = 'hidden';
                activityInput.name = 'activityId';
                activityInput.value = activityId;
                form.appendChild(activityInput);

                document.body.appendChild(form);

                // Simuliere Submit (da wir keine AJAX-Response verarbeiten können)
                completed++;
                if (completed === activityIds.length) {
                    loadingDiv.remove();
                    // Seite neu laden um Änderungen zu sehen
                    window.location.href = `/user/trip/${tripId}`;
                }

                form.submit();
            }, index * 200); // 200ms Verzögerung zwischen Requests
        });
    }
});
</script>
<%_ %>
