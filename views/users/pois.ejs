<%# views/users/pois.ejs %>
<%# Layout wird global in server.ts auf 'layouts/main_layout' gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = locals.pageTitle || 'Meine POIs' %>
<% pageSpecificClass = 'user-pois-page' %>

<%# Seitenspezifische Stylesheets %>
<% contentFor('pageStylesheets') %>
    <link rel="stylesheet" href="/css/user-activities.css">
    <style>
        .pois-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .pois-filters {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
        }

        .poi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .poi-card {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: box-shadow 0.2s ease;
        }

        .poi-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .poi-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .poi-title {
            font-size: 18px;
            font-weight: 600;
            color: #212529;
            margin: 0 0 5px 0;
        }

        .poi-type {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .poi-description {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .poi-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .poi-coordinates {
            font-family: monospace;
            font-size: 12px;
        }

        .poi-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            padding: 10px 0;
            border-top: 1px solid #e9ecef;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #6c757d;
        }

        .stat-item i {
            color: #007bff;
            font-size: 16px;
        }

        .stat-value {
            font-weight: 600;
            color: #495057;
        }

        .stat-label {
            font-size: 13px;
        }

        .poi-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .add-poi-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }

        .add-poi-btn:hover {
            background-color: #218838;
            color: white;
        }

        @media (max-width: 768px) {
            .pois-header {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-row {
                flex-direction: column;
                gap: 10px;
            }

            .filter-group input,
            .filter-group select {
                min-width: 100%;
            }

            .poi-grid {
                grid-template-columns: 1fr;
            }

            .poi-actions {
                justify-content: center;
            }

            .poi-stats {
                justify-content: center;
                gap: 10px;
            }

            .stat-item {
                font-size: 13px;
            }
        }
    </style>
<%_ %>

<div class="pois-header">
    <h2 class="page-main-title">Meine POIs</h2>
    <a href="/user/poi/add" class="add-poi-btn">
        <i class="fas fa-plus"></i> Neuen POI hinzufügen
    </a>
</div>

<!-- Filter-Bereich -->
<div class="pois-filters">
    <form method="GET" action="/user/pois">
        <div class="filter-row">
            <div class="filter-group">
                <label for="search">Suche</label>
                <input type="text" id="search" name="search" value="<%= filters.search %>" placeholder="POI-Name oder Beschreibung...">
            </div>
            <div class="filter-group">
                <label for="type">POI-Typ</label>
                <select id="type" name="type">
                    <option value="">Alle Typen</option>
                    <% poiTypesForFilter.forEach(function(type) { %>
                        <option value="<%= type %>" <%= filters.type === type ? 'selected' : '' %>><%= type %></option>
                    <% }); %>
                </select>
            </div>
            <div class="filter-group">
                <label for="sort">Sortierung</label>
                <select id="sort" name="sort">
                    <option value="created_at" <%= sort.by === 'created_at' ? 'selected' : '' %>>Erstellungsdatum</option>
                    <option value="title" <%= sort.by === 'title' ? 'selected' : '' %>>Name</option>
                    <option value="poi_type" <%= sort.by === 'poi_type' ? 'selected' : '' %>>Typ</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="order">Reihenfolge</label>
                <select id="order" name="order">
                    <option value="DESC" <%= sort.order === 'DESC' ? 'selected' : '' %>>Absteigend</option>
                    <option value="ASC" <%= sort.order === 'ASC' ? 'selected' : '' %>>Aufsteigend</option>
                </select>
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn-small btn-primary">Filtern</button>
            </div>
        </div>
    </form>
</div>

<!-- Bulk-Aktionen für Reise-Verknüpfung -->
<% if (pois && pois.length > 0) { %>
<div class="bulk-actions" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; display: none;">
    <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
        <span id="selectedPoisCount" style="font-weight: bold;">0 POIs ausgewählt</span>
        <button type="button" id="addSelectedPoisToTripBtn" class="btn-small btn-primary" disabled>Ausgewählte zu Reise hinzufügen</button>
        <button type="button" id="clearPoisSelectionBtn" class="btn-small btn-secondary">Auswahl aufheben</button>
    </div>
</div>
<% } %>

<!-- POI-Liste -->
<% if (pois && pois.length > 0) { %>
    <div class="poi-grid">
        <% pois.forEach(function(poi) { %>
            <div class="poi-card">
                <div class="poi-header-info">
                    <div style="display: flex; align-items: flex-start; gap: 10px;">
                        <input type="checkbox" class="poi-checkbox" value="<%= poi.id %>" data-poi-name="<%= poi.title %>" style="margin-top: 5px;">
                        <div>
                        <h3 class="poi-title"><%= poi.title %></h3>
                        <span class="poi-type">
                            <span class="poi-emoji" style="margin-right: 5px;"><%-
                                (function(type) {
                                    const emojiMap = {
                                        'restaurant': '🍽️', 'cafe_bistro': '☕', 'bar_kneipe': '🍺', 'schnellimbiss': '🍔', 'essen_sonstiges': '🍽️',
                                        'lebensmittel': '🛒', 'shopping_center': '🏬', 'fachgeschaeft': '🏪',
                                        'oeffentlicher_nahverkehr': '🚌', 'autofahrer': '⛽', 'fahrrad': '🚲', 'verkehr_sonstiges': '🚗',
                                        'sehenswuerdigkeit': '🏛️', 'kultur_unterhaltung': '🎭', 'natur_erholung': '🌳', 'sport_aktivitaeten': '⚽',
                                        'notfall': '🚨', 'arzt_praxis': '🏥', 'apotheke': '💊', 'therapie_wellness': '💆',
                                        'hotel': '🏨', 'alternative_unterkunft': '🏠', 'camping_wohnmobil': '🏕️',
                                        'finanzen': '🏦', 'oeffentliche_verwaltung': '🏢', 'bildung': '🎓', 'sonstige_dienstleistungen': '🔧'
                                    };
                                    return emojiMap[type] || '📍';
                                })(poi.poi_type)
                            %></span>
                            <%-
                                (function(type) {
                                    const typeMap = {
                                        'restaurant': 'Restaurant', 'cafe_bistro': 'Café & Bistro', 'bar_kneipe': 'Bar & Kneipe',
                                        'schnellimbiss': 'Schnellimbiss & To-Go', 'essen_sonstiges': 'Essen & Trinken (Sonstiges)',
                                        'lebensmittel': 'Lebensmittel', 'shopping_center': 'Shopping-Center & Kaufhäuser', 'fachgeschaeft': 'Fachgeschäft',
                                        'oeffentlicher_nahverkehr': 'Öffentlicher Nahverkehr', 'autofahrer': 'Für Autofahrer', 'fahrrad': 'Fahrrad & Co.',
                                        'verkehr_sonstiges': 'Verkehr & Mobilität (Sonstiges)', 'sehenswuerdigkeit': 'Sehenswürdigkeit',
                                        'kultur_unterhaltung': 'Kultur & Unterhaltung', 'natur_erholung': 'Natur & Erholung',
                                        'sport_aktivitaeten': 'Sport & Aktivitäten', 'notfall': 'Notfall', 'arzt_praxis': 'Arzt & Praxis',
                                        'apotheke': 'Apotheke', 'therapie_wellness': 'Therapie & Wellness', 'hotel': 'Hotel',
                                        'alternative_unterkunft': 'Alternative Unterkunft', 'camping_wohnmobil': 'Camping & Wohnmobil',
                                        'finanzen': 'Finanzen', 'oeffentliche_verwaltung': 'Öffentliche Verwaltung', 'bildung': 'Bildung',
                                        'sonstige_dienstleistungen': 'Sonstige Dienstleistungen'
                                    };
                                    return typeMap[type] || type;
                                })(poi.poi_type)
                            %>
                        </span>
                        </div>
                    </div>
                </div>

                <% if (poi.description) { %>
                    <div class="poi-description">
                        <%= poi.description.length > 100 ? poi.description.substring(0, 100) + '...' : poi.description %>
                    </div>
                <% } %>

                <div class="poi-meta">
                    <div class="poi-coordinates">
                        <%= parseFloat(poi.latitude).toFixed(6) %>, <%= parseFloat(poi.longitude).toFixed(6) %>
                    </div>
                    <div class="poi-date">
                        <%= new Date(poi.created_at).toLocaleDateString('de-DE') %>
                    </div>
                </div>

                <!-- Zusätzliche Metadaten -->
                <div class="poi-stats">
                    <div class="stat-item">
                        <i class="fas fa-images"></i>
                        <span class="stat-value"><%= poi.imageCount || 0 %></span>
                        <span class="stat-label">Bild<%= (poi.imageCount || 0) === 1 ? '' : 'er' %></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-link"></i>
                        <span class="stat-value"><%= poi.urlCount || 0 %></span>
                        <span class="stat-label">Link<%= (poi.urlCount || 0) === 1 ? '' : 's' %></span>
                    </div>
                </div>

                <div class="poi-actions">
                    <a href="/user/poi/<%= poi.id %>" class="btn-small btn-primary">Details</a>
                    <a href="/user/poi/<%= poi.id %>/edit" class="btn-small btn-secondary">Bearbeiten</a>
                    <form action="/user/poi/<%= poi.id %>/delete" method="POST" style="display: inline;"
                          onsubmit="return confirm('POI \'<%= poi.title %>\' wirklich löschen?');">
                        <button type="submit" class="btn-small btn-danger">Löschen</button>
                    </form>
                </div>
            </div>
        <% }); %>
    </div>
<% } else { %>
    <div class="empty-state">
        <h3>Noch keine POIs vorhanden</h3>
        <p>Sie haben noch keine POIs erstellt. Fügen Sie Ihren ersten POI hinzu, um interessante Orte zu markieren.</p>
        <a href="/user/poi/add" class="add-poi-btn">Ersten POI hinzufügen</a>
    </div>
<% } %>

<% contentFor('pageScripts') %>
    <script>
        // Auto-Submit bei Änderung der Filter
        document.addEventListener('DOMContentLoaded', function() {
            const filterForm = document.querySelector('.pois-filters form');
            const filterInputs = filterForm.querySelectorAll('select, input');

            filterInputs.forEach(input => {
                if (input.type !== 'text') { // Nicht bei Textfeldern
                    input.addEventListener('change', function() {
                        filterForm.submit();
                    });
                }
            });

            // POI Bulk-Aktionen
            const poiCheckboxes = document.querySelectorAll('.poi-checkbox');
            const bulkActions = document.querySelector('.bulk-actions');
            const selectedPoisCount = document.getElementById('selectedPoisCount');
            const addSelectedPoisToTripBtn = document.getElementById('addSelectedPoisToTripBtn');
            const clearPoisSelectionBtn = document.getElementById('clearPoisSelectionBtn');

            // Einzelne Checkboxen
            poiCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updatePoiBulkActions();
                });
            });

            // Auswahl aufheben
            if (clearPoisSelectionBtn) {
                clearPoisSelectionBtn.addEventListener('click', function() {
                    poiCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    updatePoiBulkActions();
                });
            }

            // Ausgewählte zu Reise hinzufügen
            if (addSelectedPoisToTripBtn) {
                addSelectedPoisToTripBtn.addEventListener('click', function() {
                    const selectedPois = Array.from(document.querySelectorAll('.poi-checkbox:checked'));
                    if (selectedPois.length === 0) {
                        alert('Bitte wählen Sie mindestens einen POI aus.');
                        return;
                    }

                    showBulkAddPoisToTripModal(selectedPois);
                });
            }

            function updatePoiBulkActions() {
                const checkedCount = document.querySelectorAll('.poi-checkbox:checked').length;

                if (checkedCount > 0) {
                    bulkActions.style.display = 'block';
                    selectedPoisCount.textContent = `${checkedCount} POI${checkedCount !== 1 ? 's' : ''} ausgewählt`;
                    addSelectedPoisToTripBtn.disabled = false;
                } else {
                    bulkActions.style.display = 'none';
                    addSelectedPoisToTripBtn.disabled = true;
                }
            }

            function showBulkAddPoisToTripModal(selectedPois) {
                // Erstelle Modal dynamisch
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.cssText = 'display: block; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);';
                modal.innerHTML = `
                    <div style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; border-radius: 8px; width: 80%; max-width: 500px; position: relative;">
                        <span style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; position: absolute; right: 15px; top: 10px;">&times;</span>
                        <h3>POIs zu Reise hinzufügen</h3>
                        <p>Folgende ${selectedPois.length} POI${selectedPois.length !== 1 ? 's' : ''} zu einer Reise hinzufügen:</p>
                        <ul style="max-height: 150px; overflow-y: auto; margin: 10px 0;">
                            ${selectedPois.map(cb => `<li>${cb.dataset.poiName}</li>`).join('')}
                        </ul>
                        <div id="tripsListBulkPois">
                            <p>Lade Reisen...</p>
                        </div>
                        <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" class="btn-small btn-secondary" onclick="this.closest('.modal').remove()">Abbrechen</button>
                            <button type="button" class="btn-small btn-primary" id="confirmBulkAddPoisToTrip">Hinzufügen</button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Modal schließen
                modal.querySelector('span').addEventListener('click', function() {
                    modal.remove();
                });

                // Reisen laden
                loadTripsForBulkAddPois();

                // Hinzufügen bestätigen
                document.getElementById('confirmBulkAddPoisToTrip').addEventListener('click', function() {
                    const selectedTrip = modal.querySelector('input[name="tripId"]:checked');
                    if (!selectedTrip) {
                        alert('Bitte wählen Sie eine Reise aus.');
                        return;
                    }

                    const tripId = selectedTrip.value;
                    const poiIds = selectedPois.map(cb => cb.value);

                    // Bulk-Add durchführen
                    bulkAddPoisToTrip(tripId, poiIds);
                    modal.remove();
                });
            }

            function loadTripsForBulkAddPois() {
                const tripsList = document.getElementById('tripsListBulkPois');

                fetch('/user/api/trips')
                    .then(response => response.json())
                    .then(trips => {
                        if (trips.length === 0) {
                            tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                            return;
                        }

                        let html = '<div class="trips-selection">';
                        trips.forEach(trip => {
                            html += `
                                <div style="padding: 10px; border-bottom: 1px solid #eee;">
                                    <label style="cursor: pointer;">
                                        <input type="radio" name="tripId" value="${trip.id}" style="margin-right: 8px;">
                                        <strong>${trip.title}</strong>
                                        ${trip.start_date || trip.end_date ?
                                            `<br><small style="color: #666;">${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                            : ''}
                                    </label>
                                </div>
                            `;
                        });
                        html += '</div>';
                        tripsList.innerHTML = html;
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Reisen:', error);
                        tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
                    });
            }

            function bulkAddPoisToTrip(tripId, poiIds) {
                // Zeige Loading-Indikator
                const loadingDiv = document.createElement('div');
                loadingDiv.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 10000;';
                loadingDiv.innerHTML = '<p>Füge POIs zur Reise hinzu...</p>';
                document.body.appendChild(loadingDiv);

                // Sequenziell hinzufügen (um Server nicht zu überlasten)
                let completed = 0;

                poiIds.forEach((poiId, index) => {
                    setTimeout(() => {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/user/trip/${tripId}/add-poi`;
                        form.style.display = 'none';

                        const poiInput = document.createElement('input');
                        poiInput.type = 'hidden';
                        poiInput.name = 'poiId';
                        poiInput.value = poiId;
                        form.appendChild(poiInput);

                        document.body.appendChild(form);

                        completed++;
                        if (completed === poiIds.length) {
                            // Nach dem letzten Submit zur Reise-Detailseite weiterleiten
                            setTimeout(() => {
                                window.location.href = `/user/trip/${tripId}`;
                            }, 500);
                        }

                        form.submit();
                    }, index * 200); // 200ms Verzögerung zwischen Requests
                });
            }
        });
    </script>
<%_ %>
