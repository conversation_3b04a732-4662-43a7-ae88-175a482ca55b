<%# views/users/activity_detail.ejs %>
<%# Das Layout 'layouts/main_layout' wird verwendet. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = (locals.activity ? (activity.activity_name || `ID ${activity.id}`) : 'Unbekannt') %>
<% pageSpecificClass = 'user-activity-detail-page' %>

<%# Seitenspezifische Stylesheets (optional) %>
<%_ contentFor('pageStylesheets') %>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="/css/activity_map_with_charts.css" />
    <style>
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr; /* Standardmäßig einspaltig */
            gap: 25px;
            margin-bottom: 20px;
        }
        @media (min-width: 992px) { /* Zweispaltig auf größeren Bildschirmen */
            .detail-grid {
                grid-template-columns: 2fr 1fr;
            }
        }
        .activity-info dl {
            display: grid;
            grid-template-columns: max-content auto;
            gap: 8px 15px;
        }
        .activity-info dt { font-weight: 600; color: #495057; }
        .activity-info dd { margin-left: 0; }
        .activity-info dd pre {
            white-space: pre-wrap;
            word-break: break-word;
            background-color: #f8f9fa;
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .equipment-list label { display: block; margin-bottom: 5px; font-weight: normal; }
        .equipment-list input[type="checkbox"] { margin-right: 8px; vertical-align: middle; }
        .equipment-notes { margin-top: 3px; margin-left: 25px; margin-bottom: 8px; }
        .equipment-notes textarea { width: 95%; font-size: 0.9em; padding: 4px; border: 1px solid #ccc; border-radius: 3px; }

        .linked-equipment-display ul { list-style: none; padding-left: 0; margin-top: 0; }
        .linked-equipment-display li {
            background-color: #f9f9f9;
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
            font-size: 0.95em;
        }
        .linked-equipment-display li:last-child { border-bottom: none; }
        .linked-equipment-display li strong { color: #333; }
        .linked-equipment-display li small { color: #555; display: block; font-style: italic; margin-top: 2px;}

        .action-links p { margin-bottom: 15px; }
    </style>
<%_ %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">
    <% if (locals.activity) { %>
        <% if (!locals.isPublicView) { %>
        <div class="action-links">
            <p>
                <% if (locals.currentUser && currentUser.username) { %>
                    <a href="/user/<%= encodeURIComponent(currentUser.username) %>/map" class="button-link-small">&laquo; Meine Karte</a> |
                    <a href="/user/activities" class="button-link-small">&laquo; Meine Aktivitäten</a>
                    <a href="/show/activity_pi_control/<%= activity.id %>" class="button-link-small" target="_blank">3D Animation</a>
                <% } %>
                <% if (isOwner) { %>
                     | <a href="/user/activity/<%= activity.id %>/edit" class="button-link-small">Bearbeiten & Ausrüstung</a>
                     | <a href="#" class="button-link-small button-success" id="shareActivityBtn">Mit Freunden teilen</a>
                     | <a href="#" class="button-link-small button-info" id="addToTripBtn">Zu Reise hinzufügen</a>
                     | <a href="#" class="button-link-small button-danger" id="deleteActivityBtn">Aktivität löschen</a>
                <% } %>
            </p>
        </div>
        <% } else { %>
        <div class="action-links">
            <p>
                <em>Öffentliche Ansicht einer Aktivität</em>
                <% if (activity.id) { %>
                    <a href="/show/activity_pi_control/<%= activity.id %>" class="button-link-small" target="_blank">3D Animation</a>
                <% } %>
            </p>
        </div>
        <% } %>

        <section class="content-section activity-map-section">
            <h3>Aktivitätskarte</h3>
            <%- include('../partials/activity_map_with_charts', { activity: activity }) %>
        </section>

        <div class="detail-grid">
            <section class="content-section activity-info">
                <h3>Aktivitätsdaten</h3>
                <dl>
                    <dt>Name:</dt><dd><%= activity.activity_name || '(Unbenannt)' %></dd>
                    <dt>Datum:</dt><dd><%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleString('de-DE', {dateStyle:'full', timeStyle:'short'}) : 'N/A' %></dd>
                    <dt>Sportart:</dt><dd><%= activity.sport_type || 'N/A' %></dd>
                    <dt>Distanz:</dt><dd><%= activity.distance != null ? (activity.distance / 1000).toFixed(2) + ' km' : 'N/A' %></dd>
                    <dt>Höhenmeter:</dt><dd><%= activity.total_elevation_gain != null ? Math.round(activity.total_elevation_gain) + ' m' : 'N/A' %></dd>
                    <dt>Bewegungszeit:</dt><dd>
                        <% if (activity.moving_time != null) {
                            const hours = Math.floor(activity.moving_time / 3600);
                            const minutes = Math.floor((activity.moving_time % 3600) / 60);
                            %><%= hours > 0 ? hours + 'h ' : '' %><%= minutes %>m<%
                        } else { %>N/A<% } %>
                    </dd>
                    <% if (activity.private_note) { %>
                        <dt>Private Notiz:</dt><dd><pre><%= activity.private_note %></pre></dd>
                    <% } %>
                    <% if (activity.strava_id) { %>
                        <dt>Strava ID:</dt>
                        <dd>
                            <a href="https://www.strava.com/activities/<%= activity.strava_id %>" target="_blank"><%= activity.strava_id %></a>
                        </dd>
                    <% } %>
                    <% if (isOwner && activity.share_uuid) { %>
                        <dt>Öffentlicher Link:</dt>
                        <dd>
                            <div class="share-link-container">
                                <input type="text" id="shareLink" value="https://map.mdiehm.org/show/activity/<%=activity.share_uuid%>" readonly class="share-link-input">
                                <button type="button" id="copyShareLinkBtn" class="button-link-small">Kopieren</button>
                            </div>
                            <small class="share-link-note">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>
                        </dd>
                    <% } %>
                </dl>
            </section>

            <section class="content-section equipment-section">
                <h3>Verwendete Ausrüstung</h3>
                <% if (locals.linkedEquipmentDetails && linkedEquipmentDetails.length > 0) { %>
                    <div class="linked-equipment-display">
                        <ul>
                            <% linkedEquipmentDetails.forEach(item => { %>
                                <li>
                                    <strong><%= item.name %></strong> (<%= item.type || 'Allgemein' %>)
                                    <% if (item.notes) { %><small>Notiz: <%= item.notes %></small><% } %>
                                </li>
                            <% }); %>
                        </ul>
                    </div>
                <% } else { %>
                    <p class="no-equipment">Keine Ausrüstung für diese Aktivität ausgewählt.</p>
                <% } %>

                <% if (isOwner) { %>
                    <p style="margin-top: 15px;">
                        <a href="/user/activity/<%= activity.id %>/edit" class="button-link-small">Bearbeiten & Ausrüstung</a>
                    </p>
                <% } %>
            </section>
        </div>
    <% } else { %>
        <div class="message error-message">Aktivitätsdaten konnten nicht geladen werden oder die Aktivität existiert nicht.</div>
    <% } %>
</div>

<%# Modal für das Teilen mit Freunden %>
<div id="shareActivityModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du diese Aktivität teilen möchtest:</p>
        <form id="shareActivityForm" action="/user/activity/<%= activity.id %>/share" method="POST">
            <div id="friendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="button button-success">Teilen</button>
                <button type="button" class="button button-secondary" id="cancelShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<%# Modal für das Löschen der Aktivität %>
<div id="deleteActivityModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität löschen</h3>
        <p>Möchtest du diese Aktivität wirklich löschen?</p>
        <p><strong>Achtung:</strong> Diese Aktion kann nicht rückgängig gemacht werden. Alle Daten, GPX-Dateien und verknüpften Fotos werden unwiderruflich gelöscht.</p>

        <div class="activity-delete-info">
            <p><strong>Aktivität:</strong> <%= activity.activity_name || '(Unbenannt)' %></p>
            <p><strong>Datum:</strong> <%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleString('de-DE', {dateStyle:'full', timeStyle:'short'}) : 'N/A' %></p>
            <p><strong>Sportart:</strong> <%= activity.sport_type || 'N/A' %></p>
            <p><strong>Distanz:</strong> <%= activity.distance != null ? (activity.distance / 1000).toFixed(2) + ' km' : 'N/A' %></p>
        </div>

        <form id="deleteActivityForm" action="/user/activities/delete/<%= activity.id %>" method="POST">
            <div class="form-actions">
                <button type="submit" class="button button-danger">Endgültig löschen</button>
                <button type="button" class="button button-secondary" id="cancelDeleteBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<%# Seitenspezifische Skripte (optional) %>
<%_ contentFor('pageScripts') %>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.2/dist/chartjs-plugin-annotation.min.js"></script>
    <script>
      // Prüfe, ob die Plugins korrekt geladen wurden
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Chart.js loaded:', typeof Chart !== 'undefined');
        console.log('Chart.js version:', Chart.version);
        console.log('Annotation plugin loaded:', typeof ChartAnnotation !== 'undefined' || (Chart && Chart.Annotation));
      });
    </script>
    <script src="/js/activityMapWithCharts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Teilen-Modal
            const shareBtn = document.getElementById('shareActivityBtn');
            const shareModal = document.getElementById('shareActivityModal');
            const shareCloseBtn = shareModal.querySelector('.close');
            const shareCancelBtn = document.getElementById('cancelShareBtn');
            const friendsList = document.getElementById('friendsList');

            // Löschen-Modal
            const deleteBtn = document.getElementById('deleteActivityBtn');
            const deleteModal = document.getElementById('deleteActivityModal');
            const deleteCloseBtn = deleteModal.querySelector('.close');
            const deleteCancelBtn = document.getElementById('cancelDeleteBtn');

            // Teilen-Modal öffnen und Freundesliste laden
            shareBtn.addEventListener('click', function(e) {
                e.preventDefault();
                shareModal.style.display = 'block';

                // Freundesliste laden
                fetch('/user/api/friends')
                    .then(response => response.json())
                    .then(data => {
                        if (data.friends && data.friends.length > 0) {
                            let html = '';
                            data.friends.forEach(friend => {
                                html += `
                                    <div class="friend-item">
                                        <label>
                                            <input type="checkbox" name="friendIds" value="${friend.friend_user_id}">
                                            ${friend.friend_username}
                                        </label>
                                    </div>
                                `;
                            });
                            friendsList.innerHTML = html;
                        } else {
                            friendsList.innerHTML = '<p>Du hast noch keine Freunde, mit denen du diese Aktivität teilen könntest.</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Freundesliste:', error);
                        friendsList.innerHTML = '<p class="error">Fehler beim Laden der Freundesliste.</p>';
                    });
            });

            // Löschen-Modal öffnen
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    deleteModal.style.display = 'block';
                });
            }

            // Share-Link kopieren
            const copyShareLinkBtn = document.getElementById('copyShareLinkBtn');
            const shareLinkInput = document.getElementById('shareLink');

            if (copyShareLinkBtn && shareLinkInput) {
                copyShareLinkBtn.addEventListener('click', function() {
                    shareLinkInput.select();
                    shareLinkInput.setSelectionRange(0, 99999); // Für mobile Geräte

                    try {
                        document.execCommand('copy');
                        copyShareLinkBtn.textContent = 'Kopiert!';
                        copyShareLinkBtn.style.backgroundColor = '#28a745';

                        setTimeout(() => {
                            copyShareLinkBtn.textContent = 'Kopieren';
                            copyShareLinkBtn.style.backgroundColor = '';
                        }, 2000);
                    } catch (err) {
                        console.error('Fehler beim Kopieren:', err);
                        copyShareLinkBtn.textContent = 'Fehler';
                        setTimeout(() => {
                            copyShareLinkBtn.textContent = 'Kopieren';
                        }, 2000);
                    }
                });
            }

            // Teilen-Modal schließen
            shareCloseBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            shareCancelBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            // Löschen-Modal schließen
            deleteCloseBtn.addEventListener('click', function() {
                deleteModal.style.display = 'none';
            });

            deleteCancelBtn.addEventListener('click', function() {
                deleteModal.style.display = 'none';
            });

            // Modals schließen, wenn außerhalb geklickt wird
            window.addEventListener('click', function(event) {
                if (event.target === shareModal) {
                    shareModal.style.display = 'none';
                }
                if (event.target === deleteModal) {
                    deleteModal.style.display = 'none';
                }
            });
        });
    </script>
<%_ %>

<%# Seitenspezifische Styles %>
<%_ contentFor('pageStylesheets') %>
    <style>
        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            display: none; /* Standardmäßig ausgeblendet */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            margin-top: -10px;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .friend-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .friend-item:hover {
            background-color: #f8f9fa;
        }

        .friend-item:last-child {
            border-bottom: none;
        }

        .friend-item label {
            display: block;
            cursor: pointer;
        }

        .friend-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .form-actions {
            margin-top: 15px;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* Löschbutton Styles */
        .button-danger {
            background-color: #dc3545;
            color: white;
        }

        .button-danger:hover {
            background-color: #c82333;
        }

        /* Löschmodal Styles */
        .activity-delete-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }

        .activity-delete-info p {
            margin: 5px 0;
        }

        /* Share-Link Styles */
        .share-link-container {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 5px;
        }

        .share-link-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
        }

        .share-link-note {
            color: #666;
            font-style: italic;
        }
    </style>
<%_ %>

<%# Modal für Reise-Auswahl %>
<% if (!locals.isPublicView && isOwner) { %>
<div id="addToTripModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität zu Reise hinzufügen</h3>
        <form id="addToTripForm" method="POST">
            <div id="tripsList">
                <p>Lade Reisen...</p>
            </div>
            <div class="form-actions">
                <button type="button" id="cancelAddToTripBtn" class="button button-secondary">Abbrechen</button>
                <button type="submit" class="button button-primary">Hinzufügen</button>
            </div>
        </form>
    </div>
</div>
<% } %>

<% contentFor('pageScripts') %>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <% if (!locals.isPublicView && isOwner) { %>
    // Zu Reise hinzufügen Modal
    const addToTripBtn = document.getElementById('addToTripBtn');
    const addToTripModal = document.getElementById('addToTripModal');
    const addToTripForm = document.getElementById('addToTripForm');
    const tripsList = document.getElementById('tripsList');

    if (addToTripBtn && addToTripModal) {
        addToTripBtn.addEventListener('click', function(e) {
            e.preventDefault();
            addToTripModal.style.display = 'block';
            loadUserTrips();
        });

        // Modal schließen
        addToTripModal.querySelector('.close').addEventListener('click', function() {
            addToTripModal.style.display = 'none';
        });

        document.getElementById('cancelAddToTripBtn').addEventListener('click', function() {
            addToTripModal.style.display = 'none';
        });

        // Form submit
        addToTripForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const selectedTrip = document.querySelector('input[name="tripId"]:checked');
            if (!selectedTrip) {
                alert('Bitte wählen Sie eine Reise aus.');
                return;
            }

            const tripId = selectedTrip.value;
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/user/trip/${tripId}/add-activity`;

            const activityInput = document.createElement('input');
            activityInput.type = 'hidden';
            activityInput.name = 'activityId';
            activityInput.value = '<%= activity.id %>';
            form.appendChild(activityInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    function loadUserTrips() {
        tripsList.innerHTML = '<p>Lade Reisen...</p>';

        fetch('/user/api/trips')
            .then(response => response.json())
            .then(trips => {
                if (trips.length === 0) {
                    tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                    return;
                }

                let html = '<div class="trips-selection">';
                trips.forEach(trip => {
                    html += `
                        <div class="trip-item">
                            <label>
                                <input type="radio" name="tripId" value="${trip.id}">
                                <strong>${trip.title}</strong>
                                ${trip.start_date || trip.end_date ?
                                    `<br><small>${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                    : ''}
                            </label>
                        </div>
                    `;
                });
                html += '</div>';
                tripsList.innerHTML = html;
            })
            .catch(error => {
                console.error('Fehler beim Laden der Reisen:', error);
                tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
            });
    }
    <% } %>
});
</script>
<%_ %>
