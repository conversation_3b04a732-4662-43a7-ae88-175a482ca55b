<%# views/partials/activity_map_with_charts.ejs %>
<%# Wiederverwendbares Template für die Anzeige einer Aktivitätskarte mit Diagrammen %>

<%#
  Erwartete Parameter:
  - activity: Die Aktivitätsdaten
  - mapId: Eine eindeutige ID für die Karte (optional, Standard: 'activity-map')
  - chartId: Eine eindeutige ID für das Diagramm (optional, Standard: 'activity-chart')
  - height: Hö<PERSON> der Karte (optional, Standard: '400px')
  - showPhotos: Ob Fotos angezeigt werden sollen (optional, Standard: true)
  - showControls: Ob Steuerelemente angezeigt werden sollen (optional, Standard: true)
%>

<%
  // Standardwerte für Parameter
  const mapId = locals.mapId || 'activity-map';
  const chartId = locals.chartId || 'activity-chart';
  const mapHeight = locals.height || '400px';
  const showPhotos = locals.showPhotos !== undefined ? locals.showPhotos : true;
  const showControls = locals.showControls !== undefined ? locals.showControls : true;
%>

<link rel="stylesheet" href="/css/chart-options.css">
<div class="activity-map-with-charts-container" id="<%= mapId %>-chart-container">
  <% if (showControls) { %>
  <div class="chart-controls">
    <fieldset class="chart-type-selector">
      <legend>Diagrammtyp</legend>
      <label>
        <input type="radio" name="chart-type-<%= mapId %>" value="elevation" checked> <span>Höhe</span>
      </label>
      <label>
        <input type="radio" name="chart-type-<%= mapId %>" value="speed"> <span>Geschwindigkeit</span>
      </label>
      <label>
        <input type="radio" name="chart-type-<%= mapId %>" value="heartrate"> <span>Puls</span>
      </label>
    </fieldset>
    <fieldset class="x-axis-selector">
      <legend>X-Achse</legend>
      <label>
        <input type="radio" name="x-axis-<%= mapId %>" value="distance" checked> <span>Distanz</span>
      </label>
      <label>
        <input type="radio" name="x-axis-<%= mapId %>" value="time"> <span>Zeit</span>
      </label>
    </fieldset>
    <% if (showPhotos) { %>
    <fieldset class="map-controls">
      <legend>Karte</legend>
      <label class="toggle-switch">
        <input type="checkbox" id="photo-toggle-<%= mapId %>" checked>
        <span class="toggle-label">Fotos anzeigen</span>
      </label>
    </fieldset>
    <% } %>
  </div>
  <% } %>

  <div class="map-chart-grid">
    <div id="<%= mapId %>" class="activity-map" style="height: <%= mapHeight %>;"></div>
    <div class="chart-container">
      <canvas id="<%= chartId %>" class="activity-chart"></canvas>
    </div>
  </div>

  <% if (activity && activity.id) { %>
  <script>
    // Daten für die Karte und Diagramme
    window.ACTIVITY_DATA = window.ACTIVITY_DATA || {};
    window.ACTIVITY_DATA['<%= activity.id %>'] = {
      id: <%= activity.id %>,
      strava_id: <%= activity.strava_id || 'null' %>,
      name: "<%= activity.activity_name ? activity.activity_name.replace(/"/g, '\\"') : '' %>",
      sport_type: "<%= activity.sport_type || '' %>",
      start_lat: <%= activity.start_lat || 'null' %>,
      start_lng: <%= activity.start_lng || 'null' %>,
      mapId: "<%= mapId %>",
      chartId: "<%= chartId %>",
      showPhotos: <%= showPhotos %>,
      apiEndpoints: {
        streamData: "/api/geojson/stream_data/<%= activity.id %>",
        trackGeoJson: "/api/geojson/track_geojson/<%= activity.id %>",
        gpxAsGeoJson: "/api/geojson/gpx_as_geojson/<%= activity.id %>",
        photos: "/api/geojson/photos/for_activity/<%= activity.id %>"
      }
    };
  </script>
  <% } %>
</div>
