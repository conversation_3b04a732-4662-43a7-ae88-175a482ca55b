<%# views/public/trip.ejs %>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= pageTitle %></title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/public.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .public-header {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .public-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .public-header h1 {
            margin: 0;
            color: #495057;
            font-size: 1.75rem;
        }
        
        .public-header .subtitle {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .trip-info-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .trip-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .trip-dates {
            color: #6c757d;
            font-weight: 500;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: bold;
            background: #d4edda;
            color: #155724;
        }
        
        .trip-description {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .trip-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .trip-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .trip-section h2 {
            margin-bottom: 1rem;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
        }
        
        .items-grid {
            display: grid;
            gap: 1rem;
        }
        
        .item-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            background: #f8f9fa;
        }
        
        .item-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .item-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.875rem;
            color: #6c757d;
            flex-wrap: wrap;
        }
        
        .item-description {
            margin-top: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state h3 {
            margin-bottom: 0.5rem;
            color: #495057;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-size: 0.875rem;
            border-top: 1px solid #dee2e6;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .trip-meta {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .trip-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .item-meta {
                flex-direction: column;
                gap: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <header class="public-header">
        <div class="container">
            <h1><%= trip.title %></h1>
            <div class="subtitle">Öffentliche Reiseansicht</div>
        </div>
    </header>

    <main class="container">
        <!-- Reise-Informationen -->
        <div class="trip-info-card">
            <div class="trip-meta">
                <% if (trip.start_date || trip.end_date) { %>
                    <div class="trip-dates">
                        📅 
                        <% if (trip.start_date && trip.end_date) { %>
                            <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                        <% } else if (trip.start_date) { %>
                            ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                        <% } else { %>
                            bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                        <% } %>
                    </div>
                <% } %>
                
                <span class="status-badge">🌐 Öffentliche Reise</span>
            </div>
            
            <% if (trip.description) { %>
                <div class="trip-description">
                    <p><%= trip.description %></p>
                </div>
            <% } %>

            <!-- Statistiken -->
            <div class="trip-stats">
                <div class="stat-item">
                    <div class="stat-number"><%= stats.activities_count %></div>
                    <div class="stat-label">Aktivitäten</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.planned_routes_count %></div>
                    <div class="stat-label">Geplante Routen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.pois_count %></div>
                    <div class="stat-label">POIs</div>
                </div>
                <% if (stats.total_distance > 0) { %>
                    <div class="stat-item">
                        <div class="stat-number"><%= stats.total_distance_km %> km</div>
                        <div class="stat-label">Gesamtdistanz</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><%= stats.total_elevation_gain_formatted %> m</div>
                        <div class="stat-label">Höhenmeter</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><%= stats.total_moving_time_formatted %></div>
                        <div class="stat-label">Bewegungszeit</div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Aktivitäten -->
        <% if (activities && activities.length > 0) { %>
            <div class="trip-section">
                <h2>🏃‍♂️ Erledigte Aktivitäten (<%= activities.length %>)</h2>
                <div class="items-grid">
                    <% activities.forEach(activity => { %>
                        <div class="item-card">
                            <div class="item-title"><%= activity.activity_name %></div>
                            <div class="item-meta">
                                <span>🏃‍♂️ <%= activity.sport_type %></span>
                                <span>📅 <%= new Date(activity.start_date_local).toLocaleDateString('de-DE') %></span>
                                <span>📏 <%= (activity.distance / 1000).toFixed(1).replace('.', ',') %> km</span>
                                <span>⛰️ <%= Math.round(activity.total_elevation_gain) %> m</span>
                                <span>⏱️ <%= Math.floor(activity.moving_time / 3600) %>h <%= Math.floor((activity.moving_time % 3600) / 60) %>m</span>
                            </div>
                        </div>
                    <% }); %>
                </div>
            </div>
        <% } %>

        <!-- Geplante Routen -->
        <% if (plannedRoutes && plannedRoutes.length > 0) { %>
            <div class="trip-section">
                <h2>🗺️ Geplante Routen (<%= plannedRoutes.length %>)</h2>
                <div class="items-grid">
                    <% plannedRoutes.forEach(route => { %>
                        <div class="item-card">
                            <div class="item-title"><%= route.name %></div>
                            <div class="item-meta">
                                <span>🏃‍♂️ <%= route.sport_type %></span>
                                <span>📏 <%= (route.distance_m / 1000).toFixed(1).replace('.', ',') %> km</span>
                                <span>⛰️ <%= Math.round(route.elevation_gain_m) %> m</span>
                                <% if (route.duration) { %>
                                    <span>⏱️ <%= Math.floor(route.duration / 3600) %>h <%= Math.floor((route.duration % 3600) / 60) %>m</span>
                                <% } %>
                            </div>
                        </div>
                    <% }); %>
                </div>
            </div>
        <% } %>

        <!-- POIs -->
        <% if (pois && pois.length > 0) { %>
            <div class="trip-section">
                <h2>📍 Points of Interest (<%= pois.length %>)</h2>
                <div class="items-grid">
                    <% pois.forEach(poi => { %>
                        <div class="item-card">
                            <div class="item-title"><%= poi.title %></div>
                            <div class="item-meta">
                                <span>📍 <%= poi.poi_type %></span>
                                <span>🌍 <%= poi.latitude.toFixed(4) %>, <%= poi.longitude.toFixed(4) %></span>
                            </div>
                            <% if (poi.description) { %>
                                <div class="item-description">
                                    <%= poi.description.substring(0, 200) %><%= poi.description.length > 200 ? '...' : '' %>
                                </div>
                            <% } %>
                        </div>
                    <% }); %>
                </div>
            </div>
        <% } %>

        <!-- Leerer Zustand -->
        <% if ((!activities || activities.length === 0) && (!plannedRoutes || plannedRoutes.length === 0) && (!pois || pois.length === 0)) { %>
            <div class="trip-section">
                <div class="empty-state">
                    <h3>Noch keine Inhalte</h3>
                    <p>Diese Reise hat noch keine verknüpften Aktivitäten, geplanten Routen oder POIs.</p>
                </div>
            </div>
        <% } %>
    </main>

    <footer class="footer">
        <div class="container">
            <p>Diese Reise wurde öffentlich geteilt. Erstellt mit Master-Map.</p>
        </div>
    </footer>
</body>
</html>
