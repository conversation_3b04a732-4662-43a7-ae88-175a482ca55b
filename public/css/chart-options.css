/* Styles für die Diagramm-Optionen und das Layout */

/* Layout für die Karte und das Diagramm */
.activity-map-with-charts-container {
  margin-bottom: 20px;
}

.map-chart-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto auto;
  gap: 10px;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .map-chart-grid {
    /*grid-template-columns: 1fr 1fr;*/
    grid-template-rows: auto;
  }
}

.activity-map {
  width: 100%;
  min-height: 300px;
  border-radius: 5px;
  overflow: hidden;
}

.chart-container {
  /*width: 100%;*/
  height: 300px;
  position: relative;
  border-radius: 5px;
  overflow: hidden;
  background-color: #f9f9f9;
}

.disabled-option {
  opacity: 0.5;
  cursor: not-allowed;
}

.chart-controls {
  margin-bottom: 10px;
  padding: 5px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.chart-controls fieldset {
  border: 1px solid #ddd;
  padding: 5px 10px;
  margin-bottom: 5px;
  border-radius: 3px;
}

.chart-controls legend {
  font-size: 14px;
  font-weight: bold;
  padding: 0 5px;
}

.chart-controls label {
  margin-right: 10px;
  font-size: 13px;
}

.chart-controls input[type="radio"] {
  margin-right: 3px;
}

.chart-controls input[type="radio"]:disabled + span {
  color: #999;
}

/* Styles für den Positionsmarker und die Informationsanzeige */
.map-marker {
  z-index: 1000;
}

.position-info {
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  line-height: 1.4;
  max-width: 200px;
  z-index: 1000;
}

.position-info-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.position-info-value {
  margin-bottom: 3px;
}

/* Styles für den Foto-Toggle-Button */
.toggle-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-switch input[type="checkbox"] {
  margin-right: 5px;
}

.toggle-label {
  font-size: 13px;
}

.map-controls {
  display: flex;
  align-items: center;
}
