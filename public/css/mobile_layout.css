/* public/css/mobile_layout.css */
/* S<PERSON>zielle CSS-Datei nur für mobile Geräte */

/* === WIEDERVERWENDBARE CARD-LAYOUT KOMPONENTEN === */

/* Container für mobile Cards */
.mobile-cards-container {
    display: block;
    padding: 0;
}

/* Basis Card-Styling */
.mobile-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.mobile-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* S<PERSON>zielle Card-Varianten */
.mobile-card.shared {
    border-left: 4px solid #17a2b8;
}

.mobile-card.duplicate {
    border-left: 4px solid #ffc107;
}

.mobile-card.owned {
    border-left: 4px solid #28a745;
}

/* Card Header */
.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    gap: 10px;
}

.mobile-card-title {
    font-weight: 600;
    font-size: 1.1em;
    color: #0d6efd;
    text-decoration: none;
    flex: 1;
    line-height: 1.3;
}

.mobile-card-title:hover {
    text-decoration: underline;
}

.mobile-card-date {
    font-size: 0.9em;
    color: #666;
    white-space: nowrap;
    flex-shrink: 0;
}

/* Card Meta Information Grid */
.mobile-card-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 12px;
    font-size: 0.9em;
}

.mobile-card-meta-item {
    display: flex;
    flex-direction: column;
}

.mobile-card-meta-label {
    font-size: 0.8em;
    color: #666;
    margin-bottom: 2px;
    font-weight: 500;
}

.mobile-card-meta-value {
    font-weight: 600;
    color: #333;
}

/* Card Actions */
.mobile-card-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.mobile-card-actions a,
.mobile-card-actions button {
    padding: 6px 12px;
    font-size: 0.85em;
    border-radius: 4px;
    text-decoration: none;
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #495057;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mobile-card-actions a:hover,
.mobile-card-actions button:hover {
    background: #e9ecef;
}

.mobile-card-actions .button-primary {
    background: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

.mobile-card-actions .button-primary:hover {
    background: #0b5ed7;
}

.mobile-card-actions .button-delete {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.mobile-card-actions .button-delete:hover {
    background: #c82333;
}

.mobile-card-actions .button-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.mobile-card-actions .button-secondary:hover {
    background: #5a6268;
}

/* Status Indicators */
.mobile-card-indicator {
    font-size: 0.8em;
    font-style: italic;
    margin-top: 5px;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}

.mobile-card-indicator.shared {
    color: #17a2b8;
    background: rgba(23, 162, 184, 0.1);
}

.mobile-card-indicator.duplicate {
    color: #856404;
    background: rgba(255, 193, 7, 0.1);
}

.mobile-card-indicator.owned {
    color: #155724;
    background: rgba(40, 167, 69, 0.1);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: 500;
    margin-left: 8px;
}

.status-badge.duplicate {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.status-badge.shared {
    background: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
}

.status-badge.success {
    background: rgba(40, 167, 69, 0.2);
    color: #155724;
}

.status-badge.secondary {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
}

/* Status Text Styles für Planned Activities */
.status-shared {
    color: #28a745;
    font-weight: bold;
}

.status-not-shared {
    color: #6c757d;
    font-style: italic;
}

/* === EQUIPMENT-SPEZIFISCHE MOBILE ANPASSUNGEN === */
@media (max-width: 768px) {
    /* Equipment Grid Mobile */
    .equipment-grid {
        display: block !important;
    }

    .equipment-grid .content-section {
        margin-bottom: 20px !important;
        padding: 15px !important;
        border-radius: 8px !important;
    }

    .equipment-grid .form-section {
        order: 2 !important; /* Formular nach unten */
    }

    .equipment-grid .list-section {
        order: 1 !important; /* Liste nach oben */
    }

    /* Equipment Filter Mobile */
    .equipment-grid .form-group input[type="search"] {
        width: 100% !important;
        padding: 10px 12px !important;
        font-size: 0.9em !important;
        border-radius: 6px !important;
    }

    /* Sport Type Equipment Section Mobile */
    .sport-type-equipment-section {
        background: #f8f9fa !important;
        border: 1px solid #e9ecef !important;
    }

    .sport-type-equipment-section h3 {
        font-size: 1.2em !important;
        margin-bottom: 10px !important;
    }

    .sport-type-equipment-section p {
        font-size: 0.9em !important;
        margin-bottom: 15px !important;
    }

    .sport-type-equipment-section .form-group {
        margin-bottom: 10px !important;
    }

    .sport-type-equipment-section select {
        width: 100% !important;
        padding: 8px 10px !important;
        font-size: 0.9em !important;
        margin-bottom: 10px !important;
    }

    .sport-type-equipment-section .button {
        width: 100% !important;
        padding: 10px !important;
        font-size: 0.9em !important;
    }

    /* Sport Type Equipment Links Mobile */
    .sport-type-equipment-links {
        margin-top: 15px !important;
    }

    .sport-type-group {
        margin-bottom: 15px !important;
        padding: 10px !important;
        background: white !important;
        border-radius: 6px !important;
        border: 1px solid #dee2e6 !important;
    }

    .sport-type-group h5 {
        font-size: 1em !important;
        margin-bottom: 8px !important;
        color: #495057 !important;
    }

    .sport-type-group ul {
        margin: 0 !important;
        padding-left: 15px !important;
    }

    .sport-type-group li {
        margin-bottom: 5px !important;
        font-size: 0.9em !important;
        line-height: 1.4 !important;
    }

    .sport-type-group form {
        margin-top: 5px !important;
    }

    .sport-type-group button {
        padding: 2px 6px !important;
        font-size: 0.75em !important;
    }
}

/* === MOBILE-SPEZIFISCHE ANPASSUNGEN === */

/* Verstecke Desktop-Tabellen auf mobilen Geräten */
.data-table {
    display: none !important;
}

.content-section{
    padding: 10px !important;
}

/* Zeige Mobile Cards */
.mobile-cards-container {
    display: block !important;
}

/* Content-Wrapper Anpassungen für Mobile */
main.user-content-wrapper {
    padding: 0px;
    padding-top: 70px;
}

main.user-content-wrapper .user-content-inner {
    padding: 5px;
    border-radius: 0;
    box-shadow: none;
    background: #f8f9fa;
}

main.user-content-wrapper h2.page-main-title {
    padding-bottom: 0px;
    margin-bottom: 0px;
    font-size: 1.2em;
}

main.user-content-wrapper h3 {
    padding-bottom: 0px;
    margin-bottom: 0px;
    font-size: 1.2em;
}

body.user-page {
    line-height: 1.2;
}

/* Filter-Formular Anpassungen */
.filter-form {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 8px;
}

.filter-form .filter-grid {
    grid-template-columns: 1fr;
    gap: 10px;
}

.filter-form .form-group {
    margin-bottom: 0;
}

.filter-form .form-group label {
    font-size: 0.9em;
    margin-bottom: 4px;
}

.filter-form .form-group input,
.filter-form .form-group select {
    padding: 8px 12px;
    font-size: 0.9em;
}

/* Pagination Anpassungen */
.pagination {
    margin: 20px 0;
    text-align: center;
}

.pagination a,
.pagination span {
    margin: 0 2px;
    padding: 8px 12px;
    font-size: 0.9em;
}

/* Page Title */
.page-main-title {
    font-size: 1.5em;
    margin-bottom: 15px;
    padding: 0 5px;
}

/* Total Count */
.total-count {
    margin: 15px 5px 10px 5px;
    font-weight: bold;
    font-size: 0.9em;
}

/* No Results Message */
.no-activities {
    margin: 20px 5px;
    text-align: center;
    font-size: 0.9em;
    color: #666;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

/* === POI-SPEZIFISCHE MOBILE ANPASSUNGEN === */

/* POI Filter Optimierung */
.pois-filters {
    margin-bottom: 15px !important;
    padding: 12px !important;
    border-radius: 8px !important;
}

.filter-row {
    flex-direction: column !important;
    gap: 8px !important;
    margin-bottom: 8px !important;
}

.filter-group {
    width: 100% !important;
}

.filter-group label {
    font-size: 0.85em !important;
    margin-bottom: 3px !important;
}

.filter-group input,
.filter-group select {
    min-width: 100% !important;
    padding: 8px 10px !important;
    font-size: 0.9em !important;
    border-radius: 4px !important;
}

/* POI Header Mobile */
.pois-header {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 10px !important;
    margin-bottom: 15px !important;
}

.add-poi-btn {
    text-align: center !important;
    padding: 10px 15px !important;
    font-size: 0.9em !important;
}

/* POI Grid Mobile */
.poi-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    margin-top: 15px !important;
}

.poi-card {
    padding: 15px !important;
    margin-bottom: 0 !important;
}

.poi-title {
    font-size: 1.1em !important;
    margin-bottom: 8px !important;
}

.poi-actions {
    justify-content: center !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    margin-top: 12px !important;
}

.poi-actions .btn {
    padding: 6px 12px !important;
    font-size: 0.85em !important;
}
