/* public/css/activity_map_with_charts.css */

.activity-map-with-charts-container {
  margin-bottom: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.map-chart-grid {
  display: grid;
  grid-template-rows: auto auto;
  gap: 10px;
  width: 100%;
}

.activity-map {
  width: 100%;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  /*position: relative;*/
  margin-top: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 5px;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.chart-type-selector, .x-axis-selector {
  display: flex;
  gap: 15px;
}

.chart-type-selector label, .x-axis-selector label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
}

.chart-type-selector input, .x-axis-selector input {
  cursor: pointer;
}

.activity-chart {
  width: 100%;
  height: 100%;
}


.map-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ff4500;
  border: 2px solid white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}


.photo-marker {
  width: 30px;
  height: 30px;
  cursor: pointer;
}

.photo-marker-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.photo-popup {
  text-align: center;
}

.photo-popup img {
  max-width: 100%;
  max-height: 200px;
  margin-bottom: 5px;
  border-radius: 4px;
}

.photo-popup .caption {
  font-size: 12px;
  margin-top: 5px;
}

/* Responsive Design */
@media (min-width: 768px) {
  .map-chart-grid {
    grid-template-rows: auto;
    grid-template-columns: 2fr 1fr;
  }

  .chart-container {
    height: auto;
    margin-top: 0;
  }
}

@media (max-width: 767px) {
  .chart-controls {
    flex-direction: column;
    gap: 10px;
  }
}
