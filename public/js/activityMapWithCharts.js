// public/js/activityMapWithCharts.js
console.warn('---- activityMapWithCharts.js WIRD AUSGEFÜHRT ----');
// Klasse für die Aktivitätskarte mit Diagrammen
class ActivityMapWithCharts {
  constructor(activityData) {
    this.instanceId = Math.random().toString(36).substr(2, 9); // Eindeutige ID
    console.log(`NEUE INSTANZ ERSTELLT: ${this.instanceId} für mapId: ${activityData.mapId}`);

    this.activityData = activityData;
    this.map = null;
    this.chart = null;
    this.trackLayer = null;
    this.photoMarkers = [];
    this.positionMarker = null;
    this.streamData = null;
    this.trackGeoJson = null;
    this.photos = [];
    this.currentChartType = 'elevation';
    this.currentXAxis = 'distance';
    this.showPhotoMarkers = true; // Neue Eigenschaft für die Sichtbarkeit der Foto-Marker
    this.chartColors = {
      elevation: {
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderColor: 'rgba(75, 192, 192, 1)'
      },
      speed: {
        backgroundColor: 'rgba(255, 159, 64, 0.2)',
        borderColor: 'rgba(255, 159, 64, 1)'
      },
      heartrate: {
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)'
      },
      power: {
        backgroundColor: 'rgba(153, 102, 255, 0.2)',
        borderColor: 'rgba(153, 102, 255, 1)'
      }
    };

    // Debounced-Funktionen werden in initMap initialisiert

    // Initialisierung
    this.init();
  }

  // OPTIMIZATION: Helper function for debouncing
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const context = this;
      const later = function() {
        timeout = null;
        func.apply(context, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  async init() {
    try {
      // Standardwerte setzen
      this.currentChartType = 'elevation';
      this.currentXAxis = 'distance'; // Standardwert, kann später geändert werden
      this.photos = [];
      this.photoMarkers = [];

      // Daten laden
      await this.loadData();

      // Prüfen, ob distance-Daten vorhanden sind, sonst auf time umschalten
      if (this.streamData && (!this.streamData.distance || !Array.isArray(this.streamData.distance.data) || this.streamData.distance.data.length === 0)) {
        this.currentXAxis = 'time';
      }

      // Karte initialisieren
      this.initMap();

      // Diagramm initialisieren
      try {
        this.initChart();
      } catch (chartError) {
        console.error('Error initializing chart:', chartError);

        // Zeige eine Meldung im Diagramm-Container an
        const chartContainer = document.querySelector(`#${this.activityData.mapId}-chart-container .chart-container`); // More specific selector
        if (chartContainer) {
          const noDataMessage = document.createElement('div');
          noDataMessage.className = 'no-data-message';
          noDataMessage.innerHTML = 'Fehler beim Initialisieren des Diagramms. Keine Daten verfügbar.';
          noDataMessage.style.cssText = 'display: flex; justify-content: center; align-items: center; height: 100%; font-size: 14px; color: #666;';

          // Canvas entfernen und Meldung hinzufügen
          const canvas = document.getElementById(this.activityData.chartId);
          if (canvas) {
            canvas.style.display = 'none'; // Hide canvas
            // Ensure message is not added multiple times
            if (!chartContainer.querySelector('.no-data-message')) {
                 chartContainer.appendChild(noDataMessage);
            }
          }
        }
      }

      // Event-Listener hinzufügen
      this.addEventListeners();

      // Setze die Radio-Buttons auf die aktuellen Werte
      this.updateRadioButtons();

      // Initialisierung abgeschlossen
    } catch (error) {
      console.error('Error initializing ActivityMapWithCharts:', error);
    }
  }

  async loadData() {
    try {
      // Stream-Daten laden
      try {
        const streamResponse = await fetch(this.activityData.apiEndpoints.streamData);
        if (streamResponse.ok) {
          this.streamData = await streamResponse.json();
        } else {
          // Stille Fehlerbehandlung, keine Warnung ausgeben
          this.streamData = {};
        }
      } catch (streamError) {
        // Stille Fehlerbehandlung, keine Warnung ausgeben
        this.streamData = {};
      }

      // Track-GeoJSON laden (mit Fallback auf GPX-Daten)
      try {
        // Versuche zuerst, verarbeitete Track-Daten zu laden
        let trackDataLoaded = false;

        if (this.activityData.apiEndpoints.trackGeoJson) {
            try {
              const trackResponse = await fetch(this.activityData.apiEndpoints.trackGeoJson);
              if (trackResponse.ok) {
                this.trackGeoJson = await trackResponse.json();
                trackDataLoaded = true;
              }
            } catch (e) {
              // Ignoriere Fehler beim Laden der verarbeiteten Track-Daten
            }
        }


        // Wenn keine verarbeiteten Track-Daten geladen wurden, versuche GPX-Daten zu laden
        if (!trackDataLoaded && this.activityData.apiEndpoints.gpxAsGeoJson) {
          try {
            const gpxResponse = await fetch(this.activityData.apiEndpoints.gpxAsGeoJson);
            if (gpxResponse.ok) {
              this.trackGeoJson = await gpxResponse.json();
              trackDataLoaded = true;
            }
          } catch (e) {
            // Ignoriere Fehler beim Laden der GPX-Daten
          }
        }

        // Wenn immer noch keine Daten geladen wurden, setze trackGeoJson auf null
        if (!trackDataLoaded) {
          this.trackGeoJson = null;
        }
      } catch (trackError) {
        // Stille Fehlerbehandlung, keine Warnung ausgeben
        this.trackGeoJson = null;
      }

      // Fotos laden, falls vorhanden und aktiviert
      if (this.activityData.showPhotos && this.activityData.apiEndpoints.photos) {
        try {
          const photosResponse = await fetch(this.activityData.apiEndpoints.photos);
          if (photosResponse.ok) {
            this.photos = await photosResponse.json();
          } else {
            this.photos = [];
          }
        } catch (photosError) {
          // Stille Fehlerbehandlung, keine Warnung ausgeben
          this.photos = [];
        }
      }

      // Nur für Debug-Zwecke, kann in Produktion entfernt werden
      if (window.debugMode) {
        console.log('Data loaded successfully for ' + this.activityData.mapId + ':', {
          streamData: this.streamData,
          trackGeoJson: this.trackGeoJson,
          photos: this.photos
        });
      }
    } catch (error) {
      // Stille Fehlerbehandlung, keine Warnung ausgeben
      this.streamData = this.streamData || {};
      this.trackGeoJson = this.trackGeoJson || null;
      this.photos = this.photos || [];
    }
  }

  initMap() {
    // Karte initialisieren mit reduzierten Animationen für bessere Performance
    this.map = L.map(this.activityData.mapId, {
      center: [this.activityData.start_lat || 48.1, this.activityData.start_lng || 8.5],
      zoom: 12,
      attributionControl: true,
      zoomAnimation: false, // Deaktiviere Zoom-Animation für bessere Performance
      fadeAnimation: false, // Deaktiviere Fade-Animation für bessere Performance
      markerZoomAnimation: false, // Deaktiviere Marker-Zoom-Animation für bessere Performance
      preferCanvas: true // Verwende Canvas statt SVG für bessere Performance
    });

    // Basiskarte hinzufügen mit reduzierten Animationen
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      updateWhenIdle: true, // Aktualisiere nur, wenn die Karte inaktiv ist
      updateWhenZooming: false, // Aktualisiere nicht während des Zoomens
      maxZoom: 19, // Begrenze den maximalen Zoom
      minZoom: 5 // Begrenze den minimalen Zoom
    }).addTo(this.map);

    // Track-Layer hinzufügen, falls vorhanden
    if (this.trackGeoJson && this.trackGeoJson.features && this.trackGeoJson.features.length > 0 && this.trackGeoJson.features[0].geometry && this.trackGeoJson.features[0].geometry.coordinates && this.trackGeoJson.features[0].geometry.coordinates.length > 0) {
      this.trackLayer = L.geoJSON(this.trackGeoJson, {
        style: {
          color: this.getColorForSportType(this.activityData.sport_type),
          weight: 4,
          opacity: 0.8,
          smoothFactor: 1.5 // Erhöhe den Glättungsfaktor für bessere Performance
        }
      }).addTo(this.map);

      // Karte auf den Track zoomen
      try {
        this.map.fitBounds(this.trackLayer.getBounds());
      } catch (e) {
        console.warn("Could not fit map to track bounds for " + this.activityData.mapId, e);
      }

    } else {
      // Wenn kein Track vorhanden ist, zeige eine Meldung an
      const noTrackMessage = L.control({ position: 'bottomleft' });
      noTrackMessage.onAdd = () => { // Use arrow function for correct 'this' if needed, though not here
        const div = L.DomUtil.create('div', 'no-track-message leaflet-control-attribution leaflet-control'); // Added leaflet classes for styling
        div.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        div.style.padding = '5px';
        div.style.borderRadius = '3px';
        div.style.margin = '10px';
        div.style.fontSize = '12px';
        div.innerHTML = 'Keine Track-Daten verfügbar.';
        return div;
      };
      noTrackMessage.addTo(this.map);
    }

    // Positionsmarker erstellen (wird später bewegt)
    // Entferne einen vorhandenen Positionsmarker, falls vorhanden
    if (this.positionMarker) {
      this.positionMarker.remove();
    }

    // Erstelle einen neuen Positionsmarker
    this.positionMarker = L.marker([0, 0], {
      icon: L.divIcon({
        className: 'map-marker', // Should be defined in CSS
        html: '',
        iconSize: [12, 12],
        iconAnchor: [6, 6]
      }),
      zIndexOffset: 1000 // Stelle sicher, dass der Marker über anderen Elementen angezeigt wird
    });

    // Füge den Marker zur Karte hinzu, aber verstecke ihn zunächst
    this.positionMarker.addTo(this.map);
    this.positionMarker.setOpacity(0);

    // Initialisiere die Debounce-Funktion für die Positionsmarker-Aktualisierung
    this.debouncedUpdatePositionMarker = this.debounce((index) => {
      this.updatePositionMarker(index);
      this.highlightChartPoint(index);
    }, 50); // 50ms Verzögerung

    // Fotos hinzufügen, falls vorhanden
    this.addPhotoMarkers();

    // Event-Listener für Klicks auf die Karte
    this.map.on('click', this.handleMapClick.bind(this));

    // Event-Listener für Mausbewegungen über die Karte mit Debouncing für bessere Performance
    this.debouncedHandleMapMouseMove = this.debounce(this.handleMapMouseMove.bind(this), 50);
    this.map.on('mousemove', this.debouncedHandleMapMouseMove);

    // Debug-Ausgabe für die Karte
    console.log('Map initialized with track:', this.trackGeoJson ? 'available' : 'not available');
  }

  addPhotoMarkers() {
    if (!this.photos || !this.photos.length || !this.map) return;

    // Bestehende Marker entfernen
    this.photoMarkers.forEach(marker => marker.remove());
    this.photoMarkers = [];

    // Neue Marker hinzufügen
    this.photos.forEach(photo => {
      if (!photo.location_lat || !photo.location_lng) return;

      const photoUrl = photo.external_url ||
        (photo.google_base_url ? `${photo.google_base_url}=w200-h200` : null);

      if (!photoUrl) return;

      const marker = L.marker([photo.location_lat, photo.location_lng], {
        icon: L.divIcon({
          className: 'photo-marker', // Defined in CSS
          iconSize: [30, 30],
          html: `<div class="photo-marker-inner" style="background-image: url('${photoUrl}');"></div>`
        })
      });

      marker.bindPopup(`
        <div class.photo-popup" style="min-width: 150px; text-align: center;">
          <img src="${photoUrl}" alt="${photo.caption || 'Activity photo'}" style="max-width: 100%; height: auto; border-radius: 3px;" />
          ${photo.caption ? `<div class.caption" style="font-size: 12px; margin-top: 5px;">${photo.caption}</div>` : ''}
        </div>
      `);

      // Füge den Marker nur hinzu, wenn showPhotoMarkers true ist
      if (this.showPhotoMarkers) {
        marker.addTo(this.map);
      }
      this.photoMarkers.push(marker);
    });
  }

  // Neue Methode zum Ein-/Ausblenden der Foto-Marker
  togglePhotoMarkers(show) {
    this.showPhotoMarkers = show;

    if (!this.map || !this.photoMarkers.length) return;

    this.photoMarkers.forEach(marker => {
      if (show) {
        marker.addTo(this.map);
      } else {
        marker.remove();
      }
    });
  }

  initChart() {
    // Prüfen, ob das Canvas-Element existiert
    const canvas = document.getElementById(this.activityData.chartId);
    if (!canvas) {
      console.error('Chart canvas element not found:', this.activityData.chartId);
      throw new Error('Chart canvas element not found');
    }

    // Setze feste Größe für das Canvas-Element, um Resize-Schleifen zu vermeiden
    const container = canvas.parentElement;
    if (container) {
      // Setze die Breite des Canvas auf die aktuelle Breite des Containers
      const containerWidth = container.clientWidth - 20;
      alert(containerWidth);
      canvas.width = containerWidth;
      canvas.style.width = containerWidth + 'px';
      canvas.height = 300; // Feste Höhe
      canvas.style.height = '300px';
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('Failed to get 2D context from canvas for ' + this.activityData.chartId);
      throw new Error('Failed to get 2D context from canvas');
    }

    // Prüfen, ob Stream-Daten vorhanden sind
    if (!this.streamData || !this.hasRequiredStreamData()) {

      // Versuche, Diagrammdaten aus dem trackGeoJson zu extrahieren, wenn vorhanden
      if (this.trackGeoJson && this.trackGeoJson.features && this.trackGeoJson.features.length > 0) {
        const feature = this.trackGeoJson.features[0];
        if (feature.geometry && feature.geometry.coordinates && feature.geometry.coordinates.length > 1) { // Need at least 2 points
          // Erstelle Stream-Daten aus den GeoJSON-Koordinaten
          this.streamData = this.streamData || {};

          const coordinates = feature.geometry.coordinates;
          const altitudeData = coordinates.map(coord => coord.length > 2 ? coord[2] : 0);
          const distanceData = [0];
          const latLngStreamData = [[coordinates[0][1], coordinates[0][0]]]; // Start with first lat/lng

          for (let i = 1; i < coordinates.length; i++) {
            const prevCoord = coordinates[i-1];
            const currCoord = coordinates[i];
            const distance = this.calculateDistance(
              prevCoord[1], prevCoord[0], // lat1, lng1
              currCoord[1], currCoord[0]  // lat2, lng2
            );
            distanceData.push(distanceData[i-1] + distance);
            latLngStreamData.push([currCoord[1], currCoord[0]]);
          }

          const timeData = [];
          const totalDistance = distanceData[distanceData.length - 1];
          const estimatedSpeedMPS = 5000 / 3600; // 5 km/h in m/s
          const estimatedDuration = totalDistance > 0 ? totalDistance / estimatedSpeedMPS : 0;

          for (let i = 0; i < coordinates.length; i++) {
            timeData.push(coordinates.length > 1 ? (i / (coordinates.length - 1)) * estimatedDuration : 0);
          }

          this.streamData.altitude = { data: altitudeData };
          this.streamData.distance = { data: distanceData };
          this.streamData.time = { data: timeData };
          this.streamData.latlng = { data: latLngStreamData };


          if (distanceData.length > 1 && timeData.length > 1 && distanceData.length === timeData.length) {
            const velocityData = [0]; // Start velocity at 0 for the first point
            for (let i = 1; i < distanceData.length; i++) {
              const distanceDiff = distanceData[i] - distanceData[i-1];
              const timeDiff = timeData[i] - timeData[i-1];
              const velocity = timeDiff > 0 ? distanceDiff / timeDiff : 0;
              velocityData.push(velocity);
            }
            this.streamData.velocity_smooth = { data: velocityData };
          } else {
             this.streamData.velocity_smooth = { data: new Array(altitudeData.length).fill(0) };
          }
        }
      }

      // Prüfe erneut, ob jetzt Stream-Daten vorhanden sind
      if (!this.streamData || !this.hasRequiredStreamData()) {
        const chartContainer = document.querySelector(`#${this.activityData.mapId}-chart-container .chart-container`);
        if (chartContainer) {
          canvas.style.display = 'none'; // Hide canvas
          if (!chartContainer.querySelector('.no-data-message')) { // Avoid duplicates
            const noDataMessage = document.createElement('div');
            noDataMessage.className = 'no-data-message';
            noDataMessage.innerHTML = 'Keine Diagramm-Daten verfügbar für diese Aktivität.';
            noDataMessage.style.cssText = 'display: flex; justify-content: center; align-items: center; height: 100%; font-size: 14px; color: #666;';
            chartContainer.appendChild(noDataMessage);
          }
        }
        const chartControls = document.querySelector(`#${this.activityData.mapId}-chart-container .chart-controls`);
        if (chartControls) {
          chartControls.style.display = 'none';
        }
        return;
      }
    }

    try {
      // Diagrammdaten vorbereiten
      const chartData = this.prepareChartData();

      if (this.chart) { // Destroy existing chart before creating a new one
          this.chart.destroy();
      }

      // Prüfe, ob die Daten gültig sind
      if (!chartData.datasets || !chartData.datasets[0] || !chartData.datasets[0].data || chartData.datasets[0].data.length === 0) {
        throw new Error('No valid chart data available');
      }

      // Prüfe, ob die Datenpunkte gültig sind
      const hasInvalidPoints = chartData.datasets[0].data.some(point =>
        !point || typeof point.x !== 'number' || typeof point.y !== 'number' ||
        isNaN(point.x) || isNaN(point.y)
      );

      if (hasInvalidPoints) {
        console.error('Chart data contains invalid points');
        throw new Error('Chart data contains invalid points');
      }

      // Chart.js-Diagramm erstellen
      this.chart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: this.getChartOptions() // Options now include decimation
      });
    } catch (error) {
      console.error('Error creating chart for ' + this.activityData.chartId + ':', error);
      const chartContainer = document.querySelector(`#${this.activityData.mapId}-chart-container .chart-container`);
      if (chartContainer) {
        canvas.style.display = 'none';
         if (!chartContainer.querySelector('.chart-error-message')) {
            const errorMessage = document.createElement('div');
            errorMessage.className = 'chart-error-message';
            errorMessage.innerHTML = 'Fehler beim Erstellen des Diagramms: ' + error.message;
            errorMessage.style.cssText = 'display: flex; justify-content: center; align-items: center; height: 100%; font-size: 14px; color: #666;';
            chartContainer.appendChild(errorMessage);
        }
      }
    }
  }

  hasRequiredStreamData() {
    if (!this.streamData) return false;

    const hasDistance = this.streamData.distance && Array.isArray(this.streamData.distance.data) && this.streamData.distance.data.length > 0;
    const hasTime = this.streamData.time && Array.isArray(this.streamData.time.data) && this.streamData.time.data.length > 0;
    const hasLatlng = this.streamData.latlng && Array.isArray(this.streamData.latlng.data) && this.streamData.latlng.data.length > 0;

    let xDataLength = 0;
    if (this.currentXAxis === 'distance' && hasDistance) xDataLength = this.streamData.distance.data.length;
    else if (this.currentXAxis === 'time' && hasTime) xDataLength = this.streamData.time.data.length;
    else if (hasDistance) xDataLength = this.streamData.distance.data.length; // Fallback if currentXAxis is not yet set but distance exists
    else if (hasTime) xDataLength = this.streamData.time.data.length; // Fallback if currentXAxis is not yet set but time exists

    if (xDataLength === 0) return false; // No valid X-axis data

    const latlngMatchesLength = hasLatlng && this.streamData.latlng.data.length === xDataLength;
    if (!latlngMatchesLength) {
        // console.warn(`LatLng data length (${hasLatlng ? this.streamData.latlng.data.length : 0}) does not match X-axis data length (${xDataLength}) for ${this.activityData.mapId}. Chart interaction might be impaired.`);
        // Depending on strictness, you might return false here. For now, allow chart to render.
    }


    const hasElevation = this.streamData.altitude && Array.isArray(this.streamData.altitude.data) && this.streamData.altitude.data.length === xDataLength;
    const hasSpeed = this.streamData.velocity_smooth && Array.isArray(this.streamData.velocity_smooth.data) && this.streamData.velocity_smooth.data.length === xDataLength;
    const hasHeartrate = this.streamData.heartrate && Array.isArray(this.streamData.heartrate.data) && this.streamData.heartrate.data.length === xDataLength;
    const hasPower = this.streamData.watts && Array.isArray(this.streamData.watts.data) && this.streamData.watts.data.length === xDataLength;

    return (hasElevation || hasSpeed || hasHeartrate || hasPower); // At least one Y-data stream matching X-data length
  }

  prepareChartData() {
    if (!this.streamData) {
      console.error('No stream data available for chart');
      return { labels: [], datasets: [{ label: 'Keine Daten', data: [] }] };
    }

    // Prüfe, ob die notwendigen Daten vorhanden sind
    let hasDistance = this.streamData.distance && Array.isArray(this.streamData.distance.data) && this.streamData.distance.data.length > 0;
    let hasTime = this.streamData.time && Array.isArray(this.streamData.time.data) && this.streamData.time.data.length > 0;


    let xData = [];
    let validXAxis = false;

    // Konvertiere ISO-Zeitstrings in Sekunden seit Beginn der Aktivität
    let timeDataInSeconds = [];
    if (hasTime) {

      try {
        // Prüfe, ob die Zeitdaten ISO-Strings sind
        const firstTimeValue = this.streamData.time.data[0];
        const isISOString = typeof firstTimeValue === 'string' && firstTimeValue.includes('T');

        if (isISOString) {

          // Konvertiere ISO-Strings in Zeitstempel
          const timestamps = [];
          for (let i = 0; i < this.streamData.time.data.length; i++) {
            const timeStr = this.streamData.time.data[i];
            const timestamp = new Date(timeStr).getTime();

            if (isNaN(timestamp)) {
              console.warn(`Invalid timestamp at index ${i}: ${timeStr}`);
              continue;
            }

            timestamps.push(timestamp);
          }

          if (timestamps.length === 0) {
            console.error('No valid timestamps could be extracted from time data');
            this.streamData.time.data = [];
            hasTime = false;
          } else {
            // Berechne Sekunden seit Beginn der Aktivität
            const startTime = timestamps[0];
            timeDataInSeconds = timestamps.map(timestamp => (timestamp - startTime) / 1000);



            // Ersetze die ursprünglichen Zeitdaten durch die Sekunden
            this.streamData.time.data = timeDataInSeconds;
          }
        } else {
        }
      } catch (error) {
        console.error('Error converting time data:', error);
        this.streamData.time.data = [];
        hasTime = false;
      }
    }

    // Wenn wir einen Track haben, aber keine Distanz- oder Zeitdaten, generiere sie
    if (this.trackGeoJson && this.trackGeoJson.features &&
        this.trackGeoJson.features.length > 0 &&
        this.trackGeoJson.features[0].geometry &&
        this.trackGeoJson.features[0].geometry.coordinates &&
        this.trackGeoJson.features[0].geometry.coordinates.length > 1) {

      const coordinates = this.trackGeoJson.features[0].geometry.coordinates;

      // Generiere Distanzdaten, wenn sie nicht vorhanden sind
      if (!hasDistance) {

        const distanceData = [0];
        for (let i = 1; i < coordinates.length; i++) {
          const prevCoord = coordinates[i-1];
          const currCoord = coordinates[i];
          const distance = this.calculateDistance(
            prevCoord[1], prevCoord[0], // lat1, lng1
            currCoord[1], currCoord[0]  // lat2, lng2
          );
          distanceData.push(distanceData[i-1] + distance);
        }

        this.streamData.distance = { data: distanceData };
        hasDistance = true;

      }

      // Generiere Zeitdaten, wenn sie nicht vorhanden sind
      if (!hasTime) {

        const timeData = [];
        const totalDistance = this.streamData.distance.data[this.streamData.distance.data.length - 1];
        const estimatedSpeedMPS = 5000 / 3600; // 5 km/h in m/s
        const estimatedDuration = totalDistance > 0 ? totalDistance / estimatedSpeedMPS : 0;

        for (let i = 0; i < coordinates.length; i++) {
          timeData.push(coordinates.length > 1 ? (i / (coordinates.length - 1)) * estimatedDuration : 0);
        }

        this.streamData.time = { data: timeData };
        hasTime = true;

      }
    }

    if (this.currentXAxis === 'distance') {
      if (hasDistance) {
        xData = this.streamData.distance.data;
        validXAxis = true;
      } else if (hasTime) {
        console.log('Switching to time-based x-axis because distance data is not available');
        this.currentXAxis = 'time';
        xData = this.streamData.time.data;
        validXAxis = true;
      }
    } else { // time
      if (hasTime) {
        xData = this.streamData.time.data;
        validXAxis = true;
      } else if (hasDistance) {
        console.log('Switching to distance-based x-axis because time data is not available');
        this.currentXAxis = 'distance';
        xData = this.streamData.distance.data;
        validXAxis = true;
      }
    }

    // Generiere Geschwindigkeitsdaten, wenn sie nicht vorhanden sind, aber Distanz- und Zeitdaten verfügbar sind
    if (hasDistance && hasTime &&
        (!this.streamData.velocity_smooth ||
         !Array.isArray(this.streamData.velocity_smooth.data) ||
         this.streamData.velocity_smooth.data.length === 0)) {


      const velocityData = [0]; // Start mit 0 für den ersten Punkt
      for (let i = 1; i < this.streamData.distance.data.length; i++) {
        const distanceDiff = this.streamData.distance.data[i] - this.streamData.distance.data[i-1];
        const timeDiff = this.streamData.time.data[i] - this.streamData.time.data[i-1];
        const velocity = timeDiff > 0 ? distanceDiff / timeDiff : 0;
        velocityData.push(velocity);
      }

      this.streamData.velocity_smooth = { data: velocityData };

    }

    if (!validXAxis || xData.length === 0) {
      console.error('No valid X-axis data available');
      return { labels: [], datasets: [{ label: 'Keine X-Achsen Daten', data: [] }] };
    }

    let yData = [];
    let label = '';
    let yDataFound = false;
    let selectedStream;

    // Prüfe, ob die gewählte Diagrammart verfügbar ist
    let validChartType = this.isChartTypeAvailable(this.currentChartType);
    if (!validChartType) {
      console.warn(`Chart type ${this.currentChartType} is not available, falling back to elevation`);
      this.currentChartType = 'elevation';

      // Prüfe, ob Höhendaten verfügbar sind
      if (!this.isChartTypeAvailable('elevation')) {
        console.error('No elevation data available');
        return { datasets: [{ label: 'Keine Daten verfügbar', data: [] }] };
      }
    }

    switch (this.currentChartType) {
      case 'elevation':
        selectedStream = this.streamData.altitude;
        label = 'Höhe (m)';
        break;
      case 'speed':
        selectedStream = this.streamData.velocity_smooth;
        label = 'Geschwindigkeit (km/h)';
        break;
      case 'heartrate':
        selectedStream = this.streamData.heartrate;
        label = 'Puls (bpm)';
        break;
      case 'power':
        selectedStream = this.streamData.watts;
        label = 'Leistung (W)';
        break;
      default:
        label = 'Unbekannter Typ';
    }

    if (selectedStream && Array.isArray(selectedStream.data)) {
      if (selectedStream.data.length === xData.length) {
        yData = (this.currentChartType === 'speed') ? selectedStream.data.map(v => v * 3.6) : selectedStream.data;
        yDataFound = true;
      } else {
        console.warn(`Y-data length (${selectedStream.data.length}) does not match X-data length (${xData.length})`);
      }
    }

    if (!yDataFound) {
      console.warn(`No valid Y-data for ${this.currentChartType} matching X-data length for ${this.activityData.mapId}.`);

      // Versuche, Höhendaten zu verwenden, wenn keine anderen Daten verfügbar sind
      if (this.currentChartType !== 'elevation' && this.streamData.altitude && Array.isArray(this.streamData.altitude.data) && this.streamData.altitude.data.length === xData.length) {
        console.log('Falling back to elevation data');
        yData = this.streamData.altitude.data;
        label = 'Höhe (m)';
        this.currentChartType = 'elevation';
        yDataFound = true;
      } else {
        // Wenn keine passenden Daten gefunden wurden, fülle mit Nullen
        yData = new Array(xData.length).fill(0);
        console.warn('Filling Y data with zeros');
      }
    }

    const formattedXData = (this.currentXAxis === 'distance')
      ? xData.map(d => parseFloat((d / 1000).toFixed(2))) // Keep as number for decimation
      : xData.map(t => parseFloat((t / 60).toFixed(1)));  // Keep as number for decimation



    // Erstelle ein Array von Datenpunkten mit x und y Werten
    const dataPoints = [];
    for (let i = 0; i < formattedXData.length; i++) {
      // Stelle sicher, dass die Werte gültige Zahlen sind
      const xValue = parseFloat(formattedXData[i]);
      const yValue = parseFloat(yData[i]);

      // Überspringe ungültige Werte
      if (isNaN(xValue) || isNaN(yValue)) {
        console.warn(`Skipping invalid data point at index ${i}: x=${formattedXData[i]}, y=${yData[i]}`);
        continue;
      }

      dataPoints.push({
        x: xValue,
        y: yValue
      });
    }



    // Wenn keine gültigen Datenpunkte vorhanden sind, gib einen Fehler zurück
    if (dataPoints.length === 0) {
      console.error('No valid data points could be created');
      return {
        datasets: [{
          label: 'Keine gültigen Daten',
          data: []
        }]
      };
    }

    return {
      datasets: [{
        label: label,
        data: dataPoints,
        backgroundColor: this.chartColors[this.currentChartType]?.backgroundColor || 'rgba(200, 200, 200, 0.2)',
        borderColor: this.chartColors[this.currentChartType]?.borderColor || 'rgba(200, 200, 200, 1)',
        borderWidth: 1.5,
        fill: true,
        tension: 0.1, // Leichte Glättung für bessere Darstellung
        pointRadius: 0, // Keine Punkte für bessere Performance
        pointHoverRadius: 5, // Punkte beim Hover anzeigen
      }]
    };
  }

  getChartOptions() {
    return {
      responsive: false, // Deaktiviere responsive Verhalten, um Resize-Schleifen zu vermeiden
      maintainAspectRatio: false,
      animation: { // Animations disabled globally for performance
          duration: 0
      },
      parsing: false, // Wichtig für die Performance, da die Daten bereits im Format {x,y} sind
      responsiveAnimationDuration: 0,
      elements: {
          line: {
              tension: 0.1 // Slight tension for smoother lines, 0 for max performance
          },
          point: {
              radius: 0,
              hoverRadius: 5,
              hitRadius: 10
          }
      },
      interaction: {
        mode: 'index',
        intersect: false,
        axis: 'x'
      },
      plugins: {
        // OPTIMIZATION: Added Decimation plugin
        decimation: {
          enabled: true,
          algorithm: 'lttb', // Largest Triangle Three Buckets
          samples: 500, // Target number of data points after decimation
          threshold: 1000, // Only decimate if data points > threshold
        },
        tooltip: {
          enabled: true,
          callbacks: {
            title: (tooltipItems) => {
              if (!tooltipItems || tooltipItems.length === 0) return '';
              const item = tooltipItems[0];

              // Verwende die X-Koordinate direkt aus dem Datenpunkt
              const xValue = item.parsed.x;

              // Formatiere den Wert basierend auf der X-Achse
              let formattedValue = '';
              if (this.currentXAxis === 'distance') {
                formattedValue = `${xValue.toFixed(2)} km`;
              } else { // time
                formattedValue = `${xValue.toFixed(1)} min`;
              }

              return `${this.currentXAxis === 'distance' ? 'Distanz' : 'Zeit'}: ${formattedValue}`;
            },
             label: (tooltipItem) => {
                let label = tooltipItem.dataset.label || '';
                if (label) {
                    label += ': ';
                }
                if (tooltipItem.parsed.y !== null) {
                    let value = tooltipItem.parsed.y;
                    if (this.currentChartType === 'speed') { // Already in km/h from prepareChartData
                        label += value.toFixed(1) + ' km/h';
                    } else if (this.currentChartType === 'elevation') {
                        label += value.toFixed(0) + ' m';
                    } else if (this.currentChartType === 'heartrate') {
                        label += value.toFixed(0) + ' bpm';
                    } else if (this.currentChartType === 'power') {
                        label += value.toFixed(0) + ' W';
                    } else {
                        label += value;
                    }
                }
                return label;
            }
          }
        },
        legend: {
            display: true
        }
      },
      scales: {
        x: {
          type: 'linear', // Important for decimation and numerical labels
          title: {
            display: true,
            text: this.currentXAxis === 'distance' ? 'Distanz (km)' : 'Zeit (min)'
          },
          ticks: {
            maxTicksLimit: 10,
            autoSkip: true,
             callback: function(value) {
                // Ensure labels are formatted nicely, e.g., for km or min
                return parseFloat(value).toFixed(1);
            }
          },
          grid: {
            display: true,
            color: 'rgba(200, 200, 200, 0.2)'
          }
        },
        y: {
          title: {
            display: true,
            text: this.getYAxisLabel()
          },
          beginAtZero: false
        }
      },
      onHover: (_, elements) => { // Verwende _ für ungenutzte Parameter
        // Wenn keine Elemente vorhanden sind oder die Maus nicht über dem Diagramm ist
        if (!elements || !elements.length) {
            // Verstecke den Marker nicht, damit er bei Klicks auf die Karte sichtbar bleibt
            // Verstecke nur die Informationsanzeige
            if (this.positionInfoElement) {
                this.positionInfoElement.style.display = 'none';
            }

            // Entferne den hervorgehobenen Punkt im Diagramm
            if (this.chart && this.chart.data && this.chart.data.datasets && this.chart.data.datasets.length > 1) {
                this.chart.data.datasets[1].data = [];
                this.chart.update('none');
            }

            return;
        }



        // Verwende den Index des Elements direkt
        const element = elements[0];
        if (!element || element.index === undefined) {
            console.warn('No valid element index found');
            return;
        }

        const dataIndex = element.index;

        // Finde den entsprechenden Index in den Originaldaten
        let originalIndex;

        // Wenn die Anzahl der Datenpunkte im Diagramm mit der Anzahl der Originaldatenpunkte übereinstimmt
        if (this.chart.data.datasets[0].data.length === this.streamData.latlng.data.length) {
            // Verwende den Index direkt
            originalIndex = dataIndex;
        } else {
            // Berechne den entsprechenden Index basierend auf dem Verhältnis
            const ratio = dataIndex / (this.chart.data.datasets[0].data.length - 1);
            originalIndex = Math.floor(ratio * (this.streamData.latlng.data.length - 1));
        }

        // Wenn ein gültiger Index gefunden wurde, aktualisiere den Positionsmarker und das Diagramm
        if (originalIndex !== undefined && originalIndex >= 0 && originalIndex < this.streamData.latlng.data.length) {


            // Direkt aktualisieren ohne Debouncing für bessere Reaktionszeit
            this.updatePositionMarker(originalIndex);
            this.highlightChartPoint(originalIndex);
        } else {
            console.warn('Invalid original index:', originalIndex);
        }

        // Der Code wurde nach oben verschoben und verbessert
      }
    };
  }

  getYAxisLabel() {
    switch (this.currentChartType) {
      case 'elevation': return 'Höhe (m)';
      case 'speed': return 'Geschwindigkeit (km/h)';
      case 'heartrate': return 'Puls (bpm)';
      case 'power': return 'Leistung (W)';
      default: return '';
    }
  }

  updatePositionMarker(index) {

    // Speichere den aktuellen Index für die Synchronisierung
    this.currentPositionIndex = index;

    // Aktualisiere den Marker auf der Karte
    if (!this.map || index < 0) {

      // Verstecke den Marker, wenn kein gültiger Index vorhanden ist
      if (this.positionMarker) {
        this.positionMarker.setOpacity(0);
      }
      return;
    }

    let validPosition = null;

    // Versuche zuerst, die Position aus den Stream-Daten zu erhalten
    if (this.streamData && this.streamData.latlng && Array.isArray(this.streamData.latlng.data) &&
        index < this.streamData.latlng.data.length) {

      const latlngData = this.streamData.latlng.data[index];
      if (latlngData && Array.isArray(latlngData) && latlngData.length >= 2) {
        const [lat, lng] = latlngData;
        if (typeof lat === 'number' && typeof lng === 'number' && !isNaN(lat) && !isNaN(lng)) {
          validPosition = L.latLng(lat, lng);
        }
      }
    }

    // Fallback auf trackGeoJson, wenn keine gültige Position aus den Stream-Daten gefunden wurde
    if (!validPosition && this.trackGeoJson && this.trackGeoJson.features &&
        this.trackGeoJson.features.length > 0) {

      const feature = this.trackGeoJson.features[0];
      if (feature.geometry && feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
        const coordinates = feature.geometry.coordinates;

        // Berechne den entsprechenden Index in den GeoJSON-Koordinaten
        let geoJsonIndex;
        if (this.streamData && this.streamData.latlng && this.streamData.latlng.data) {
          // Wenn Stream-Daten vorhanden sind, verwende das Verhältnis
          const ratio = index / (this.streamData.latlng.data.length - 1);
          geoJsonIndex = Math.floor(ratio * (coordinates.length - 1));
        } else {
          // Sonst verwende den Index direkt, aber begrenze ihn auf die Länge der Koordinaten
          geoJsonIndex = Math.min(index, coordinates.length - 1);
        }

        const coord = coordinates[geoJsonIndex];
        if (coord && coord.length >= 2) {
          validPosition = L.latLng(coord[1], coord[0]);
        }
      }
    }

    // Wenn eine gültige Position gefunden wurde, aktualisiere den Marker
    if (validPosition) {


      // Aktualisiere den Marker
      this.positionMarker.setLatLng(validPosition);

      // Stelle sicher, dass der Marker sichtbar ist
      this.positionMarker.setOpacity(1);

      // Aktualisiere auch die Informationsanzeige, wenn vorhanden
      this.updatePositionInfo(index);
    } else {


      // Verstecke den Marker, wenn keine gültige Position gefunden wurde
      if (this.positionMarker) {
        this.positionMarker.setOpacity(0);
      }
    }
  }

  // Neue Methode zur Aktualisierung der Informationsanzeige
  updatePositionInfo(index) {
    // Erstelle oder aktualisiere das Informationselement, wenn es noch nicht existiert
    if (!this.positionInfoElement) {
      this.positionInfoElement = document.createElement('div');
      this.positionInfoElement.className = 'position-info';
      this.positionInfoElement.style.cssText = 'position: absolute; bottom: 10px; left: 10px; background: white; padding: 5px; border-radius: 3px; box-shadow: 0 0 5px rgba(0,0,0,0.2); font-size: 12px; z-index: 1000;';

      // Füge das Element zum Kartencontainer hinzu
      const mapContainer = document.getElementById(this.activityData.mapId);
      if (mapContainer) {
        mapContainer.style.position = 'relative';
        mapContainer.appendChild(this.positionInfoElement);
      }
    }

    // Sammle Informationen für die Anzeige
    let infoText = '';

    // Distanz
    if (this.streamData && this.streamData.distance && Array.isArray(this.streamData.distance.data) &&
        index < this.streamData.distance.data.length) {
      const distance = this.streamData.distance.data[index];
      infoText += `Distanz: ${(distance / 1000).toFixed(2)} km<br>`;
    }

    // Zeit
    if (this.streamData && this.streamData.time && Array.isArray(this.streamData.time.data) &&
        index < this.streamData.time.data.length) {
      const time = this.streamData.time.data[index];
      infoText += `Zeit: ${this.formatTime(time)}<br>`;
    }

    // Höhe
    if (this.streamData && this.streamData.altitude && Array.isArray(this.streamData.altitude.data) &&
        index < this.streamData.altitude.data.length) {
      const altitude = this.streamData.altitude.data[index];
      infoText += `Höhe: ${altitude.toFixed(0)} m<br>`;
    }

    // Geschwindigkeit
    if (this.streamData && this.streamData.velocity_smooth && Array.isArray(this.streamData.velocity_smooth.data) &&
        index < this.streamData.velocity_smooth.data.length) {
      const speed = this.streamData.velocity_smooth.data[index] * 3.6; // m/s zu km/h
      infoText += `Geschwindigkeit: ${speed.toFixed(1)} km/h<br>`;
    }

    // Herzfrequenz
    if (this.streamData && this.streamData.heartrate && Array.isArray(this.streamData.heartrate.data) &&
        index < this.streamData.heartrate.data.length) {
      const heartrate = this.streamData.heartrate.data[index];
      infoText += `Puls: ${heartrate.toFixed(0)} bpm<br>`;
    }

    // Leistung
    if (this.streamData && this.streamData.watts && Array.isArray(this.streamData.watts.data) &&
        index < this.streamData.watts.data.length) {
      const power = this.streamData.watts.data[index];
      infoText += `Leistung: ${power.toFixed(0)} W<br>`;
    }

    // Aktualisiere den Inhalt des Informationselements
    this.positionInfoElement.innerHTML = infoText;

    // Zeige das Informationselement an
    this.positionInfoElement.style.display = infoText ? 'block' : 'none';
  }

  // Hilfsmethode zur Formatierung von Zeitwerten
  formatTime(seconds) {
    if (typeof seconds !== 'number' || isNaN(seconds)) {
      return '00:00:00';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }


  addEventListeners() {
    // Event-Listener für die Diagrammtyp-Auswahl
    // Ensure selectors are specific if multiple maps are on a page
    const parentElement = document.getElementById(`${this.activityData.mapId}-chart-container`) || document;

    console.log('Adding event listeners for chart options, parent element:', parentElement);

    // Diagrammtyp-Auswahl (Höhe, Geschwindigkeit, etc.)
    const chartTypeRadios = parentElement.querySelectorAll(`input[name="chart-type-${this.activityData.mapId}"]`);
    console.log('Found chart type radios:', chartTypeRadios.length);

    chartTypeRadios.forEach(radio => {
      console.log('Adding event listener to chart type radio:', radio.value);
      radio.addEventListener('change', (event) => {
        console.log('Chart type changed to:', event.target.value);
        this.currentChartType = event.target.value;
        this.updateChart();
      });
    });

    // Event-Listener für die X-Achsen-Auswahl (Distanz oder Zeit)
    const xAxisRadios = parentElement.querySelectorAll(`input[name="x-axis-${this.activityData.mapId}"]`);
    console.log('Found x-axis radios:', xAxisRadios.length);

    xAxisRadios.forEach(radio => {
      console.log('Adding event listener to x-axis radio:', radio.value);
      radio.addEventListener('change', (event) => {
        console.log('X-axis changed to:', event.target.value);
        this.currentXAxis = event.target.value;
        this.updateChart();
      });
    });

    // Event-Listener für den Foto-Toggle-Button
    const photoToggle = document.getElementById(`photo-toggle-${this.activityData.mapId}`);
    if (photoToggle) {
      console.log('Adding event listener to photo toggle checkbox');
      photoToggle.addEventListener('change', (event) => {
        console.log('Photo toggle changed to:', event.target.checked);
        this.togglePhotoMarkers(event.target.checked);
      });

      // Setze den initialen Zustand des Checkboxes
      photoToggle.checked = this.showPhotoMarkers;
    }

    // Fallback: Wenn keine spezifischen Radios gefunden wurden, versuche es mit generischen Selektoren
    if (chartTypeRadios.length === 0) {
      console.log('No specific chart type radios found, trying generic selectors');
      const genericChartTypeRadios = document.querySelectorAll('input[name="chart-type"]');
      console.log('Found generic chart type radios:', genericChartTypeRadios.length);

      genericChartTypeRadios.forEach(radio => {
        console.log('Adding event listener to generic chart type radio:', radio.value);
        radio.addEventListener('change', (event) => {
          console.log('Chart type changed to:', event.target.value);
          this.currentChartType = event.target.value;
          this.updateChart();
        });
      });
    }

    if (xAxisRadios.length === 0) {
      console.log('No specific x-axis radios found, trying generic selectors');
      const genericXAxisRadios = document.querySelectorAll('input[name="x-axis"]');
      console.log('Found generic x-axis radios:', genericXAxisRadios.length);

      genericXAxisRadios.forEach(radio => {
        console.log('Adding event listener to generic x-axis radio:', radio.value);
        radio.addEventListener('change', (event) => {
          console.log('X-axis changed to:', event.target.value);
          this.currentXAxis = event.target.value;
          this.updateChart();
        });
      });
    }
  }

  updateRadioButtons() {
    console.log('Updating radio buttons to match current settings:', {
      chartType: this.currentChartType,
      xAxis: this.currentXAxis
    });

    // Finde den übergeordneten Container
    const parentElement = document.getElementById(`${this.activityData.mapId}-chart-container`) || document;

    // Aktualisiere die Diagrammtyp-Radios
    const chartTypeRadios = parentElement.querySelectorAll(`input[name="chart-type-${this.activityData.mapId}"]`);
    if (chartTypeRadios.length > 0) {
      console.log('Updating chart type radios:', chartTypeRadios.length);

      // Deaktiviere Radio-Buttons für nicht verfügbare Datentypen
      chartTypeRadios.forEach(radio => {
        const isAvailable = this.isChartTypeAvailable(radio.value);

        // Setze disabled-Attribut basierend auf Verfügbarkeit
        radio.disabled = !isAvailable;

        // Füge eine Klasse hinzu, um den deaktivierten Zustand zu visualisieren
        const label = radio.parentElement;
        if (label && label.tagName === 'LABEL') {
          if (!isAvailable) {
            label.classList.add('disabled-option');
            label.title = 'Keine Daten verfügbar';
          } else {
            label.classList.remove('disabled-option');
            label.title = '';
          }
        }

        // Setze checked-Attribut für den aktuellen Wert
        if (radio.value === this.currentChartType) {
          radio.checked = true;
          console.log('Set chart type radio to checked:', radio.value);
        }
      });
    } else {
      // Versuche es mit generischen Selektoren
      const genericChartTypeRadios = document.querySelectorAll('input[name="chart-type"]');
      if (genericChartTypeRadios.length > 0) {
        console.log('Updating generic chart type radios:', genericChartTypeRadios.length);

        // Deaktiviere Radio-Buttons für nicht verfügbare Datentypen
        genericChartTypeRadios.forEach(radio => {
          const isAvailable = this.isChartTypeAvailable(radio.value);

          // Setze disabled-Attribut basierend auf Verfügbarkeit
          radio.disabled = !isAvailable;

          // Füge eine Klasse hinzu, um den deaktivierten Zustand zu visualisieren
          const label = radio.parentElement;
          if (label && label.tagName === 'LABEL') {
            if (!isAvailable) {
              label.classList.add('disabled-option');
              label.title = 'Keine Daten verfügbar';
            } else {
              label.classList.remove('disabled-option');
              label.title = '';
            }
          }

          // Setze checked-Attribut für den aktuellen Wert
          if (radio.value === this.currentChartType) {
            radio.checked = true;
            console.log('Set generic chart type radio to checked:', radio.value);
          }
        });
      }
    }

    // Aktualisiere die X-Achsen-Radios
    const xAxisRadios = parentElement.querySelectorAll(`input[name="x-axis-${this.activityData.mapId}"]`);
    if (xAxisRadios.length > 0) {
      console.log('Updating x-axis radios:', xAxisRadios.length);

      // Deaktiviere Radio-Buttons für nicht verfügbare X-Achsen
      xAxisRadios.forEach(radio => {
        const isAvailable = this.isXAxisAvailable(radio.value);

        // Setze disabled-Attribut basierend auf Verfügbarkeit
        radio.disabled = !isAvailable;

        // Füge eine Klasse hinzu, um den deaktivierten Zustand zu visualisieren
        const label = radio.parentElement;
        if (label && label.tagName === 'LABEL') {
          if (!isAvailable) {
            label.classList.add('disabled-option');
            label.title = 'Keine Daten verfügbar';
          } else {
            label.classList.remove('disabled-option');
            label.title = '';
          }
        }

        // Setze checked-Attribut für den aktuellen Wert
        if (radio.value === this.currentXAxis) {
          radio.checked = true;
          console.log('Set x-axis radio to checked:', radio.value);
        }
      });
    } else {
      // Versuche es mit generischen Selektoren
      const genericXAxisRadios = document.querySelectorAll('input[name="x-axis"]');
      if (genericXAxisRadios.length > 0) {
        console.log('Updating generic x-axis radios:', genericXAxisRadios.length);

        // Deaktiviere Radio-Buttons für nicht verfügbare X-Achsen
        genericXAxisRadios.forEach(radio => {
          const isAvailable = this.isXAxisAvailable(radio.value);

          // Setze disabled-Attribut basierend auf Verfügbarkeit
          radio.disabled = !isAvailable;

          // Füge eine Klasse hinzu, um den deaktivierten Zustand zu visualisieren
          const label = radio.parentElement;
          if (label && label.tagName === 'LABEL') {
            if (!isAvailable) {
              label.classList.add('disabled-option');
              label.title = 'Keine Daten verfügbar';
            } else {
              label.classList.remove('disabled-option');
              label.title = '';
            }
          }

          // Setze checked-Attribut für den aktuellen Wert
          if (radio.value === this.currentXAxis) {
            radio.checked = true;
            console.log('Set generic x-axis radio to checked:', radio.value);
          }
        });
      }
    }
  }

  isChartTypeAvailable(chartType) {
    if (!this.streamData) return false;

    switch (chartType) {
      case 'elevation':
        // Prüfe, ob Höhendaten direkt verfügbar sind
        if (this.streamData.altitude &&
            Array.isArray(this.streamData.altitude.data) &&
            this.streamData.altitude.data.length > 0) {
          return true;
        }

        // Prüfe, ob Höhendaten aus dem Track extrahiert werden können
        if (this.trackGeoJson && this.trackGeoJson.features &&
            this.trackGeoJson.features.length > 0 &&
            this.trackGeoJson.features[0].geometry &&
            this.trackGeoJson.features[0].geometry.coordinates &&
            this.trackGeoJson.features[0].geometry.coordinates.length > 1) {

          // Prüfe, ob die Koordinaten Höhendaten enthalten (3. Wert)
          const coordinates = this.trackGeoJson.features[0].geometry.coordinates;
          return coordinates[0].length > 2 && typeof coordinates[0][2] === 'number';
        }

        return false;

      case 'speed':
        // Prüfe, ob Geschwindigkeitsdaten direkt verfügbar sind
        if (this.streamData.velocity_smooth &&
            Array.isArray(this.streamData.velocity_smooth.data) &&
            this.streamData.velocity_smooth.data.length > 0) {
          return true;
        }

        // Wenn wir Distanz- und Zeitdaten haben, können wir Geschwindigkeit berechnen
        if (this.isXAxisAvailable('distance') && this.isXAxisAvailable('time')) {
          return true;
        }

        return false;

      case 'heartrate':
        return this.streamData.heartrate &&
               Array.isArray(this.streamData.heartrate.data) &&
               this.streamData.heartrate.data.length > 0;

      case 'power':
        return this.streamData.watts &&
               Array.isArray(this.streamData.watts.data) &&
               this.streamData.watts.data.length > 0;

      default:
        return false;
    }
  }

  isXAxisAvailable(xAxis) {
    if (!this.streamData) return false;

    switch (xAxis) {
      case 'distance':
        // Wenn wir einen Track haben, können wir immer Distanzdaten berechnen
        if (this.trackGeoJson && this.trackGeoJson.features &&
            this.trackGeoJson.features.length > 0 &&
            this.trackGeoJson.features[0].geometry &&
            this.trackGeoJson.features[0].geometry.coordinates &&
            this.trackGeoJson.features[0].geometry.coordinates.length > 1) {
          return true;
        }

        // Ansonsten prüfen, ob direkte Distanzdaten vorhanden sind
        return this.streamData.distance &&
               Array.isArray(this.streamData.distance.data) &&
               this.streamData.distance.data.length > 0;

      case 'time':
        // Wenn wir einen Track haben, können wir immer Zeitdaten schätzen
        if (this.trackGeoJson && this.trackGeoJson.features &&
            this.trackGeoJson.features.length > 0 &&
            this.trackGeoJson.features[0].geometry &&
            this.trackGeoJson.features[0].geometry.coordinates &&
            this.trackGeoJson.features[0].geometry.coordinates.length > 1) {
          return true;
        }

        // Ansonsten prüfen, ob direkte Zeitdaten vorhanden sind
        return this.streamData.time &&
               Array.isArray(this.streamData.time.data) &&
               this.streamData.time.data.length > 0;

      default:
        return false;
    }
  }

  updateChart() {
    if (!this.chart) {
        console.warn("UpdateChart called but chart is not initialized for " + this.activityData.chartId);
        this.initChart(); // Try to re-initialize if not present
        return;
    }

    // Prüfe, ob die gewählte Diagrammart verfügbar ist
    let validChartType = this.isChartTypeAvailable(this.currentChartType);
    if (!validChartType) {
      console.warn(`Chart type ${this.currentChartType} is not available, falling back to elevation`);
      this.currentChartType = 'elevation';

      // Aktualisiere die Radio-Buttons
      this.updateRadioButtons();
    }

    // Prüfe, ob die gewählte X-Achse verfügbar ist
    let validXAxis = this.isXAxisAvailable(this.currentXAxis);
    if (!validXAxis) {
      console.warn(`X-axis ${this.currentXAxis} is not available, falling back to time`);
      this.currentXAxis = 'time';

      // Aktualisiere die Radio-Buttons
      this.updateRadioButtons();
    }

    // Stelle sicher, dass das Canvas-Element eine feste Größe hat, aber ändere nicht die Größe
    // während einer Aktualisierung, um Layout-Probleme zu vermeiden
    const canvas = document.getElementById(this.activityData.chartId);
    if (canvas) {
      // Setze die Größe nur bei der ersten Initialisierung, nicht bei Updates
      if (!this._canvasSizeInitialized) {
        const container = canvas.parentElement;
        if (container) {
          const containerWidth = container.clientWidth;
          canvas.width = containerWidth;
          canvas.style.width = containerWidth + 'px';
          canvas.height = 300;
          canvas.style.height = '300px';
          this._canvasSizeInitialized = true;
        }
      }
    }

    console.log('Updating chart with current settings:', {
      chartType: this.currentChartType,
      xAxis: this.currentXAxis
    });

    const newData = this.prepareChartData();

    if (!newData.datasets || !newData.datasets[0]) {
      console.error('Failed to prepare chart data');
      return;
    }

    // Aktualisiere die Diagrammdaten
    this.chart.data.datasets[0].data = newData.datasets[0].data;
    this.chart.data.datasets[0].label = newData.datasets[0].label;
    this.chart.data.datasets[0].backgroundColor = newData.datasets[0].backgroundColor;
    this.chart.data.datasets[0].borderColor = newData.datasets[0].borderColor;

    console.log('Chart data updated with new data points:', newData.datasets[0].data.length, 'sample:', newData.datasets[0].data.slice(0, 5));

    this.chart.options.scales.x.title.text = this.currentXAxis === 'distance' ? 'Distanz (km)' : 'Zeit (min)';
    this.chart.options.scales.y.title.text = this.getYAxisLabel();

    // Aktualisiere die Decimation-Einstellungen basierend auf der Datenpunktanzahl
    const dataLength = newData.datasets[0].data.length;
    this.chart.options.plugins.decimation.samples = (dataLength > 1500) ? 500 : (dataLength > 500 ? 250 : 100);

    // Stelle sicher, dass responsive deaktiviert ist
    this.chart.options.responsive = false;

    // Aktualisiere das Diagramm ohne Animation
    this.chart.update('none');
  }



  getColorForSportType(sportType) {
    const colors = {
      Run: '#e60000', Ride: '#00a300', Swim: '#0077c3', Hike: '#d97b00',
      Walk: '#8c00b3', AlpineSki: '#008ae6', BackcountrySki: '#cc00cc',
      Canoeing: '#00cccc', Crossfit: '#a30000', EBikeRide: '#007a00',
      Elliptical: '#0000a3', Golf: '#b3b300', Handcycle: '#a300a3',
      IceSkate: '#00a3a3', InlineSkate: '#ff8080', Kayaking: '#80ff80',
      Kitesurf: '#8080ff', NordicSki: '#ffff80', RockClimbing: '#ff80ff',
      RollerSki: '#80ffff', Rowing: '#666666', Sail: '#808000',
      Skateboard: '#008080', Snowboard: '#800080', Snowshoe: '#ff6666',
      Soccer: '#66ff66', StairStepper: '#6666ff', StandUpPaddling: '#ffff66',
      Surfing: '#ff66ff', Velomobile: '#66ffff', VirtualRide: '#404040',
      VirtualRun: '#595900', WeightTraining: '#004d4d', Wheelchair: '#4d004d',
      Windsurf: '#ff4d4d', Workout: '#4dff4d', Yoga: '#4d4dff'
    };
    return colors[sportType] || '#808080';
  }

  calculateDistance(lat1, lon1, lat2, lon2) {
    if (lat1 == lat2 && lon1 == lon2) return 0;
    const R = 6371e3; // Earth radius in meters
    const phi1 = lat1 * Math.PI / 180;
    const phi2 = lat2 * Math.PI / 180;
    const deltaPhi = (lat2 - lat1) * Math.PI / 180;
    const deltaLambda = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(deltaPhi / 2) * Math.sin(deltaPhi / 2) +
              Math.cos(phi1) * Math.cos(phi2) *
              Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in meters
  }
  // Methode zur Behandlung von Klicks auf die Karte
  handleMapClick(event) {


    if (!this.map) {
      return;
    }

    if (!this.trackGeoJson) {
      return;
    }

    // Finde den nächsten Punkt auf dem Track
    const clickLatLng = event.latlng;
    let closestPointIndex = -1;
    let minDistance = Infinity;

    // Versuche zuerst, den nächsten Punkt aus den Stream-Daten zu finden
    if (this.streamData && this.streamData.latlng && Array.isArray(this.streamData.latlng.data) && this.streamData.latlng.data.length > 0) {
      console.log('Finding closest point in stream data, points:', this.streamData.latlng.data.length);

      // Durchlaufe alle Punkte im Track und finde den nächsten
      for (let i = 0; i < this.streamData.latlng.data.length; i++) {
        const latlngData = this.streamData.latlng.data[i];
        if (!latlngData || !Array.isArray(latlngData) || latlngData.length < 2) continue;

        const [lat, lng] = latlngData;
        if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) continue;

        const pointLatLng = L.latLng(lat, lng);
        const distance = clickLatLng.distanceTo(pointLatLng);

        if (distance < minDistance) {
          minDistance = distance;
          closestPointIndex = i;
        }
      }

    }
    // Fallback: Verwende die Koordinaten aus dem GeoJSON
    else if (this.trackGeoJson.features &&
             this.trackGeoJson.features.length > 0 &&
             this.trackGeoJson.features[0].geometry &&
             this.trackGeoJson.features[0].geometry.coordinates &&
             this.trackGeoJson.features[0].geometry.coordinates.length > 0) {

      const coordinates = this.trackGeoJson.features[0].geometry.coordinates;

      // Durchlaufe alle Punkte im GeoJSON und finde den nächsten
      for (let i = 0; i < coordinates.length; i++) {
        const coord = coordinates[i];
        if (!coord || !Array.isArray(coord) || coord.length < 2) continue;

        const [lng, lat] = coord; // GeoJSON verwendet [lng, lat]
        if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) continue;

        const pointLatLng = L.latLng(lat, lng);
        const distance = clickLatLng.distanceTo(pointLatLng);

        if (distance < minDistance) {
          minDistance = distance;
          closestPointIndex = i;
        }
      }


    } else {
      return;
    }

    // Wenn ein Punkt gefunden wurde, aktualisiere den Positionsmarker
    if (closestPointIndex !== -1) {

      this.updatePositionMarker(closestPointIndex);

      // Aktualisiere auch das Diagramm, um den entsprechenden Punkt hervorzuheben
      this.highlightChartPoint(closestPointIndex);
    } else {
    }
  }

  // Methode zur Hervorhebung eines Punktes im Diagramm
  highlightChartPoint(index) {
    if (!this.chart || !this.chart.data || !this.chart.data.datasets || !this.chart.data.datasets[0] || !this.chart.data.datasets[0].data) {
      return;
    }

    // Finde den entsprechenden Punkt im Diagramm
    const chartData = this.chart.data.datasets[0].data;
    if (!Array.isArray(chartData) || chartData.length === 0) {
      return;
    }

    // Berechne den entsprechenden Index im Diagramm (kann aufgrund von Decimation unterschiedlich sein)
    let chartIndex;
    if (this.streamData && this.streamData.latlng && Array.isArray(this.streamData.latlng.data)) {
      if (chartData.length === this.streamData.latlng.data.length) {
        // Wenn die Längen übereinstimmen, verwende den Index direkt
        chartIndex = index;
      } else {
        // Sonst berechne den entsprechenden Index basierend auf dem Verhältnis
        const ratio = index / (this.streamData.latlng.data.length - 1);
        chartIndex = Math.floor(ratio * (chartData.length - 1));
      }
    } else {
      // Fallback, wenn keine latlng-Daten vorhanden sind
      chartIndex = Math.min(index, chartData.length - 1);
    }

    // Aktualisiere die Hervorhebung im Diagramm
    if (chartIndex >= 0 && chartIndex < chartData.length) {
      try {
        // Einfache Methode zur Hervorhebung: Füge einen einzelnen Punkt hinzu
        // Erstelle einen neuen Datensatz für den hervorgehobenen Punkt
        if (!this.chart.data.datasets[1]) {
          this.chart.data.datasets.push({
            label: 'Position',
            data: [],
            backgroundColor: 'red',
            borderColor: 'red',
            pointRadius: 5,
            pointHoverRadius: 7,
            pointStyle: 'circle',
            showLine: false
          });
        }

        // Setze den Datenpunkt
        this.chart.data.datasets[1].data = [chartData[chartIndex]];

        // Aktualisiere das Diagramm ohne Animation
        this.chart.update('none');
      } catch (error) {
        console.error('Error updating chart highlight:', error);
      }
    } else {
    }
  }

  // Hilfsfunktion für Debouncing
  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  // Methode zur Behandlung von Mausbewegungen über die Karte
  handleMapMouseMove(event) {
    // Wenn die Maus gedrückt ist, ignoriere die Bewegung (um Konflikte mit Drag & Zoom zu vermeiden)
    if (this.map.dragging._draggable && this.map.dragging._draggable._moved) {
      return;
    }

    // Wenn kein Track vorhanden ist, ignoriere die Bewegung
    if (!this.map || !this.trackGeoJson || !this.streamData || !this.streamData.latlng || !Array.isArray(this.streamData.latlng.data) || this.streamData.latlng.data.length === 0) {
      return;
    }

    // Finde den nächsten Punkt auf dem Track
    const mouseLatLng = event.latlng;
    let closestPointIndex = -1;
    let minDistance = Infinity;

    // Versuche zuerst, den nächsten Punkt aus den Stream-Daten zu finden
    if (this.streamData && this.streamData.latlng && Array.isArray(this.streamData.latlng.data) && this.streamData.latlng.data.length > 0) {
      // Durchlaufe alle Punkte im Track und finde den nächsten
      for (let i = 0; i < this.streamData.latlng.data.length; i++) {
        const latlngData = this.streamData.latlng.data[i];
        if (!latlngData || !Array.isArray(latlngData) || latlngData.length < 2) continue;

        const [lat, lng] = latlngData;
        if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) continue;

        const pointLatLng = L.latLng(lat, lng);
        const distance = mouseLatLng.distanceTo(pointLatLng);

        if (distance < minDistance) {
          minDistance = distance;
          closestPointIndex = i;
        }
      }
    }
    // Fallback: Verwende die Koordinaten aus dem GeoJSON
    else if (this.trackGeoJson.features &&
             this.trackGeoJson.features.length > 0 &&
             this.trackGeoJson.features[0].geometry &&
             this.trackGeoJson.features[0].geometry.coordinates &&
             this.trackGeoJson.features[0].geometry.coordinates.length > 0) {

      const coordinates = this.trackGeoJson.features[0].geometry.coordinates;

      // Durchlaufe alle Punkte im GeoJSON und finde den nächsten
      for (let i = 0; i < coordinates.length; i++) {
        const coord = coordinates[i];
        if (!coord || !Array.isArray(coord) || coord.length < 2) continue;

        const [lng, lat] = coord; // GeoJSON verwendet [lng, lat]
        if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) continue;

        const pointLatLng = L.latLng(lat, lng);
        const distance = mouseLatLng.distanceTo(pointLatLng);

        if (distance < minDistance) {
          minDistance = distance;
          closestPointIndex = i;
        }
      }
    } else {
      return;
    }

    // Wenn ein Punkt gefunden wurde und er nahe genug ist, aktualisiere den Positionsmarker
    if (closestPointIndex !== -1 && minDistance < 20) { // 20 Meter Toleranz
      this.updatePositionMarker(closestPointIndex);
      this.highlightChartPoint(closestPointIndex);
    }
  }
}

// Initialisierung aller Aktivitätskarten mit Diagrammen auf der Seite
document.addEventListener('DOMContentLoaded', () => {
  // Prüfe, ob Chart.js geladen ist
  if (typeof Chart === 'undefined') return;

  // Versuche, das Annotations-Plugin zu registrieren, wenn verfügbar
  try {
    if (typeof ChartAnnotation !== 'undefined') {
      Chart.register(ChartAnnotation);
    } else if (Chart && Chart.Annotation) {
      Chart.register(Chart.Annotation);
    }
  } catch (error) {
  }

  if (window.ACTIVITY_DATA && typeof window.ACTIVITY_DATA === 'object') {
    Object.keys(window.ACTIVITY_DATA).forEach(key => {
      const activityData = window.ACTIVITY_DATA[key];
      // Erstelle einen eindeutigen Container für jede Karte + Diagramm Kombi
      // um Selektoren spezifischer zu machen, falls die HTML-Struktur dies erfordert.
      // Angenommen, die HTML-Struktur hat bereits Container wie z.B.
      // <div id="activity-123-container">
      //   <div id="map-123"></div>
      //   <div id="activity-123-chart-container">
      //      <div class="chart-controls">...</div>
      //      <div class.chart-container"><canvas id="chart-123"></canvas></div>
      //   </div>
      // </div>
      // activityData.mapId wäre dann z.B. "map-123"
      // activityData.chartId wäre "chart-123"
      // Wir können einen übergeordneten ID annehmen oder erzeugen, z.B. basierend auf mapId
      if (activityData && activityData.mapId && activityData.chartId && activityData.apiEndpoints) {
         // Ensure the container exists in your HTML structure for each map instance.
         new ActivityMapWithCharts(activityData);
      }
    });
  }
});
