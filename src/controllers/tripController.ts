// src/controllers/tripController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { generateShareUuid } from '../utils/shareUuidUtils';
import * as tripRepository from '../db/tripRepository';
import { TripFormData } from '../types/trip';

const log = logger.getLogger(__filename);

/**
 * Zeigt die Übersicht aller Reisen des Benutzers
 * GET /user/trip
 */
export const showTripsOverview = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const fnLogPrefix = `[Ctrl TripsOverview User:${userId}]`;

  try {
    log.info(`${fnLogPrefix} Fetching trips overview...`);

    const trips = await tripRepository.getTripsForUser(userId);

    log.info(`${fnLogPrefix} Found ${trips.length} trips`);
    res.render('users/trips', {
      pageTitle: '<PERSON>ne <PERSON>isen',
      trips: trips,
      currentUser: res.locals.currentUser
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error fetching trips:`, error);
    next(error);
  }
};

/**
 * Zeigt die Detailseite einer Reise
 * GET /user/trip/:id
 */
export const showTripDetail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const fnLogPrefix = `[Ctrl TripDetail User:${userId} Trip:${tripId}]`;

  if (isNaN(tripId)) {
    res.status(400).render('error', {
      pageTitle: 'Fehler',
      message: 'Ungültige Reise-ID.',
      statusCode: 400
    });
    return;
  }

  try {
    log.info(`${fnLogPrefix} Fetching trip details...`);

    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      res.status(404).render('error', {
        pageTitle: 'Reise nicht gefunden',
        message: 'Die angeforderte Reise wurde nicht gefunden.',
        statusCode: 404
      });
      return;
    }

    // Hole verknüpfte Daten
    const [activities, plannedRoutes, pois, stats, images, geoData] = await Promise.all([
      tripRepository.getTripActivities(tripId),
      tripRepository.getTripPlannedRoutes(tripId),
      tripRepository.getTripPois(tripId),
      tripRepository.getTripPublicStats(tripId),
      tripRepository.getTripImages(tripId, 12), // Erste 12 Bilder
      tripRepository.getTripGeoData(tripId)
    ]);

    log.info(`${fnLogPrefix} Trip found with ${activities.length} activities, ${plannedRoutes.length} planned routes, ${pois.length} POIs`);

    res.render('users/trip_detail', {
      pageTitle: `Reise: ${trip.title}`,
      trip: trip,
      activities: activities,
      plannedRoutes: plannedRoutes,
      pois: pois,
      stats: stats,
      images: images,
      geoData: geoData,
      currentUser: res.locals.currentUser,
      isOwner: true
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error fetching trip details:`, error);
    next(error);
  }
};

/**
 * Zeigt das Formular zum Erstellen einer neuen Reise
 * GET /user/trip/new
 */
export const showCreateTripForm = async (req: Request, res: Response): Promise<void> => {
  res.render('users/trip_edit', {
    pageTitle: 'Neue Reise erstellen',
    trip: null,
    isEdit: false,
    currentUser: res.locals.currentUser
  });
};

/**
 * Zeigt das Formular zum Bearbeiten einer Reise
 * GET /user/trip/:id/edit
 */
export const showEditTripForm = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const fnLogPrefix = `[Ctrl EditTripForm User:${userId} Trip:${tripId}]`;

  if (isNaN(tripId)) {
    res.status(400).render('error', {
      pageTitle: 'Fehler',
      message: 'Ungültige Reise-ID.',
      statusCode: 400
    });
    return;
  }

  try {
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      res.status(404).render('error', {
        pageTitle: 'Reise nicht gefunden',
        message: 'Die angeforderte Reise wurde nicht gefunden.',
        statusCode: 404
      });
      return;
    }

    res.render('users/trip_edit', {
      pageTitle: `Reise bearbeiten: ${trip.title}`,
      trip: trip,
      isEdit: true,
      currentUser: res.locals.currentUser
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error fetching trip for edit:`, error);
    next(error);
  }
};

/**
 * Erstellt eine neue Reise
 * POST /user/trip
 */
export const createTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const fnLogPrefix = `[Ctrl CreateTrip User:${userId}]`;

  try {
    const formData: TripFormData = {
      title: req.body.title?.trim(),
      description: req.body.description?.trim() || null,
      start_date: req.body.start_date || null,
      end_date: req.body.end_date || null,
      is_public: req.body.is_public === 'on' || req.body.is_public === 'true'
    };

    // Validierung
    if (!formData.title) {
      req.flash('error', 'Titel ist erforderlich.');
      return res.redirect('/user/trip/new');
    }

    // Datum-Validierung
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
      req.flash('error', 'Das Startdatum muss vor dem Enddatum liegen.');
      return res.redirect('/user/trip/new');
    }

    const tripData = {
      user_id: userId,
      share_uuid: generateShareUuid(),
      title: formData.title,
      description: formData.description,
      start_date: formData.start_date,
      end_date: formData.end_date,
      is_public: formData.is_public
    };

    log.info(`${fnLogPrefix} Creating trip: ${tripData.title}`);
    const tripId = await tripRepository.createTrip(tripData);

    req.flash('success', 'Reise erfolgreich erstellt.');

    // Wenn Datum gesetzt ist, zu Aktivitäten-Vorschlägen weiterleiten
    if (tripData.start_date || tripData.end_date) {
      res.redirect(`/user/trip/${tripId}/suggest-activities`);
    } else {
      res.redirect(`/user/trip/${tripId}`);
    }

  } catch (error) {
    log.error(`${fnLogPrefix} Error creating trip:`, error);
    req.flash('error', 'Fehler beim Erstellen der Reise.');
    res.redirect('/user/trip/new');
  }
};

/**
 * Aktualisiert eine Reise
 * POST /user/trip/:id/edit
 */
export const updateTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const fnLogPrefix = `[Ctrl UpdateTrip User:${userId} Trip:${tripId}]`;

  if (isNaN(tripId)) {
    req.flash('error', 'Ungültige Reise-ID.');
    return res.redirect('/user/trip');
  }

  try {
    const formData: TripFormData = {
      title: req.body.title?.trim(),
      description: req.body.description?.trim() || null,
      start_date: req.body.start_date || null,
      end_date: req.body.end_date || null,
      is_public: req.body.is_public === 'on' || req.body.is_public === 'true'
    };

    // Validierung
    if (!formData.title) {
      req.flash('error', 'Titel ist erforderlich.');
      return res.redirect(`/user/trip/${tripId}/edit`);
    }

    // Datum-Validierung
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
      req.flash('error', 'Das Startdatum muss vor dem Enddatum liegen.');
      return res.redirect(`/user/trip/${tripId}/edit`);
    }

    log.info(`${fnLogPrefix} Updating trip: ${formData.title}`);
    const success = await tripRepository.updateTrip(tripId, userId, formData);

    if (success) {
      req.flash('success', 'Reise erfolgreich aktualisiert.');
      res.redirect(`/user/trip/${tripId}`);
    } else {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      res.redirect('/user/trip');
    }

  } catch (error) {
    log.error(`${fnLogPrefix} Error updating trip:`, error);
    req.flash('error', 'Fehler beim Aktualisieren der Reise.');
    res.redirect(`/user/trip/${tripId}/edit`);
  }
};

/**
 * Löscht eine Reise
 * POST /user/trip/:id/delete
 */
export const deleteTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const fnLogPrefix = `[Ctrl DeleteTrip User:${userId} Trip:${tripId}]`;

  if (isNaN(tripId)) {
    req.flash('error', 'Ungültige Reise-ID.');
    return res.redirect('/user/trip');
  }

  try {
    log.info(`${fnLogPrefix} Deleting trip`);
    const success = await tripRepository.deleteTrip(tripId, userId);

    if (success) {
      req.flash('success', 'Reise erfolgreich gelöscht.');
    } else {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
    }

    res.redirect('/user/trip');

  } catch (error) {
    log.error(`${fnLogPrefix} Error deleting trip:`, error);
    req.flash('error', 'Fehler beim Löschen der Reise.');
    res.redirect('/user/trip');
  }
};

// === Verknüpfungs-Controller ===

/**
 * Fügt eine Aktivität zu einer Reise hinzu
 * POST /user/trip/:id/add-activity
 */
export const addActivityToTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const activityId = parseInt(req.body.activityId, 10);
  const fnLogPrefix = `[Ctrl AddActivityToTrip User:${userId} Trip:${tripId} Activity:${activityId}]`;

  if (isNaN(tripId) || isNaN(activityId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Adding activity to trip`);
    const success = await tripRepository.addActivityToTrip(tripId, activityId);

    if (success) {
      req.flash('success', 'Aktivität zur Reise hinzugefügt.');
    } else {
      req.flash('info', 'Aktivität ist bereits mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error adding activity to trip:`, error);
    req.flash('error', 'Fehler beim Hinzufügen der Aktivität.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

/**
 * Entfernt eine Aktivität von einer Reise
 * POST /user/trip/:id/remove-activity
 */
export const removeActivityFromTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const activityId = parseInt(req.body.activityId, 10);
  const fnLogPrefix = `[Ctrl RemoveActivityFromTrip User:${userId} Trip:${tripId} Activity:${activityId}]`;

  if (isNaN(tripId) || isNaN(activityId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Removing activity from trip`);
    const success = await tripRepository.removeActivityFromTrip(tripId, activityId);

    if (success) {
      req.flash('success', 'Aktivität von der Reise entfernt.');
    } else {
      req.flash('info', 'Aktivität war nicht mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error removing activity from trip:`, error);
    req.flash('error', 'Fehler beim Entfernen der Aktivität.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

/**
 * Fügt eine geplante Route zu einer Reise hinzu
 * POST /user/trip/:id/add-planned-route
 */
export const addPlannedRouteToTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const plannedRouteId = parseInt(req.body.plannedRouteId, 10);
  const fnLogPrefix = `[Ctrl AddPlannedRouteToTrip User:${userId} Trip:${tripId} Route:${plannedRouteId}]`;

  if (isNaN(tripId) || isNaN(plannedRouteId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Adding planned route to trip`);
    const success = await tripRepository.addPlannedRouteToTrip(tripId, plannedRouteId);

    if (success) {
      req.flash('success', 'Geplante Route zur Reise hinzugefügt.');
    } else {
      req.flash('info', 'Geplante Route ist bereits mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error adding planned route to trip:`, error);
    req.flash('error', 'Fehler beim Hinzufügen der geplanten Route.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

/**
 * Entfernt eine geplante Route von einer Reise
 * POST /user/trip/:id/remove-planned-route
 */
export const removePlannedRouteFromTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const plannedRouteId = parseInt(req.body.plannedRouteId, 10);
  const fnLogPrefix = `[Ctrl RemovePlannedRouteFromTrip User:${userId} Trip:${tripId} Route:${plannedRouteId}]`;

  if (isNaN(tripId) || isNaN(plannedRouteId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Removing planned route from trip`);
    const success = await tripRepository.removePlannedRouteFromTrip(tripId, plannedRouteId);

    if (success) {
      req.flash('success', 'Geplante Route von der Reise entfernt.');
    } else {
      req.flash('info', 'Geplante Route war nicht mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error removing planned route from trip:`, error);
    req.flash('error', 'Fehler beim Entfernen der geplanten Route.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

/**
 * Fügt einen POI zu einer Reise hinzu
 * POST /user/trip/:id/add-poi
 */
export const addPoiToTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const poiId = parseInt(req.body.poiId, 10);
  const fnLogPrefix = `[Ctrl AddPoiToTrip User:${userId} Trip:${tripId} POI:${poiId}]`;

  if (isNaN(tripId) || isNaN(poiId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Adding POI to trip`);
    const success = await tripRepository.addPoiToTrip(tripId, poiId);

    if (success) {
      req.flash('success', 'POI zur Reise hinzugefügt.');
    } else {
      req.flash('info', 'POI ist bereits mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error adding POI to trip:`, error);
    req.flash('error', 'Fehler beim Hinzufügen des POI.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

/**
 * Entfernt einen POI von einer Reise
 * POST /user/trip/:id/remove-poi
 */
export const removePoiFromTrip = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const poiId = parseInt(req.body.poiId, 10);
  const fnLogPrefix = `[Ctrl RemovePoiFromTrip User:${userId} Trip:${tripId} POI:${poiId}]`;

  if (isNaN(tripId) || isNaN(poiId)) {
    req.flash('error', 'Ungültige IDs.');
    return res.redirect(`/user/trip/${tripId}`);
  }

  try {
    // Prüfe ob Reise dem Benutzer gehört
    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      req.flash('error', 'Reise nicht gefunden oder keine Berechtigung.');
      return res.redirect('/user/trip');
    }

    log.info(`${fnLogPrefix} Removing POI from trip`);
    const success = await tripRepository.removePoiFromTrip(tripId, poiId);

    if (success) {
      req.flash('success', 'POI von der Reise entfernt.');
    } else {
      req.flash('info', 'POI war nicht mit der Reise verknüpft.');
    }

    res.redirect(`/user/trip/${tripId}`);

  } catch (error) {
    log.error(`${fnLogPrefix} Error removing POI from trip:`, error);
    req.flash('error', 'Fehler beim Entfernen des POI.');
    res.redirect(`/user/trip/${tripId}`);
  }
};

// === API-Endpunkte ===

/**
 * API: Holt alle Reisen eines Benutzers für Dropdown/Modal
 * GET /user/api/trips
 */
export const getTripsApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const fnLogPrefix = `[Ctrl TripsApi User:${userId}]`;

  try {
    log.info(`${fnLogPrefix} Fetching trips for API`);

    const trips = await tripRepository.getTripsForUser(userId);

    // Vereinfachte Daten für API zurückgeben
    const simplifiedTrips = trips.map(trip => ({
      id: trip.id,
      title: trip.title,
      start_date: trip.start_date,
      end_date: trip.end_date,
      is_public: trip.is_public
    }));

    res.json(simplifiedTrips);

  } catch (error) {
    log.error(`${fnLogPrefix} Error fetching trips for API:`, error);
    res.status(500).json({ error: 'Fehler beim Laden der Reisen' });
  }
};

/**
 * Zeigt Aktivitäten-Vorschläge für eine neue Reise basierend auf dem Datum
 * GET /user/trip/:id/suggest-activities
 */
export const showActivitySuggestions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = res.locals.currentUser.id;
  const tripId = parseInt(req.params.id, 10);
  const fnLogPrefix = `[Ctrl ActivitySuggestions User:${userId} Trip:${tripId}]`;

  if (isNaN(tripId)) {
    res.status(400).render('error', {
      pageTitle: 'Fehler',
      message: 'Ungültige Reise-ID.',
      statusCode: 400
    });
    return;
  }

  try {
    log.info(`${fnLogPrefix} Fetching activity suggestions...`);

    const trip = await tripRepository.getTripById(tripId, userId);
    if (!trip) {
      res.status(404).render('error', {
        pageTitle: 'Reise nicht gefunden',
        message: 'Die angeforderte Reise wurde nicht gefunden.',
        statusCode: 404
      });
      return;
    }

    // Hole Aktivitäten im Zeitraum der Reise
    let suggestedActivities: any[] = [];
    if (trip.start_date || trip.end_date) {
      // Importiere activityRepository für die Datenbankabfrage
      const { getActivitiesInDateRange } = await import('../db/activityRepository');

      const startDate = trip.start_date || '1900-01-01';
      const endDate = trip.end_date || '2100-12-31';

      suggestedActivities = await getActivitiesInDateRange(userId, startDate, endDate);
      log.info(`${fnLogPrefix} Found ${suggestedActivities.length} activities in date range`);
    }

    res.render('users/trip_activity_suggestions', {
      pageTitle: `Aktivitäten für Reise: ${trip.title}`,
      trip: trip,
      suggestedActivities: suggestedActivities,
      currentUser: res.locals.currentUser
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error fetching activity suggestions:`, error);
    next(error);
  }
};
