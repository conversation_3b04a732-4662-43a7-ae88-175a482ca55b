// src/controllers/activityGeoJsonController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import * as activityRepository from '../db/activityRepository';
import * as photoRepository from '../db/photoRepository';
import { ActivityPhoto } from '../types/photo';

const log = logger.getLogger(__filename);

/**
 * API: Holt Track, Fotos, Pausen und Downhill-Segmente für PI Controller.
 * 
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 * 
 * @route GET /api/geojson/pi_control_data/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 * 
 * @returns {Promise<void>} - JSON-Antwort mit Track, Fotos, Pausen und Downhill-Segmenten
 */
export const getActivityDataForPiControl = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const activityIdParam = req.params.activityId; // Dies ist die Activity ID aus der URL
    const fnName = "[Ctrl API GetActivityDataForPiControl]";
    log.info(`${fnName} Request for activity ${activityIdParam}`);

    if (!activityIdParam) { // Vereinfachte Prüfung, da Strava IDs auch nicht-numerisch sein können
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }

    const numericActiviyId = parseInt(activityIdParam, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }

    try {
        // Hole die Aktivität, um an die interne ID und andere Basisdaten zu kommen
        const activity = await activityRepository.getActivityByPrimaryKey(numericActiviyId);
        if (!activity || !activity.id) {
            res.status(404).json({ message: `Aktivität ${numericActiviyId} nicht gefunden (Basisdaten).` });
            return;
        }
        const internalActivityId = activity.id;

        // Hole die verarbeiteten Track-Daten etc. direkt mit der internen ID oder Strava ID
        const basePiData = await activityRepository.getActivityPiDataById(numericActiviyId);
        if (!basePiData) {
            // Sollte nicht passieren, wenn 'activity' gefunden wurde, aber zur Sicherheit
            res.status(404).json({ message: `Verarbeitete Trackdaten für ${numericActiviyId} nicht gefunden.` });
            return;
        }

        const result: {
            track: any | null;
            photos: any[]; // Wird unten spezifischer typisiert
            pauseIntervals: any[];
            downhillSegments: any[];
        } = { track: null, photos: [], pauseIntervals: [], downhillSegments: [] };

        if (basePiData.processed_track_geojson) try { result.track = JSON.parse(basePiData.processed_track_geojson); } catch (e: any) { log.error(`${fnName}: Fehler Parsen Track PI: ${e.message}`); }
        if (basePiData.pause_intervals_json) try { result.pauseIntervals = JSON.parse(basePiData.pause_intervals_json); if (!Array.isArray(result.pauseIntervals)) result.pauseIntervals = []; } catch (e: any) { log.error(`${fnName}: Fehler Parsen Pausen PI: ${e.message}`); }
        if (basePiData.downhill_segments_json) try { result.downhillSegments = JSON.parse(basePiData.downhill_segments_json); if (!Array.isArray(result.downhillSegments)) result.downhillSegments = []; } catch (e: any) { log.error(`${fnName}: Fehler Parsen Downhill PI: ${e.message}`); }

        try {
            const photosFromDb: ActivityPhoto[] = await photoRepository.getPhotosForActivity(internalActivityId); // Nutzt interne ID

            result.photos = photosFromDb.map(photo => {
                // Das 'photo'-Objekt von mapDbRowToActivityPhoto enthält bereits computed_url_...
                // und die korrekte 'source_photo_id'
                let photo_id_to_use: string | number = photo.source_photo_id || photo.id; // Fallback auf DB ID, falls source_photo_id fehlt

                return {
                    photo_id: photo_id_to_use, // Ist jetzt die source_photo_id (Google oder Strava)
                    source: photo.source,
                    location: (photo.location_lat != null && photo.location_lng != null) ? [photo.location_lat, photo.location_lng] : null,
                    caption: photo.caption,
                    urls: { // Verwende die computed URLs
                        small: photo.computed_url_small,
                        medium: photo.computed_url_medium,
                        original: photo.computed_url_original,
                        googleBase: photo.google_base_url, // Für direkten Google Zugriff, falls benötigt
                        external: photo.external_url      // Link zur Quellplattform
                    },
                    dimensions: {
                        small: { width: photo.width_small, height: photo.height_small },
                        medium: { width: photo.width_medium, height: photo.height_medium },
                        original: { width: photo.width_original, height: photo.height_original }
                    },
                    created_at: photo.created_at_utc ? photo.created_at_utc.toISOString() : null
                };
            }).filter((p: any) => p.location !== null); // Filtere Fotos ohne Location für die Karte
        } catch (photoDbError: any) {
            log.error(`${fnName}: Fehler beim Holen/Formatieren der Fotos für Aktivität ${activityIdParam} (interne ID ${internalActivityId}): ${photoDbError.message}`);
        }
        res.json(result);
    } catch (error: any) {
        log.error(`API Fehler in ${fnName} für ${activityIdParam}:`, error);
        next(error);
    }
};

/**
 * API: Liefert Daten für das Höhenprofil-Diagramm mit vertikalen Ankern.
 * 
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 * 
 * @route GET /api/debug/elevation_anchor_data/:activityId
 * @route GET /api/geojson/debug/elevation_anchor_data/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 * 
 * @returns {Promise<void>} - JSON-Antwort mit Höhenprofildaten
 */
export const getElevationAnchorData = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const activityId = req.params.activityId;
    const fnName = "[Ctrl API GetElevationAnchorData]";
    log.info(`${fnName} Request for activity ${activityId}`);

    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }

    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }

    try {
        const data = await activityRepository.getElevationAndVerticalAnchors(numericActiviyId);
        if (data && data.elevationProfile) {
            res.json(data);
        } else {
            res.status(404).json({ message: `Keine Höhendaten für ${activityId}.` });
        }
    } catch (error: any) {
        log.error(`${fnName} Fehler für ${activityId}:`, error);
        next(error);
    }
};

/**
 * API: Liefert den Inhalt der Spalte processed_track_geojson.
 * 
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 * 
 * @route GET /api/geojson/processed_track/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 * 
 * @returns {Promise<void>} - GeoJSON-Antwort mit verarbeitetem Track
 */
export const getProcessedTrackGeoJson = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const activityId = req.params.activityId;
    const fnLogPrefix = `[Ctrl API GetProcessedTrack ${activityId}]`;
    log.info(`${fnLogPrefix} Request`);

    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }

    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }

    try {
        const processedJsonString = await activityRepository.getProcessedTrackJsonById(numericActiviyId);
        if (processedJsonString) {
            res.header('Content-Type', 'application/json');
            res.send(processedJsonString);
        } else {
            log.warn(`${fnLogPrefix} Kein processed_track_geojson in DB gefunden.`);
            res.status(404).json({ message: `Kein verarbeiteter Track für Aktivität ${numericActiviyId} gefunden.` });
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * API: Liefert eine GeoJSON FeatureCollection mit einem LineString basierend auf stream_json.
 * 
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 * 
 * @route GET /api/geojson/original_track/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 * 
 * @returns {Promise<void>} - GeoJSON-Antwort mit Original-Track
 */
export const getOriginalTrackGeoJson = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const activityId = req.params.activityId;
    const fnLogPrefix = `[Ctrl API GetOriginalTrack ${activityId}]`;
    log.info(`${fnLogPrefix} Request`);

    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }

    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }

    try {
        const streamJsonString = await activityRepository.getStreamJsonById(numericActiviyId);
        if (!streamJsonString) {
            res.status(404).json({ message: `Keine Stream-Daten für Aktivität ${numericActiviyId} gefunden.` });
            return;
        }
        let resultTrack: GeoJSON.FeatureCollection | null = null;
        try {
            const streamDataParsed = JSON.parse(streamJsonString);
            const findStream = (type: string): any[] => {
                if (!Array.isArray(streamDataParsed)) return [];
                const stream = streamDataParsed.find(s => s.type === type);
                return (stream && Array.isArray(stream.data)) ? stream.data : [];
            };
            const latlngStream: Array<[number, number]> = findStream('latlng');
            const altitudeStream: number[] = findStream('altitude');
            const timeStream: number[] = findStream('time');

            if (latlngStream.length >= 2 && latlngStream.length === timeStream.length) {
                const coordinates = latlngStream.map((ll, index) => {
                    const alt = altitudeStream[index];
                    return (typeof alt === 'number' && !isNaN(alt)) ? [ll[1], ll[0], alt] : [ll[1], ll[0]];
                });
                resultTrack = {
                    type: "FeatureCollection",
                    features: [{
                        type: "Feature",
                        properties: { coordinateTimes: timeStream },
                        geometry: { type: "LineString", coordinates: coordinates }
                    }]
                };
            }
        } catch (parseError: any) {
            log.error(`${fnLogPrefix} Fehler beim Parsen/Verarbeiten von stream_json:`, parseError);
            res.status(500).json({ message: "Fehler beim Verarbeiten der Stream-Daten." });
            return;
        }

        if (resultTrack) {
            res.json(resultTrack);
        } else {
            res.status(404).json({ message: `Konnte keinen gültigen Original-Track für ${activityId} erstellen.` });
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} API Fehler:`, error);
        next(error);
    }
};
