// src/controllers/publicActivityController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

const log = logger.getLogger(__filename);
import * as activityRepository from '../db/activityRepository';
import * as activityEquipmentRepository from '../db/activityEquipmentRepository';

/**
 * Zeigt eine öffentliche Aktivitätsdetailseite anhand der Share-UUID an.
 * Diese Route ist ohne Login-Requirement zugänglich.
 */
export const showPublicActivityByUuid = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const shareUuid = req.params.shareUuid;
    const fnLogPrefix = `[Ctrl PublicActivity UUID:${shareUuid}]`;

    if (!shareUuid || shareUuid.length !== 36) {
        res.status(400).render('error', {
            pageTitle: '<PERSON><PERSON>',
            message: 'Ungültige Aktivitäts-UUID.',
            statusCode: 400,
            layout: 'layouts/simple_layout'
        });
        return;
    }

    try {
        log.info(`${fnLogPrefix} Fetching public activity by UUID...`);

        // Hole die Aktivität anhand der UUID
        const activity = await activityRepository.getActivityByShareUuid(shareUuid);

        if (!activity) {
            log.warn(`${fnLogPrefix} Activity not found for UUID.`);
            res.status(404).render('error', {
                pageTitle: 'Aktivität nicht gefunden',
                message: 'Die angeforderte Aktivität wurde nicht gefunden oder ist nicht mehr verfügbar.',
                statusCode: 404,
                layout: 'layouts/simple_layout'
            });
            return;
        }

        log.info(`${fnLogPrefix} Activity found: ${activity.activity_name || `ID ${activity.id}`}`);

        // Hole verknüpfte Ausrüstung für die Anzeige (nur Details, keine Bearbeitungsmöglichkeit)
        let linkedEquipmentDetails: any[] = [];
        try {
            linkedEquipmentDetails = await activityEquipmentRepository.getEquipmentForActivity(activity.id);
        } catch (equipmentError) {
            log.warn(`${fnLogPrefix} Error fetching linked equipment:`, equipmentError);
            // Fehler beim Laden der Ausrüstung ist nicht kritisch für die öffentliche Ansicht
        }

        log.info(`${fnLogPrefix} Rendering public activity page.`);
        res.render('users/activity', {
            pageTitle: `Aktivität: ${activity.activity_name || `ID ${activity.id}`}`,
            activity: activity,
            userEquipment: [], // Keine Ausrüstungsliste für öffentliche Ansicht
            linkedEquipmentIds: [], // Keine IDs für öffentliche Ansicht
            linkedEquipmentDetails: linkedEquipmentDetails, // Details der verknüpften Ausrüstung für die Anzeige
            isOwner: false, // Immer false für öffentliche Ansicht
            isPublicView: true // Flag für die View, um öffentliche Ansicht zu kennzeichnen
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering public activity page:`, error);
        next(error);
    }
};
