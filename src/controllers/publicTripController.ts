// src/controllers/publicTripController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import * as tripRepository from '../db/tripRepository';

const log = logger.getLogger(__filename);

/**
 * Zeigt eine öffentliche Reise anhand der Share-UUID an.
 * Diese Route ist ohne Login-Requirement zugänglich.
 * GET /show/trip/:shareUuid
 */
export const showPublicTripByUuid = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const shareUuid = req.params.shareUuid;
  const fnLogPrefix = `[Ctrl PublicTrip UUID:${shareUuid}]`;

  if (!shareUuid || shareUuid.length !== 36) {
    res.status(400).render('error', {
      pageTitle: 'Fehler',
      message: 'Ungültige Reise-UUID.',
      statusCode: 400,
      layout: 'layouts/simple_layout'
    });
    return;
  }

  try {
    log.info(`${fnLogPrefix} Fetching public trip by UUID...`);

    // Hole die Reise anhand der UUID (nur öffentliche)
    const trip = await tripRepository.getTripByShareUuid(shareUuid);

    if (!trip) {
      log.warn(`${fnLogPrefix} Trip not found for UUID.`);
      res.status(404).render('error', {
        pageTitle: 'Reise nicht gefunden',
        message: 'Die angeforderte Reise wurde nicht gefunden oder ist nicht öffentlich verfügbar.',
        statusCode: 404,
        layout: 'layouts/simple_layout'
      });
      return;
    }

    log.info(`${fnLogPrefix} Public trip found: ${trip.title}`);

    // Hole verknüpfte Daten und Statistiken
    const [activities, plannedRoutes, pois, stats, images, geoData, totalImages] = await Promise.all([
      tripRepository.getTripActivities(trip.id!),
      tripRepository.getTripPlannedRoutes(trip.id!),
      tripRepository.getTripPois(trip.id!),
      tripRepository.getTripPublicStats(trip.id!),
      tripRepository.getTripImages(trip.id!, 12), // Erste 12 Bilder
      tripRepository.getTripGeoData(trip.id!),
      tripRepository.getTripImagesCount(trip.id!) // Gesamtanzahl für Paginierung
    ]);

    log.info(`${fnLogPrefix} Trip data loaded: ${activities.length} activities, ${plannedRoutes.length} planned routes, ${pois.length} POIs`);

    // Formatiere Statistiken für die Anzeige
    const formattedStats = {
      ...stats,
      total_distance_km: stats.total_distance ? (stats.total_distance / 1000).toFixed(1) : '0',
      total_elevation_gain_formatted: stats.total_elevation_gain ? Math.round(stats.total_elevation_gain).toString() : '0',
      total_moving_time_formatted: formatMovingTime(stats.total_moving_time)
    };

    log.info(`${fnLogPrefix} Rendering public trip page.`);

    // Verwende die gleiche View wie für private Ansicht, aber mit isOwner: false
    res.render('users/trip_detail', {
      pageTitle: `Reise: ${trip.title}`,
      trip: trip,
      activities: activities,
      plannedRoutes: plannedRoutes,
      pois: pois,
      stats: stats,
      images: images,
      totalImages: totalImages,
      geoData: geoData,
      currentUser: null, // Kein eingeloggter User
      isOwner: false // Wichtig: Keine Edit-Funktionen
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error rendering public trip page:`, error);
    next(error);
  }
};

/**
 * API-Endpunkt zum Laden weiterer Bilder einer öffentlichen Reise
 * GET /api/public/trip/:shareUuid/images
 */
export async function getPublicTripImagesApi(req: Request, res: Response, next: NextFunction): Promise<void> {
  const fnLogPrefix = `[Ctrl PublicTripImagesApi]`;

  try {
    const shareUuid = req.params.shareUuid;
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = parseInt(req.query.limit as string, 10) || 12;
    const offset = (page - 1) * limit;

    if (!shareUuid) {
      res.status(400).json({ error: 'Ungültige Reise-ID' });
      return;
    }

    log.info(`${fnLogPrefix} Loading images for public trip ${shareUuid}, page ${page}`);

    // Prüfe ob Reise öffentlich ist
    const trip = await tripRepository.getTripByShareUuid(shareUuid);
    if (!trip) {
      res.status(404).json({ error: 'Reise nicht gefunden' });
      return;
    }

    // Lade Bilder
    const images = await tripRepository.getTripImages(trip.id!, limit, offset);

    res.json({
      success: true,
      images: images,
      page: page,
      limit: limit,
      hasMore: images.length === limit
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error loading public trip images:`, error);
    res.status(500).json({ error: 'Fehler beim Laden der Bilder' });
  }
}

/**
 * Formatiert die Bewegungszeit in ein lesbares Format
 */
function formatMovingTime(seconds: number): string {
  if (!seconds || seconds === 0) return '0h 0m';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}
