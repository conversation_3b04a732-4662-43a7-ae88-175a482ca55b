// src/controllers/publicTripController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import * as tripRepository from '../db/tripRepository';

const log = logger.getLogger(__filename);

/**
 * Zeigt eine öffentliche Reise anhand der Share-UUID an.
 * Diese Route ist ohne Login-Requirement zugänglich.
 * GET /show/trip/:shareUuid
 */
export const showPublicTripByUuid = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const shareUuid = req.params.shareUuid;
  const fnLogPrefix = `[Ctrl PublicTrip UUID:${shareUuid}]`;

  if (!shareUuid || shareUuid.length !== 36) {
    res.status(400).render('error', {
      pageTitle: 'Fehler',
      message: 'Ungültige Reise-UUID.',
      statusCode: 400,
      layout: 'layouts/simple_layout'
    });
    return;
  }

  try {
    log.info(`${fnLogPrefix} Fetching public trip by UUID...`);

    // Hole die Reise anhand der UUID (nur öffentliche)
    const trip = await tripRepository.getTripByShareUuid(shareUuid);

    if (!trip) {
      log.warn(`${fnLogPrefix} Trip not found for UUID.`);
      res.status(404).render('error', {
        pageTitle: 'Reise nicht gefunden',
        message: 'Die angeforderte Reise wurde nicht gefunden oder ist nicht öffentlich verfügbar.',
        statusCode: 404,
        layout: 'layouts/simple_layout'
      });
      return;
    }

    log.info(`${fnLogPrefix} Public trip found: ${trip.title}`);

    // Hole verknüpfte Daten und Statistiken
    const [activities, plannedRoutes, pois, stats] = await Promise.all([
      tripRepository.getTripActivities(trip.id!),
      tripRepository.getTripPlannedRoutes(trip.id!),
      tripRepository.getTripPois(trip.id!),
      tripRepository.getTripPublicStats(trip.id!)
    ]);

    log.info(`${fnLogPrefix} Trip data loaded: ${activities.length} activities, ${plannedRoutes.length} planned routes, ${pois.length} POIs`);

    // Formatiere Statistiken für die Anzeige
    const formattedStats = {
      ...stats,
      total_distance_km: stats.total_distance ? (stats.total_distance / 1000).toFixed(1) : '0',
      total_elevation_gain_formatted: stats.total_elevation_gain ? Math.round(stats.total_elevation_gain).toString() : '0',
      total_moving_time_formatted: formatMovingTime(stats.total_moving_time)
    };

    log.info(`${fnLogPrefix} Rendering public trip page.`);
    res.render('public/trip', {
      pageTitle: `Reise: ${trip.title}`,
      trip: trip,
      activities: activities,
      plannedRoutes: plannedRoutes,
      pois: pois,
      stats: formattedStats,
      isPublicView: true,
      layout: 'layouts/simple_layout'
    });

  } catch (error) {
    log.error(`${fnLogPrefix} Error rendering public trip page:`, error);
    next(error);
  }
};

/**
 * Formatiert die Bewegungszeit in ein lesbares Format
 */
function formatMovingTime(seconds: number): string {
  if (!seconds || seconds === 0) return '0h 0m';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}
