// src/utils/shareUuidUtils.ts
import { randomUUID } from 'crypto';
import connection from '../db/connection';
import logger from '../utils/logger';

const log = logger.getLogger(__filename);
import { ResultSetHeader } from 'mysql2';

/**
 * Generiert eine neue UUID für das Teilen von Aktivitäten
 */
export function generateShareUuid(): string {
    return randomUUID();
}

/**
 * Generiert UUIDs für alle bestehenden Aktivitäten, die noch keine haben
 */
export async function migrateExistingActivitiesWithUuids(): Promise<{ success: boolean; message: string; updated: number }> {
    const fnLogPrefix = '[Utils MigrateShareUuids]';

    try {
        // Zähle Aktivitäten ohne UUID
        const [countResult] = await connection.execute<any[]>(
            'SELECT COUNT(*) as count FROM activities WHERE share_uuid IS NULL'
        );
        const totalToUpdate = countResult[0].count;

        if (totalToUpdate === 0) {
            log.info(`${fnLogPrefix} Keine Aktivitäten ohne UUID gefunden.`);
            return { success: true, message: 'Alle Aktivitäten haben bereits UUIDs', updated: 0 };
        }

        log.info(`${fnLogPrefix} Starte Migration für ${totalToUpdate} Aktivitäten...`);

        // Hole alle Aktivitäten ohne UUID
        const [activities] = await connection.execute<any[]>(
            'SELECT id FROM activities WHERE share_uuid IS NULL'
        );

        let updated = 0;

        // Update jede Aktivität einzeln mit einer neuen UUID
        for (const activity of activities) {
            const uuid = generateShareUuid();

            try {
                const [result] = await connection.execute<ResultSetHeader>(
                    'UPDATE activities SET share_uuid = ? WHERE id = ?',
                    [uuid, activity.id]
                );

                if (result.affectedRows > 0) {
                    updated++;
                }
            } catch (error: any) {
                // Falls UUID bereits existiert (sehr unwahrscheinlich), versuche eine neue
                if (error.code === 'ER_DUP_ENTRY') {
                    log.warn(`${fnLogPrefix} UUID-Kollision für Aktivität ${activity.id}, versuche neue UUID...`);
                    const newUuid = generateShareUuid();
                    const [retryResult] = await connection.execute<ResultSetHeader>(
                        'UPDATE activities SET share_uuid = ? WHERE id = ?',
                        [newUuid, activity.id]
                    );
                    if (retryResult.affectedRows > 0) {
                        updated++;
                    }
                } else {
                    log.error(`${fnLogPrefix} Fehler beim Update von Aktivität ${activity.id}:`, error);
                }
            }
        }

        log.info(`${fnLogPrefix} Migration abgeschlossen. ${updated} von ${totalToUpdate} Aktivitäten aktualisiert.`);

        return {
            success: true,
            message: `Migration erfolgreich: ${updated} von ${totalToUpdate} Aktivitäten mit UUIDs versehen`,
            updated
        };

    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler bei der Migration:`, error);
        return {
            success: false,
            message: `Fehler bei der Migration: ${error.message}`,
            updated: 0
        };
    }
}

/**
 * Generiert eine UUID für eine neue Aktivität, falls noch keine vorhanden ist
 */
export function ensureShareUuid(existingUuid?: string | null): string {
    return existingUuid || generateShareUuid();
}
