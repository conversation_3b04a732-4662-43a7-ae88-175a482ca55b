-- <PERSON><PERSON><PERSON><PERSON><PERSON> für Reisen
CREATE TABLE IF NOT EXISTS `trips` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `share_uuid` varchar(36) NOT NULL COMMENT 'UUID für öffentliche Reise-Links',
  `title` varchar(255) NOT NULL COMMENT 'Titel der Reise',
  `description` text DEFAULT NULL COMMENT 'Beschreibung der Reise',
  `start_date` date DEFAULT NULL COMMENT 'Startdatum der Reise',
  `end_date` date DEFAULT NULL COMMENT 'Enddatum der Reise',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'Ist die Reise öffentlich sichtbar',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON>EY (`id`),
  UNIQUE KEY `idx_share_uuid` (`share_uuid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  CONSTRAINT `fk_trips_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Reisen der Benutzer';

-- Verknüpfungstabelle für erledigte Aktivitäten zu Reisen
CREATE TABLE IF NOT EXISTS `trip_activities` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` int(10) unsigned NOT NULL,
  `activity_id` int(10) unsigned NOT NULL,
  `added_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_trip_activity` (`trip_id`, `activity_id`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_activity_id` (`activity_id`),
  CONSTRAINT `fk_trip_activities_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trip_activities_activity_id` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Verknüpfung von Reisen mit erledigten Aktivitäten';

-- Verknüpfungstabelle für geplante Routen zu Reisen
CREATE TABLE IF NOT EXISTS `trip_planned_routes` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` int(10) unsigned NOT NULL,
  `planned_route_id` int(10) unsigned NOT NULL,
  `added_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_trip_planned_route` (`trip_id`, `planned_route_id`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_planned_route_id` (`planned_route_id`),
  CONSTRAINT `fk_trip_planned_routes_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trip_planned_routes_planned_route_id` FOREIGN KEY (`planned_route_id`) REFERENCES `planned_routes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Verknüpfung von Reisen mit geplanten Routen';

-- Verknüpfungstabelle für POIs zu Reisen
CREATE TABLE IF NOT EXISTS `trip_pois` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` int(10) unsigned NOT NULL,
  `poi_id` int(11) NOT NULL,
  `added_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_trip_poi` (`trip_id`, `poi_id`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_poi_id` (`poi_id`),
  CONSTRAINT `fk_trip_pois_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trip_pois_poi_id` FOREIGN KEY (`poi_id`) REFERENCES `pois` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Verknüpfung von Reisen mit POIs';
