// routes/viewRoutes.js
import express from 'express';
import * as viewController from '../controllers/viewController';
import * as publicActivityController from '../controllers/publicActivityController';

const router = express.Router();



// DEPRECATED: Route entfernt - verwende /user/activity/:id stattdessen
// router.get('/activity/:id', viewController.showActivityDetail); // 2D View
// Legacy route for backward compatibility
router.get('/activity_pi_control/:id', viewController.renderActivityPiControl); // <-- NEUE ROUTE
router.get('/pi_control_compare/:id', viewController.renderPiControlComparePage); // Neuer Endpunkt für die PI Debug-Ansicht
router.get('/elevation_graph_v2/:id', viewController.renderElevationGraphPageV2); // Neuer Endpunkt für V2

// Öffentliche Aktivitätsansicht über Share-UUID (ohne Login-Requirement)
router.get('/activity/:shareUuid', publicActivityController.showPublicActivityByUuid);




//router.get('/activity_motion_pro/:id', viewController.renderActivityMotionPro);

router.get('/map/:username', viewController.showPublicUserMap); // Oder der importierte Name der Funktion


export default router;