// src/scripts/migrateShareUuids.ts
import { migrateExistingActivitiesWithUuids } from '../utils/shareUuidUtils';
import logger from '../utils/logger';

const log = logger.getLogger(__filename);

/**
 * Migrations-Skript zum Hinzufügen von UUIDs zu bestehenden Aktivitäten
 */
async function runMigration() {
    const fnLogPrefix = '[Migration ShareUUIDs]';

    try {
        log.info(`${fnLogPrefix} Starte Migration für Share-UUIDs...`);

        const result = await migrateExistingActivitiesWithUuids();

        if (result.success) {
            log.info(`${fnLogPrefix} Migration erfolgreich abgeschlossen: ${result.message}`);
            console.log(`✅ Migration erfolgreich: ${result.message}`);
        } else {
            log.error(`${fnLogPrefix} Migration fehlgeschlagen: ${result.message}`);
            console.error(`❌ Migration fehlgeschlagen: ${result.message}`);
            process.exit(1);
        }

    } catch (error: any) {
        log.error(`${fnLogPrefix} Unerwarteter Fehler bei der Migration:`, error);
        console.error(`❌ Unerwarteter Fehler: ${error.message}`);
        process.exit(1);
    }

    process.exit(0);
}

// Skript ausführen, wenn direkt aufgerufen
if (require.main === module) {
    runMigration();
}
