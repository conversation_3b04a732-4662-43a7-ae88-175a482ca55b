// src/types/activity.d.ts

// Importiere GeoJSON-Typen, falls du @types/geojson installiert hast und sie hier benötigst.
// Wenn nicht, kannst du die vereinfachten GeoJSON-Typen aus common.d.ts verwenden oder hier definieren.
import { LineString as GeoJsonLineString, Feature as GeoJsonFeature } from 'geojson';
import { ActivityPhoto, ActivityPhotoInputData } from './photo'; // Importiere aus photo.d.ts



// GeoJSON Basistypen (vereinfacht, falls @types/geojson nicht genutzt wird)
// Es wird empfohlen, @types/geojson zu installieren: npm install --save-dev @types/geojson
// Wenn installiert, können diese Deklarationen hier entfernt und direkt GeoJSON.* verwendet werden.

export type PhotoSource = 'strava' | 'google' | 'manual_upload' | 'other'; // Beispiel

// Filter für die "Meine Aktivitäten"-Seite des Users (NEU/angepasst)
export interface ActivityUserListFilters {
    searchTerm?: string;
    activityType?: string;
    dateFrom?: string; // YYYY-MM-DD
    dateTo?: string;   // YYYY-MM-DD
    participation?: 'all' | 'owned' | 'shared_with_me';
    distMin?: number; // Wird im Controller zu number geparst
    distMax?: number;
    elevMin?: number;
    elevMax?: number;
}

// Options-Typ spezifisch für die User-Liste
export interface ActivityUserListOptions {
    filters?: ActivityUserListFilters;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    page?: number;
    limit?: number;
}

// Options-Typ spezifisch für die Admin-Liste (kann so bleiben, falls schon so definiert)
export interface AdminActivityListOptions {
    filters?: ActivityListFilters;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    page?: number;
    limit?: number;
}

// Struktur für eine Aktivität in der "Meine Aktivitäten"-Liste
export interface ActivityForUserListing {
    id: number; // DB Primärschlüssel der Aktivität
    strava_id: string | number | null;
    activity_name: string | null;
    start_date_local: Date | null;
    sport_type: string | null;
    distance_m: number | null;
    elevation_gain_m: number | null;
    gpx_status: number | null; // 0 oder 1, zeigt an, ob eine GPX-Datei vorhanden ist
    photo_count_from_db: number; // Anzahl Fotos aus der activity_photos Tabelle
    is_owned_by_user: boolean; // true, wenn activity.user_id === loggedInUserId
                               // false, wenn es eine geteilte Aktivität ist
    share_uuid: string | null; // UUID für öffentliche Aktivitäts-Links
}

// ActivityListOptions kann wiederverwendet oder leicht angepasst werden,
// falls die Sortierfelder für User anders sind als für Admins.
// Fürs Erste nehmen wir an, sie ist ähnlich.
export interface ActivityListOptions {
    filters?: ActivityUserListFilters | ActivityListFilters; // Kann beide Typen annehmen
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    page?: number;
    limit?: number;
}

/**
 * Repräsentiert die Rohdatenstruktur einer Aktivität, wie sie direkt
 * aus der Datenbanktabelle 'activities' gelesen wird.
 * Die Typen hier spiegeln wider, wie Datenbanktreiber die Daten oft liefern
 * (z.B. Timestamps und Datetimes als Strings, numerische Typen als number oder string).
 */
export interface ActivityForDb {
    id: number; // int(10) unsigned NOT NULL AUTO_INCREMENT
    user_id: number; // int(11) NOT NULL
    share_uuid: string | null; // varchar(36) DEFAULT NULL (UUID für öffentliche Aktivitäts-Links)
    strava_id: number | null; // bigint(20) unsigned DEFAULT NULL (string ist sicherer für große Zahlen)
    activity_name: string | null; // varchar(255) DEFAULT NULL
    start_date: string | null; // timestamp NULL DEFAULT NULL (oft als ISO-String)
    start_date_local: string | null; // datetime DEFAULT NULL (oft als 'YYYY-MM-DD HH:MM:SS' String)
    sport_type: string | null; // varchar(50) DEFAULT NULL
    distance: number | null; // decimal(15,2) DEFAULT NULL
    total_elevation_gain: number | null; // decimal(10,2) DEFAULT NULL
    moving_time: number | null; // int(10) unsigned DEFAULT NULL
    elapsed_time: number | null; // int(10) unsigned DEFAULT NULL
    start_lat: number | null; // decimal(10,7) DEFAULT NULL
    start_lng: number | null; // decimal(10,7) DEFAULT NULL
    end_lat: number | null; // decimal(10,7) DEFAULT NULL
    end_lng: number | null; // decimal(10,7) DEFAULT NULL
    location_country: string | null; // varchar(255) DEFAULT NULL
    private_note: string | null; // text DEFAULT NULL
    summary_polyline: string | null; // text DEFAULT NULL
    detailed_polyline: string | null; // mediumtext DEFAULT NULL
    average_speed: number | null; // decimal(10,5) DEFAULT NULL
    max_speed: number | null; // decimal(10,5) DEFAULT NULL
    average_heartrate: number | null; // decimal(5,2) DEFAULT NULL
    max_heartrate: number | null; // decimal(5,2) DEFAULT NULL
    photo_count: number | null; // int(11) DEFAULT NULL
    resource_state: number | null; // int(11) DEFAULT NULL
    some_json: string | null; // text DEFAULT NULL (für rohe Strava-Daten)
    stream_json: string | null; // text DEFAULT NULL (für rohe Stream-Daten)
    gpx: number | null; // tinyint(1) DEFAULT NULL (0 oder 1)
    created_at: string; // timestamp NOT NULL DEFAULT current_timestamp() (oft als ISO-String)
    updated_at: string; // timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
    processed_track_geojson: string | null; // mediumtext DEFAULT NULL
    pause_intervals_json: string | null; // text DEFAULT NULL
    downhill_segments_json: string | null; // text DEFAULT NULL
    is_trainer: number | null; // tinyint(1) DEFAULT 0 (0 oder 1)
    komoot_id: string | null; // varchar(255) DEFAULT NULL (Komoot-ID für Aktivitäten, die aus Komoot importiert wurden)
}

export interface ProcessActivityOptions {
    userId?: number;                      // Die ID des Benutzers, der die Verarbeitung anfordert (wichtig für Strava API-Aufrufe, die im Kontext eines Benutzers erfolgen müssen)
    processStreams?: boolean;             // Sollen die Streams von Strava neu geladen und verarbeitet werden? (Standard: true)
    processElevation?: boolean;           // Sollen die Höhendaten (neu) berechnet werden? (Standard: true)
    processGpx?: boolean;                 // Soll eine verarbeitete GPX-Datei (neu) erstellt werden? (Standard: true)
    skipProcessedTrackIfExisting?: boolean; // Wenn true und eine verarbeitete Strecke bereits existiert, wird die Neuverarbeitung übersprungen (Standard: true oder false, je nach gewünschtem Verhalten)
    // Weitere mögliche Optionen könnten sein:
    // forceFullReprocess?: boolean;      // Erzwingt eine komplette Neuverarbeitung aller Schritte
    // targetGpxFilename?: string;        // Spezifischer Dateiname für die zu erstellende GPX-Datei
}

export interface ProcessActivityResult{
  success: boolean;
  message: string;
  errors?: any[];
  db_id?: number;
  activity_id?: number | null;
}


/**
 * Hauptinterface für eine Aktivität, wie sie im Code nach der Konvertierung
 * aus der Datenbank oder der API verwendet wird.
 */
export interface Activity extends Omit<ActivityForDb, 'start_date' | 'start_date_local' | 'created_at' | 'updated_at' | 'is_trainer' | 'user_id'> {
    user_id: number; // Sicherstellen, dass es hier als number (nicht optional) ist
    start_date: Date | null;
    start_date_local: Date | null;
    created_at?: Date | null; // Kann optional bleiben, wenn nicht immer gesetzt
    updated_at?: Date | null; // Kann optional bleiben
    is_trainer: boolean;
    komoot_id?: string | null; // Komoot-ID für Aktivitäten, die aus Komoot importiert wurden
    share_uuid?: string | null; // UUID für öffentliche Aktivitäts-Links
}

// Für DataQualityService
export interface UnrealisticSpeedActivity extends Partial<Activity> {
  max_speed_kmh?: string | null;
  average_speed_kmh?: string | null;
  check_reason?: string;
  threshold_type?: string;
}

export interface MissingPhotoFile {
  activity_strava_id: string | number;
  source_photo_id: string | null; // oder google_photo_id
  db_photo_id: number; // Primärschlüssel aus activity_photos
  missingSizes: string[]; // z.B. ['small', 'medium']
}

export interface DataQualityIssues {
  missingStreams?: Partial<Activity>[];
  gpxMissingFiles?: Partial<Activity>[];
  gpxShouldExist?: Partial<Activity>[];
  unrealisticSpeeds?: UnrealisticSpeedActivity[];
  inconsistentTimes?: Partial<Activity>[];
  missingCoordinates?: Partial<Activity>[];
  photoCountMismatches?: PhotoCountMismatch[];
  missingPhotoFiles?: MissingPhotoFile[];
  // Fehler bei einzelnen Checks
  missingStreams_error?: string;
  gpxInconsistencies_error?: string;
  unrealisticSpeeds_error?: string;
  inconsistentTimes_error?: string;
  missingCoordinates_error?: string;
  photoInconsistencies_error?: string;
}

export interface PhotoCountMismatch extends Pick<Activity, 'id' | 'strava_id' | 'activity_name' | 'start_date_local'> {
  expected_count: number | null;
  actual_count: number | null;
}

/**
 * Interface für Daten, wie sie direkt aus der 'activities'-Tabelle gelesen werden könnten,
 * bevor Konvertierungen (z.B. Datum-Strings zu Date-Objekten, Zahlen zu Booleans) stattfinden.
 */
export interface ActivityFromDB extends Omit<Activity, 'start_date' | 'start_date_local' | 'created_at' | 'updated_at' | 'is_trainer'> {
  start_date: string | null; // Kommt als String von der DB
  start_date_local: string | null; // Kommt als String von der DB
  created_at: string | null; // Kommt als String von der DB
  updated_at: string | null; // Kommt als String von der DB
  is_trainer: number | null; // 0 oder 1 aus der DB
  // Numerische Felder könnten auch als Strings kommen, je nach DB-Treiber/Query,
  // aber mysql2 liefert sie meist als Zahlen, wenn der DB-Typ numerisch ist.
  distance: number | string | null;
  total_elevation_gain: number | string | null;
  moving_time: number | string | null;
  elapsed_time: number | string | null;
  photo_count: number | string | null;
  gpx: number | string | null;
  komoot_id: string | null; // Komoot-ID für Aktivitäten, die aus Komoot importiert wurden
  share_uuid: string | null; // UUID für öffentliche Aktivitäts-Links
}

/**
 * Für eine kurze Zusammenfassung einer Aktivität.
 */
export type ActivitySummary = Pick<Activity, 'activity_name' | 'start_date_local' | 'distance' | 'total_elevation_gain' | 'moving_time'>;

/**
 * Daten, die für einen schnellen Status-Check einer Aktivität relevant sind.
 */
export interface ActivityCheckData {
  resource_state: number | null;
  photo_count: number | null; // photo_count aus der activities Tabelle
  device_watts: string | null; // Aus some_json
  has_heartrate: string | null; // Aus some_json (als 'true'/'false' String)
  average_cadence: string | null; // Aus some_json
  average_temp: string | null; // Aus some_json
  distance: number | null; // Aus activities Tabelle
  stream_state: string | null; // Erster Stream-Typ aus stream_json
  activityID: string | number | null; // strava_id
  gpx_file_status: number | null; // gpx Spalte (0 oder 1)
  stream_possible?: boolean; // Im Code berechnet
}

/**
 * Daten, die für die PI (Proportional-Integral) Controller Ansicht benötigt werden.
 * Enthält JSON-Strings, die im Frontend geparst werden.
 */
export interface ActivityPiData {
  processed_track_geojson: string | null;
  pause_intervals_json: string | null;
  downhill_segments_json: string | null;
}

/**
 * Ein Punkt im Höhenprofil (Distanz, Höhe).
 */
export interface ElevationPoint {
  d: number; // Distanz vom Start in Metern
  alt: number | null; // Höhe in Metern
}

/**
 * Ein vertikaler Ankerpunkt im Höhenprofil (z.B. Start/Ende eines Gefälles).
 */
export interface VerticalAnchor {
  d: number; // Distanz vom Start in Metern
  type: string; // z.B. 'EnterDownhill', 'ExitDownhill'
}

/**
 * Kombinierte Daten für das Höhenprofil-Diagramm.
 */
export interface ElevationAnchorData {
    elevationProfile: ElevationPoint[];
    verticalAnchors: VerticalAnchor[];
}

/**
 * Daten, die für die Generierung einer GPX-Datei aus einer Aktivität benötigt werden.
 */
export interface ActivityForGpx {
  id?: number;
  strava_id?: number | null;
  activity_name?: string | null;
  private_note?: string | null;
  start_date_local?: Date | null;
  start_date?: Date | null;
  stream_json: string | null;
  is_trainer?: boolean;
}

/**
 * Daten, die für das Karten-Popup einer Aktivität angezeigt werden.
 */
export interface ActivityForPopup extends Pick<Activity, 'strava_id' | 'activity_name' | 'start_date_local' | 'distance' | 'total_elevation_gain' | 'moving_time'> {
    id?: number; // Interne Datenbank-ID der Aktivität
    thumbnail_url?: string | null; // URL zum kleinen Vorschaubild
    full_image_url?: string | null;  // URL zum Originalbild (oder Medium)
    sport_type?: string | null; // Sport-Typ der Aktivität
}

/**
 * Properties für ein minimales GeoJSON Feature einer Aktivität.
 */
export interface MinimalActivityGeoJsonFeatureProperties {
    strava_id: string | number;
    sport_type: string | null;
    name?: string | null;
    date?: string | null; // ISO String
    distance?: number | null;
    elevation?: number | null;
    duration?: number | null;
    ownership?: 'own' | 'shared' | null; // Zur Unterscheidung im Popup
    activity_id?: number | null; // Interne Datenbank-ID der Aktivität
    // summary_polyline?: string | null; // Ist in geometry, nicht properties
}

/**
 * Typ für die Rohdaten, die von getActivitiesForCategoryGeoJson aus dem Repository kommen
 * und als Input für die Erstellung von GeoJSON Features im Controller dienen.
 */
export interface ActivityDataForGeoJsonFeature {
  id?: number; // Hinzugefügt für die Sportart-Obergruppen
  activity_id?: number; // Explizite Aktivitäts-ID
  strava_id: string | number;
  summary_polyline: string | null;
  sport_type: string | null;
  activity_name?: string | null; // NEU für Popup
  start_date_local?: Date | null; // NEU für Popup
  distance?: number | null; // NEU für Popup
  total_elevation_gain?: number | null; // NEU für Popup
  moving_time?: number | null; // NEU für Popup
  ownership_type?: 'own' | 'shared'; // NEU zur Unterscheidung
}

/**
 * Ein minimales GeoJSON Feature, das eine Aktivität repräsentiert (z.B. für Karten-Layer).
 */
export interface MinimalActivityGeoJson extends GeoJSON.Feature<GeoJSON.LineString, MinimalActivityGeoJsonFeatureProperties> {}


/**
 * Daten für die Anzeige einer Aktivität in einer Liste (z.B. Admin-Browser).
 */
export interface ActivityForListing extends Pick<Activity, 'id' | 'strava_id' | 'activity_name' | 'start_date_local' | 'sport_type' | 'gpx' | 'photo_count'> {
    user_id: number; // Benutzer-ID des Eigentümers
    distance_m: number | null; // Explizit als Meter
    elevation_gain_m: number | null; // Explizit als Meter
    has_downhill: boolean; // true, wenn downhill_segments_json nicht leer/null ist
}

/**
 * Struktur für aggregierte Jahresstatistiken.
 */
export interface UserYearlySummaryStat {
    year: number;
    total_count: number;
    total_distance: number;
    total_elevation: number;
    total_moving_time: number;
    count_fuss: number;
    distance_fuss: number;
    elevation_fuss: number;
    moving_time_fuss: number;
    count_rad: number;
    distance_rad: number;
    elevation_rad: number;
    moving_time_rad: number;
}

/**
 * Repräsentiert eine ähnliche Aktivität, die bereits in der Datenbank existiert.
 * Wird verwendet, um Duplikate zu vermeiden.
 */
export interface SimilarActivity {
    id: number;
    activity_name: string;
    start_date_local: Date;
    sport_type: string;
    distance: number;
    total_elevation_gain: number;
    source: string; // 'strava', 'komoot', 'garmin', etc.
    similarity_score: number; // 0-100, wobei 100 eine exakte Übereinstimmung ist
}


/**
 * Filteroptionen für das Auflisten von Aktivitäten.
 * Werte kommen oft als Strings aus req.query.
 */
export interface ActivityListFilters { // Dieser existiert wahrscheinlich schon so oder ähnlich
    searchTerm?: string;
    activityType?: string;
    dateFrom?: string;
    dateTo?: string;
    sylvie_filter?: 'yes' | 'no' | ''; // Für Admin-Seite ggf. noch relevant oder wird umgebaut
    dist_min?: string; // Admin-Controller übergibt Strings, Repo parst
    dist_max?: string;
    elev_min?: string;
    elev_max?: string;
}


// --- Typen für die interne Datenverarbeitung im activityProcessingService ---

/**
 * Ein Rohdatenpunkt aus den Strava Streams.
 */
export interface RawStreamPoint {
  lat: number | null;
  lon: number | null;
  alt: number | null;
  time: number; // Sekunden seit Start der Aktivität
}

/**
 * Ein verarbeiteter Datenpunkt (z.B. nach Pausenfilterung).
 */
export interface ProcessedPoint extends RawStreamPoint {}

/**
 * Repräsentiert ein Pausenintervall.
 */
export interface PauseInterval {
  start: number; // Sekunden seit Start
  end: number;   // Sekunden seit Start
}

/**
 * Repräsentiert ein vertikales Segment (Steigung, Gefälle, Flach).
 */
export interface VerticalSegment {
  type: 'Uphill' | 'Downhill' | 'Flat';
  startIndex: number; // Index im Array der verarbeiteten Punkte
  endIndex: number;
  startMeters: number; // Distanz vom Start des Tracks in Metern
  endMeters: number;
  startTime: number; // Zeit vom Start des Tracks in Sekunden
  endTime: number;
  lengthMeters: number;
  avgGradient: number | null;
}

/**
 * Repräsentiert ein identifiziertes Downhill-Segment.
 */
export interface DownhillSegment extends Pick<VerticalSegment, 'startTime' | 'endTime' | 'avgGradient'> {
  startDist: number; // Distanz in Metern vom Start des Tracks
  endDist: number;
  segmentDistance: number; // Länge des Segments in Metern
}

/**
 * Ergebnisobjekt der Hauptfunktion im activityProcessingService.
 */
export interface ActivityProcessingResult {
  success: boolean;
  message: string;
  errors?: string[];
}

/**
 * Typ für das Strava API Payload beim Einfügen/Aktualisieren von Aktivitäten.
 * Sollte idealerweise in src/types/strava.d.ts liegen und hier importiert werden.
 * Hier als Referenz, falls es noch nicht verschoben wurde.
 */
export interface StravaActivityPayload {
    id: number | string;
    user_id?: number | null; // Eigene user_id, nicht die von Strava
    name?: string | null;
    distance?: number | null;
    moving_time?: number | null;
    elapsed_time?: number | null;
    total_elevation_gain?: number | null;
    sport_type?: string | null;
    start_date?: string | null; // ISO8601
    start_date_local?: string | null; // ISO8601
    timezone?: string | null;
    start_latlng?: [number, number] | null; // [lat, lng]
    end_latlng?: [number, number] | null;   // [lat, lng]
    location_city?: string | null;
    location_state?: string | null;
    location_country?: string | null;
    map?: {
        id?: string | null;
        summary_polyline?: string | null;
        polyline?: string | null;
        resource_state?: number;
    } | null;
    trainer?: boolean | null;
    commute?: boolean | null;
    manual?: boolean | null;
    private?: boolean | null;
    visibility?: string | null;
    average_speed?: number | null; // m/s
    max_speed?: number | null; // m/s
    has_heartrate?: boolean | null;
    average_heartrate?: number | null;
    max_heartrate?: number | null;
    average_cadence?: number | null;
    average_temp?: number | null;
    average_watts?: number | null;
    max_watts?: number | null;
    weighted_average_watts?: number | null;
    kilojoules?: number | null;
    device_watts?: boolean | null;
    total_photo_count?: number | null;
    resource_state?: number;
    private_note?: string | null;
}
