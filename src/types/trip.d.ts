// src/types/trip.d.ts

export interface Trip {
  id?: number;
  user_id: number;
  share_uuid: string;
  title: string;
  description?: string | null;
  start_date?: string | null; // YYYY-MM-DD format
  end_date?: string | null; // YYYY-MM-DD format
  is_public: boolean;
  created_at?: Date | string;
  updated_at?: Date | string;
}

export interface TripActivity {
  id?: number;
  trip_id: number;
  activity_id: number;
  added_at?: Date | string;
}

export interface TripPlannedRoute {
  id?: number;
  trip_id: number;
  planned_route_id: number;
  added_at?: Date | string;
}

export interface TripPoi {
  id?: number;
  trip_id: number;
  poi_id: number;
  added_at?: Date | string;
}

// Erweiterte Interfaces für Anzeige mit verknüpften Daten
export interface TripWithStats {
  id: number;
  user_id: number;
  share_uuid: string;
  title: string;
  description?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  is_public: boolean;
  created_at: Date | string;
  updated_at: Date | string;
  // Statistiken
  activities_count: number;
  planned_routes_count: number;
  pois_count: number;
  total_distance?: number; // in meters
  total_elevation_gain?: number; // in meters
  total_moving_time?: number; // in seconds
}

export interface TripActivityDetail {
  trip_id: number;
  activity_id: number;
  activity_name: string;
  sport_type: string;
  distance: number;
  total_elevation_gain: number;
  moving_time: number;
  start_date_local: string;
  added_at: Date | string;
}

export interface TripPlannedRouteDetail {
  trip_id: number;
  planned_route_id: number;
  name: string;
  sport_type: string;
  distance_m: number;
  elevation_gain_m: number;
  duration: number;
  added_at: Date | string;
}

export interface TripPoiDetail {
  trip_id: number;
  poi_id: number;
  title: string;
  poi_type: string;
  latitude: number;
  longitude: number;
  description?: string | null;
  added_at: Date | string;
}

// Interface für die Erstellung/Bearbeitung von Reisen
export interface TripFormData {
  title: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  is_public: boolean;
}

// Interface für Reise-Statistiken (öffentliche Ansicht)
export interface TripPublicStats {
  activities_count: number;
  planned_routes_count: number;
  pois_count: number;
  total_distance: number; // in meters
  total_elevation_gain: number; // in meters
  total_moving_time: number; // in seconds
  total_activities: number;
}
