{"level":"error","message":"[ActivityRepo GetPopupDetails ID:1226] Error fetching photos: Error: Unknown column 'computed_url_small' in 'SELECT'","metadata":{},"module":"db/activityRepository.js","timestamp":"2025-06-02 08:54:36"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\"><PERSON><PERSON><PERSON></button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\"><PERSON>ser <PERSON> kann mit jedem geteilt werden, auch ohne <PERSON>dung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:28:20"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:28:46"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:31:36"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%=req.protocol%>://<%=req.get('host')%>/show/activity/<%=activity.share_uuid%>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%=req.protocol%>://<%=req.get('host')%>/show/activity/<%=activity.share_uuid%>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:25)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:35:19"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"::ffff:127.0.0.1","method":"GET","stack":"Error: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":21:17)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 11:31:25"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":21:17)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 11:31:32"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: /var/www/html/map/views/users/trips.ejs:23\n    21|         </div>\n    22| \n >> 23|         <%- include('partials/flash_messages') %>\n    24| \n    25|         <% if (trips && trips.length > 0) { %>\n    26|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":21:17)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 11:31:44"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:17\n    15|         </div>\n    16| \n >> 17|         <%- include('partials/flash_messages') %>\n    18| \n    19|         <% if (trips && trips.length > 0) { %>\n    20|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: /var/www/html/map/views/users/trips.ejs:17\n    15|         </div>\n    16| \n >> 17|         <%- include('partials/flash_messages') %>\n    18| \n    19|         <% if (trips && trips.length > 0) { %>\n    20|             <div class=\"table-container\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":22:17)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 11:34:51"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_edit.ejs:27\n    25|         </div>\n    26| \n >> 27|         <%- include('partials/flash_messages') %>\n    28| \n    29|         <div class=\"form-container\">\n    30|             <form method=\"POST\" action=\"<%= isEdit ? `/user/trip/${trip.id}/edit` : '/user/trip' %>\" class=\"trip-form\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: /var/www/html/map/views/users/trip_edit.ejs:27\n    25|         </div>\n    26| \n >> 27|         <%- include('partials/flash_messages') %>\n    28| \n    29|         <div class=\"form-container\">\n    30|             <form method=\"POST\" action=\"<%= isEdit ? `/user/trip/${trip.id}/edit` : '/user/trip' %>\" class=\"trip-form\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trip_edit.ejs\":33:17)\n    at trip_edit (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip/new"},"module":"server.js","timestamp":"2025-06-02 11:35:31"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_detail.ejs:29\n    27|         </div>\n    28| \n >> 29|         <%- include('partials/flash_messages') %>\n    30| \n    31|         <!-- Reise-Informationen -->\n    32|         <div class=\"trip-info-card\">\n\nCould not find the include file \"partials/flash_messages\"","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: /var/www/html/map/views/users/trip_detail.ejs:29\n    27|         </div>\n    28| \n >> 29|         <%- include('partials/flash_messages') %>\n    30| \n    31|         <!-- Reise-Informationen -->\n    32|         <div class=\"trip-info-card\">\n\nCould not find the include file \"partials/flash_messages\"\n    at getIncludePath (/var/www/html/map/node_modules/ejs/lib/ejs.js:185:13)\n    at includeFile (/var/www/html/map/node_modules/ejs/lib/ejs.js:311:19)\n    at include (/var/www/html/map/node_modules/ejs/lib/ejs.js:701:16)\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":39:17)\n    at trip_detail (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)","url":"/user/trip/1"},"module":"server.js","timestamp":"2025-06-02 11:37:09"}
{"level":"error","message":"Unknown column 'a.gpx_filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n      SELECT\n        a.id,\n        a.activity_name,\n        a.sport_type,\n        a.distance,\n        a.start_date_local,\n        a.gpx_filename\n      FROM trip_activities ta\n      JOIN activities a ON ta.activity_id = a.id\n      WHERE ta.trip_id = ? AND a.gpx_filename IS NOT NULL\n      ORDER BY a.start_date_local DESC\n    ","sqlMessage":"Unknown column 'a.gpx_filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'a.gpx_filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripGeoData (/var/www/html/map/src/db/tripRepository.ts:530:12)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:74:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:20:55"}
{"level":"error","message":"Unknown column 'a.gpx_filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n      SELECT\n        a.id,\n        a.activity_name,\n        a.sport_type,\n        a.distance,\n        a.start_date_local,\n        a.gpx_filename\n      FROM trip_activities ta\n      JOIN activities a ON ta.activity_id = a.id\n      WHERE ta.trip_id = ? AND a.gpx_filename IS NOT NULL\n      ORDER BY a.start_date_local DESC\n    ","sqlMessage":"Unknown column 'a.gpx_filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'a.gpx_filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripGeoData (/var/www/html/map/src/db/tripRepository.ts:530:12)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:74:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"controllers/tripController.js","timestamp":"2025-06-02 12:20:55"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: Unknown column 'a.gpx_filename' in 'SELECT'","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: Unknown column 'a.gpx_filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripGeoData (/var/www/html/map/src/db/tripRepository.ts:530:12)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:74:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 12:20:55"}
{"level":"error","message":"Unknown column 'ap.filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n    SELECT\n      ap.id,\n      ap.activity_id,\n      ap.filename,\n      ap.caption,\n      ap.uploaded_at,\n      a.activity_name,\n      a.start_date_local\n    FROM trip_activities ta\n    JOIN activities a ON ta.activity_id = a.id\n    JOIN activity_photos ap ON a.id = ap.activity_id\n    WHERE ta.trip_id = ?\n    ORDER BY a.start_date_local DESC, ap.uploaded_at DESC\n    LIMIT ?\n  ","sqlMessage":"Unknown column 'ap.filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:20:55"}
{"level":"error","message":"Unknown column 'ap.filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n    SELECT\n      ap.id,\n      ap.activity_id,\n      ap.filename,\n      ap.caption,\n      ap.uploaded_at,\n      a.activity_name,\n      a.start_date_local\n    FROM trip_activities ta\n    JOIN activities a ON ta.activity_id = a.id\n    JOIN activity_photos ap ON a.id = ap.activity_id\n    WHERE ta.trip_id = ?\n    ORDER BY a.start_date_local DESC, ap.uploaded_at DESC\n    LIMIT ?\n  ","sqlMessage":"Unknown column 'ap.filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:21:07"}
{"level":"error","message":"Unknown column 'ap.filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n    SELECT\n      ap.id,\n      ap.activity_id,\n      ap.filename,\n      ap.caption,\n      ap.uploaded_at,\n      a.activity_name,\n      a.start_date_local\n    FROM trip_activities ta\n    JOIN activities a ON ta.activity_id = a.id\n    JOIN activity_photos ap ON a.id = ap.activity_id\n    WHERE ta.trip_id = ?\n    ORDER BY a.start_date_local DESC, ap.uploaded_at DESC\n    LIMIT ?\n  ","sqlMessage":"Unknown column 'ap.filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"controllers/tripController.js","timestamp":"2025-06-02 12:21:07"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: Unknown column 'ap.filename' in 'SELECT'","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 12:21:07"}
{"level":"error","message":"Unknown column 'a.gpx_filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n      SELECT\n        a.id,\n        a.activity_name,\n        a.sport_type,\n        a.distance,\n        a.start_date_local,\n        a.gpx_filename\n      FROM trip_activities ta\n      JOIN activities a ON ta.activity_id = a.id\n      WHERE ta.trip_id = ? AND a.gpx_filename IS NOT NULL\n      ORDER BY a.start_date_local DESC\n    ","sqlMessage":"Unknown column 'a.gpx_filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'a.gpx_filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripGeoData (/var/www/html/map/src/db/tripRepository.ts:530:12)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:74:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:21:07"}
{"level":"error","message":"Unknown column 'ap.filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n    SELECT\n      ap.id,\n      ap.activity_id,\n      ap.filename,\n      ap.caption,\n      ap.uploaded_at,\n      a.activity_name,\n      a.start_date_local\n    FROM trip_activities ta\n    JOIN activities a ON ta.activity_id = a.id\n    JOIN activity_photos ap ON a.id = ap.activity_id\n    WHERE ta.trip_id = ?\n    ORDER BY a.start_date_local DESC, ap.uploaded_at DESC\n    LIMIT ?\n  ","sqlMessage":"Unknown column 'ap.filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:25:08"}
{"level":"error","message":"Unknown column 'ap.filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n    SELECT\n      ap.id,\n      ap.activity_id,\n      ap.filename,\n      ap.caption,\n      ap.uploaded_at,\n      a.activity_name,\n      a.start_date_local\n    FROM trip_activities ta\n    JOIN activities a ON ta.activity_id = a.id\n    JOIN activity_photos ap ON a.id = ap.activity_id\n    WHERE ta.trip_id = ?\n    ORDER BY a.start_date_local DESC, ap.uploaded_at DESC\n    LIMIT ?\n  ","sqlMessage":"Unknown column 'ap.filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"controllers/tripController.js","timestamp":"2025-06-02 12:25:08"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: Unknown column 'ap.filename' in 'SELECT'","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"Error: Unknown column 'ap.filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripImages (/var/www/html/map/src/db/tripRepository.ts:469:34)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:73:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 12:25:08"}
{"level":"error","message":"Unknown column 'a.gpx_filename' in 'SELECT'","metadata":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n      SELECT\n        a.id,\n        a.activity_name,\n        a.sport_type,\n        a.distance,\n        a.start_date_local,\n        a.gpx_filename\n      FROM trip_activities ta\n      JOIN activities a ON ta.activity_id = a.id\n      WHERE ta.trip_id = ? AND a.gpx_filename IS NOT NULL\n      ORDER BY a.start_date_local DESC\n    ","sqlMessage":"Unknown column 'a.gpx_filename' in 'SELECT'","sqlState":"42S22","stack":"Error: Unknown column 'a.gpx_filename' in 'SELECT'\n    at PromisePool.execute (/var/www/html/map/node_modules/mysql2/lib/promise/pool.js:54:22)\n    at Object.getTripGeoData (/var/www/html/map/src/db/tripRepository.ts:530:12)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:74:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"},"module":"db/tripRepository.js","timestamp":"2025-06-02 12:25:08"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_detail.ejs:119\n    117|                         <div class=\"images-container\">\n    118|                             <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;\">\n >> 119|                                 <h3 style=\"margin: 0;\">Bilder der Reise (<%= totalImages || images.length %>)</h3>\n    120|                                 <% if (totalImages > 12) { %>\n    121|                                     <button id=\"loadMoreImagesBtn\" class=\"button button-secondary\" style=\"padding: 8px 12px; font-size: 0.9em;\">\n    122|                                         Mehr laden (<%= totalImages - 12 %> weitere)\n\ntotalImages is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trip_detail.ejs:119\n    117|                         <div class=\"images-container\">\n    118|                             <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;\">\n >> 119|                                 <h3 style=\"margin: 0;\">Bilder der Reise (<%= totalImages || images.length %>)</h3>\n    120|                                 <% if (totalImages > 12) { %>\n    121|                                     <button id=\"loadMoreImagesBtn\" class=\"button button-secondary\" style=\"padding: 8px 12px; font-size: 0.9em;\">\n    122|                                         Mehr laden (<%= totalImages - 12 %> weitere)\n\ntotalImages is not defined\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":148:26)\n    at trip_detail (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showTripDetail (/var/www/html/map/src/controllers/tripController.ts:79:9)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 13:12:38"}
{"level":"error","message":"[ActivityRepo GetPopupDetails ID:1035] Error fetching photos: Error: Unknown column 'computed_url_small' in 'SELECT'","metadata":{},"module":"db/activityRepository.js","timestamp":"2025-06-02 14:02:39"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:93\n    91|             <div class=\"mobile-cards-container\">\n    92|                 <% trips.forEach(trip => { %>\n >> 93|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    94|                         <div class=\"mobile-card-header\">\n    95|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"mobile-card-title\" title=\"Details für '<%= trip.description.substring(0, 100) %>'\">\n    96|                                 <%= trip.description.substring(0, 100) %>\n\nactivity is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trips.ejs:93\n    91|             <div class=\"mobile-cards-container\">\n    92|                 <% trips.forEach(trip => { %>\n >> 93|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    94|                         <div class=\"mobile-card-header\">\n    95|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"mobile-card-title\" title=\"Details für '<%= trip.description.substring(0, 100) %>'\">\n    96|                                 <%= trip.description.substring(0, 100) %>\n\nactivity is not defined\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":133:27)\n    at Array.forEach (<anonymous>)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":130:14)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 15:18:19"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trips.ejs:93\n    91|             <div class=\"mobile-cards-container\">\n    92|                 <% trips.forEach(trip => { %>\n >> 93|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    94|                         <div class=\"mobile-card-header\">\n    95|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"mobile-card-title\" title=\"Details für '<%= trip.description.substring(0, 100) %>'\">\n    96|                                 <%= trip.description.substring(0, 100) %>\n\nactivity is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trips.ejs:93\n    91|             <div class=\"mobile-cards-container\">\n    92|                 <% trips.forEach(trip => { %>\n >> 93|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    94|                         <div class=\"mobile-card-header\">\n    95|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"mobile-card-title\" title=\"Details für '<%= trip.description.substring(0, 100) %>'\">\n    96|                                 <%= trip.description.substring(0, 100) %>\n\nactivity is not defined\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":133:27)\n    at Array.forEach (<anonymous>)\n    at eval (\"/var/www/html/map/views/users/trips.ejs\":130:14)\n    at trips (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)","url":"/user/trip"},"module":"server.js","timestamp":"2025-06-02 15:19:15"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_detail.ejs:366\n    364|             <div class=\"mobile-cards-container\">\n    365|                 <% pois.forEach(poi => { %>\n >> 366|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    367|                         <div class=\"mobile-card-header\">\n    368|                             <a href=\"/user/poi/<%= poi.poi_id %>\" class=\"mobile-card-title\"><%= poi.title %></a>                                \n    369|                         </div>\n\nactivity is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trip_detail.ejs:366\n    364|             <div class=\"mobile-cards-container\">\n    365|                 <% pois.forEach(poi => { %>\n >> 366|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    367|                         <div class=\"mobile-card-header\">\n    368|                             <a href=\"/user/poi/<%= poi.poi_id %>\" class=\"mobile-card-title\"><%= poi.title %></a>                                \n    369|                         </div>\n\nactivity is not defined\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":475:27)\n    at Array.forEach (<anonymous>)\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":472:13)\n    at trip_detail (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 16:21:04"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_detail.ejs:366\n    364|             <div class=\"mobile-cards-container\">\n    365|                 <% pois.forEach(poi => { %>\n >> 366|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    367|                         <div class=\"mobile-card-header\">\n    368|                             <a href=\"/user/poi/<%= poi.poi_id %>\" class=\"mobile-card-title\"><%= poi.title %></a>                                \n    369|                         </div>\n\nactivity is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trip_detail.ejs:366\n    364|             <div class=\"mobile-cards-container\">\n    365|                 <% pois.forEach(poi => { %>\n >> 366|                     <div class=\"mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>\">\n    367|                         <div class=\"mobile-card-header\">\n    368|                             <a href=\"/user/poi/<%= poi.poi_id %>\" class=\"mobile-card-title\"><%= poi.title %></a>                                \n    369|                         </div>\n\nactivity is not defined\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":475:27)\n    at Array.forEach (<anonymous>)\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":472:13)\n    at trip_detail (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 16:22:27"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/trip_detail.ejs:379\n    377| \n    378|                         <div class=\"mobile-card-actions\">\n >> 379|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"button-primary\" title=\"Details ansehen\">Details</a>\n    380|                             <% if (isOwner) { %>\n    381|                                 <form action=\"/user/trip/<%= trip.id %>/remove-activity\" method=\"POST\" style=\"display: inline;\">\n    382|                                     <input type=\"hidden\" name=\"activityId\" value=\"<%= activity.activity_id %>\">\n\nactivity is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/trip_detail.ejs:379\n    377| \n    378|                         <div class=\"mobile-card-actions\">\n >> 379|                             <a href=\"/user/activity/<%= activity.id %>\" class=\"button-primary\" title=\"Details ansehen\">Details</a>\n    380|                             <% if (isOwner) { %>\n    381|                                 <form action=\"/user/trip/<%= trip.id %>/remove-activity\" method=\"POST\" style=\"display: inline;\">\n    382|                                     <input type=\"hidden\" name=\"activityId\" value=\"<%= activity.activity_id %>\">\n\nactivity is not defined\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":483:26)\n    at Array.forEach (<anonymous>)\n    at eval (\"/var/www/html/map/views/users/trip_detail.ejs\":472:13)\n    at trip_detail (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)","url":"/user/trip/2"},"module":"server.js","timestamp":"2025-06-02 16:22:52"}
