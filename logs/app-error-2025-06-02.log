{"level":"error","message":"[ActivityRepo GetPopupDetails ID:1226] Error fetching photos: Error: Unknown column 'computed_url_small' in 'SELECT'","metadata":{},"module":"db/activityRepository.js","timestamp":"2025-06-02 08:54:36"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\"><PERSON><PERSON><PERSON></button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\"><PERSON>ser <PERSON> kann mit jedem geteilt werden, auch ohne <PERSON>dung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:28:20"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:28:46"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%= `${req.protocol}://${req.get('host')}/show/activity/${activity.share_uuid}` %>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:29)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:31:36"}
{"level":"error","message":"[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%=req.protocol%>://<%=req.get('host')%>/show/activity/<%=activity.share_uuid%>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined","metadata":{"ip":"2003:c2:af03:dabe:5931:f20d:b8b7:a273","method":"GET","stack":"ReferenceError: /var/www/html/map/views/users/activity.ejs:124\n    122|                         <dd>\r\n    123|                             <div class=\"share-link-container\">\r\n >> 124|                                 <input type=\"text\" id=\"shareLink\" value=\"<%=req.protocol%>://<%=req.get('host')%>/show/activity/<%=activity.share_uuid%>\" readonly class=\"share-link-input\">\r\n    125|                                 <button type=\"button\" id=\"copyShareLinkBtn\" class=\"button-link-small\">Kopieren</button>\r\n    126|                             </div>\r\n    127|                             <small class=\"share-link-note\">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>\r\n\nreq is not defined\n    at eval (\"/var/www/html/map/views/users/activity.ejs\":131:25)\n    at activity (/var/www/html/map/node_modules/ejs/lib/ejs.js:703:17)\n    at tryHandleCache (/var/www/html/map/node_modules/ejs/lib/ejs.js:274:36)\n    at View.exports.renderFile [as engine] (/var/www/html/map/node_modules/ejs/lib/ejs.js:491:10)\n    at View.render (/var/www/html/map/node_modules/express/lib/view.js:135:8)\n    at tryRender (/var/www/html/map/node_modules/express/lib/application.js:657:10)\n    at Function.render (/var/www/html/map/node_modules/express/lib/application.js:609:3)\n    at ServerResponse.render (/var/www/html/map/node_modules/express/lib/response.js:1049:7)\n    at ServerResponse.res.render (/var/www/html/map/node_modules/express-ejs-layouts/lib/express-layouts.js:77:18)\n    at showUserActivityDetailPage (/var/www/html/map/src/controllers/userController.ts:940:13)","url":"/user/activity/1228"},"module":"server.js","timestamp":"2025-06-02 09:35:19"}
