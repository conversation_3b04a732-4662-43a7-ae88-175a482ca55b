"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateShareUuid = generateShareUuid;
exports.migrateExistingActivitiesWithUuids = migrateExistingActivitiesWithUuids;
exports.ensureShareUuid = ensureShareUuid;
// src/utils/shareUuidUtils.ts
const crypto_1 = require("crypto");
const connection_1 = __importDefault(require("../db/connection"));
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Generiert eine neue UUID für das Teilen von Aktivitäten
 */
function generateShareUuid() {
    return (0, crypto_1.randomUUID)();
}
/**
 * <PERSON><PERSON>t UUIDs für alle bestehenden Aktivitäten, die noch keine haben
 */
async function migrateExistingActivitiesWithUuids() {
    const fnLogPrefix = '[Utils MigrateShareUuids]';
    try {
        // Zähle Aktivitäten ohne UUID
        const [countResult] = await connection_1.default.execute('SELECT COUNT(*) as count FROM activities WHERE share_uuid IS NULL');
        const totalToUpdate = countResult[0].count;
        if (totalToUpdate === 0) {
            log.info(`${fnLogPrefix} Keine Aktivitäten ohne UUID gefunden.`);
            return { success: true, message: 'Alle Aktivitäten haben bereits UUIDs', updated: 0 };
        }
        log.info(`${fnLogPrefix} Starte Migration für ${totalToUpdate} Aktivitäten...`);
        // Hole alle Aktivitäten ohne UUID
        const [activities] = await connection_1.default.execute('SELECT id FROM activities WHERE share_uuid IS NULL');
        let updated = 0;
        // Update jede Aktivität einzeln mit einer neuen UUID
        for (const activity of activities) {
            const uuid = generateShareUuid();
            try {
                const [result] = await connection_1.default.execute('UPDATE activities SET share_uuid = ? WHERE id = ?', [uuid, activity.id]);
                if (result.affectedRows > 0) {
                    updated++;
                }
            }
            catch (error) {
                // Falls UUID bereits existiert (sehr unwahrscheinlich), versuche eine neue
                if (error.code === 'ER_DUP_ENTRY') {
                    log.warn(`${fnLogPrefix} UUID-Kollision für Aktivität ${activity.id}, versuche neue UUID...`);
                    const newUuid = generateShareUuid();
                    const [retryResult] = await connection_1.default.execute('UPDATE activities SET share_uuid = ? WHERE id = ?', [newUuid, activity.id]);
                    if (retryResult.affectedRows > 0) {
                        updated++;
                    }
                }
                else {
                    log.error(`${fnLogPrefix} Fehler beim Update von Aktivität ${activity.id}:`, error);
                }
            }
        }
        log.info(`${fnLogPrefix} Migration abgeschlossen. ${updated} von ${totalToUpdate} Aktivitäten aktualisiert.`);
        return {
            success: true,
            message: `Migration erfolgreich: ${updated} von ${totalToUpdate} Aktivitäten mit UUIDs versehen`,
            updated
        };
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler bei der Migration:`, error);
        return {
            success: false,
            message: `Fehler bei der Migration: ${error.message}`,
            updated: 0
        };
    }
}
/**
 * Generiert eine UUID für eine neue Aktivität, falls noch keine vorhanden ist
 */
function ensureShareUuid(existingUuid) {
    return existingUuid || generateShareUuid();
}
//# sourceMappingURL=shareUuidUtils.js.map