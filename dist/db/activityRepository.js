"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listActivitiesForUser = listActivitiesForUser;
exports.countActivitiesForUser = countActivitiesForUser;
exports.isActivitySharedWithUser = isActivitySharedWithUser;
exports.getActivitiesForUserMap = getActivitiesForUserMap;
exports.getDistinctActivityTypesForUser = getDistinctActivityTypesForUser;
exports.getDistinctActivityYearsForUser = getDistinctActivityYearsForUser;
exports.getActivitySummaryById = getActivitySummaryById;
exports.getActivityByPrimaryKey = getActivityByPrimaryKey;
exports.getActivityByStravaId = getActivityByStravaId;
exports.getActivityByShareUuid = getActivityByShareUuid;
exports.decrementActivityPhotoCount = decrementActivityPhotoCount;
exports.incrementActivityPhotoCount = incrementActivityPhotoCount;
exports.getProcessedTrackJsonById = getProcessedTrackJsonById;
exports.findActivitiesMissingProcessing = findActivitiesMissingProcessing;
exports.setGpxStatus = setGpxStatus;
exports.setResource_state = setResource_state;
exports.updateActivityPhotoCount = updateActivityPhotoCount;
exports.findActivitiesWithMissingCoords = findActivitiesWithMissingCoords;
exports.findActivitiesWithGpxStatusSet = findActivitiesWithGpxStatusSet;
exports.getActivitiesWithGpxForUser = getActivitiesWithGpxForUser;
exports.findActivitiesWithStreamsButNoGpx = findActivitiesWithStreamsButNoGpx;
exports.findActivitiesWithMissingStreams = findActivitiesWithMissingStreams;
exports.getActivityPiDataById = getActivityPiDataById;
exports.getYearlySummaryStatsForUser = getYearlySummaryStatsForUser;
exports.getElevationAndVerticalAnchors = getElevationAndVerticalAnchors;
exports.deleteActivityById = deleteActivityById;
exports.getMonthlyStats = getMonthlyStats;
exports.checkActivityExists = checkActivityExists;
exports.insertUserUploadedActivity = insertUserUploadedActivity;
exports.insertActivityFromStrava = insertActivityFromStrava;
exports.updateActivityStreamJson = updateActivityStreamJson;
exports.getActivityCheckData = getActivityCheckData;
exports.getActivityCheckDataByStravaId = getActivityCheckDataByStravaId;
exports.getActivityPopupDetails = getActivityPopupDetails;
exports.shareActivityWithUser = shareActivityWithUser;
exports.deleteSharedEntriesForActivity = deleteSharedEntriesForActivity;
exports.getActivitiesForCategoryGeoJson = getActivitiesForCategoryGeoJson;
exports.getDistinctActivityTypes = getDistinctActivityTypes;
exports.getDistinctActivityYears = getDistinctActivityYears;
exports.getActivitiesForGeoJson = getActivitiesForGeoJson;
exports.listActivities = listActivities;
exports.countActivities = countActivities;
exports.getStreamJsonById = getStreamJsonById;
exports.getActivityWithStreams = getActivityWithStreams;
exports.updateActivityFromStrava = updateActivityFromStrava;
exports.updateActivityProcessingResults = updateActivityProcessingResults;
exports.findActivitiesWithUnrealisticSpeeds = findActivitiesWithUnrealisticSpeeds;
exports.findActivitiesWithInconsistentTimes = findActivitiesWithInconsistentTimes;
exports.findActivitiesWithPhotoCountMismatch = findActivitiesWithPhotoCountMismatch;
exports.getActivitiesForYearGroup = getActivitiesForYearGroup;
exports.getActivitiesForYearType = getActivitiesForYearType;
exports.getActivitiesInDateRange = getActivitiesInDateRange;
// src/db/activityRepository.ts
const connection_1 = __importDefault(require("./connection"));
const logger_1 = __importDefault(require("../utils/logger"));
const config_1 = __importDefault(require("../config/config"));
const geoGpxUtils_1 = require("../utils/geoGpxUtils");
const shareUuidUtils_1 = require("../utils/shareUuidUtils");
const log = logger_1.default.getLogger(__filename);
// --- Interne Hilfsfunktionen ---
function formatToMySQLDateTime(dateInput) {
    if (!dateInput)
        return null;
    try {
        const date = (typeof dateInput === 'string') ? new Date(dateInput) : dateInput;
        if (isNaN(date.getTime()))
            return null;
        return date.toISOString().slice(0, 19).replace('T', ' ');
    }
    catch (e) {
        log.warn(`Konnte Datum nicht formatieren: ${dateInput}`, e);
        return null;
    }
}
/**
 * Listet Aktivitäten für einen bestimmten Benutzer auf (eigene und mit ihm geteilte).
 * Berücksichtigt Filter, Sortierung und Paginierung.
 */
async function listActivitiesForUser(userId, options = {}) {
    const { filters = {}, sortBy = 'date', sortOrder = 'DESC', page = 1, limit = 25 } = options;
    const fnLogPrefix = `[Repo ListActForUser:${userId}]`;
    let mainQuery = `
        SELECT
            a.id, a.strava_id, a.activity_name, a.start_date_local, a.sport_type,
            a.distance AS distance_m, a.total_elevation_gain AS elevation_gain_m,
            a.gpx, a.share_uuid,
            (SELECT COUNT(*) FROM activity_photos ap WHERE ap.activity_id = a.id) as photo_count_from_db,
            (a.user_id = ?) AS is_owned_by_user
        FROM activities a
    `;
    const queryParams = [userId];
    let whereClauses = [];
    if (filters.participation === 'owned') {
        mainQuery += ` WHERE a.user_id = ? `;
        queryParams.push(userId);
    }
    else if (filters.participation === 'shared_with_me') {
        mainQuery += ` JOIN shared_activities sa ON a.id = sa.activity_id AND sa.shared_with_user_id = ? `;
        queryParams.push(userId);
    }
    else {
        mainQuery += ` LEFT JOIN shared_activities sa ON a.id = sa.activity_id `;
        whereClauses.push(`(a.user_id = ? OR sa.shared_with_user_id = ?)`);
        queryParams.push(userId, userId);
    }
    const filterParams = [];
    if (filters.searchTerm) {
        whereClauses.push(`(CAST(a.strava_id AS CHAR) = ? OR a.activity_name LIKE ?)`);
        filterParams.push(filters.searchTerm, `%${filters.searchTerm}%`);
    }
    if (filters.activityType) {
        whereClauses.push(`a.sport_type = ?`);
        filterParams.push(filters.activityType);
    }
    if (filters.dateFrom) {
        whereClauses.push(`DATE(a.start_date_local) >= ?`);
        filterParams.push(filters.dateFrom);
    }
    if (filters.dateTo) {
        whereClauses.push(`DATE(a.start_date_local) <= ?`);
        filterParams.push(filters.dateTo);
    }
    if (filters.distMin !== undefined) {
        whereClauses.push(`a.distance >= ?`);
        filterParams.push(filters.distMin * 1000);
    } // Annahme: Filter ist in km
    if (filters.distMax !== undefined) {
        whereClauses.push(`a.distance <= ?`);
        filterParams.push(filters.distMax * 1000);
    } // Annahme: Filter ist in km
    if (filters.elevMin !== undefined) {
        whereClauses.push(`a.total_elevation_gain >= ?`);
        filterParams.push(filters.elevMin);
    }
    if (filters.elevMax !== undefined) {
        whereClauses.push(`a.total_elevation_gain <= ?`);
        filterParams.push(filters.elevMax);
    }
    queryParams.push(...filterParams);
    if (whereClauses.length > 0) {
        if (filters.participation === 'owned' || filters.participation === 'shared_with_me') {
            mainQuery += ` AND ${whereClauses.filter((_, i) => i > 0 || filters.participation === 'owned').join(' AND ')}`; // Erste whereClause ist schon Teil des Joins/Haupt-Where
        }
        else {
            mainQuery += ` WHERE ${whereClauses.join(' AND ')}`;
        }
    }
    if (filters.activityType !== 'VirtualRide' && (!filters.activityType || filters.activityType.toLowerCase() !== 'virtualride')) { // Sicherstellen, dass VirtualRide nicht explizit gefiltert wird
        mainQuery += ` AND (a.sport_type != 'VirtualRide' OR a.sport_type IS NULL) `;
    }
    const sortColumnMap = {
        'name': 'a.activity_name', 'date': 'a.start_date_local', 'type': 'a.sport_type',
        'distance': 'a.distance', 'elevation': 'a.total_elevation_gain',
        'gpx_status': 'a.gpx', 'photos': 'photo_count_from_db'
    };
    const sortCol = sortColumnMap[sortBy] || 'a.start_date_local';
    const orderDir = sortOrder?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    mainQuery += ` ORDER BY ${sortCol} ${orderDir}, a.id ${orderDir}`;
    const offset = (page - 1) * limit;
    mainQuery += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);
    try {
        const [results] = await connection_1.default.query(mainQuery, queryParams);
        return results.map(row => ({
            id: row.id, strava_id: row.strava_id, activity_name: row.activity_name,
            start_date_local: row.start_date_local ? new Date(row.start_date_local) : null,
            sport_type: row.sport_type, distance_m: row.distance_m, elevation_gain_m: row.elevation_gain_m,
            gpx_status: row.gpx, // Füge gpx_status hinzu
            photo_count_from_db: parseInt(row.photo_count_from_db, 10) || 0,
            is_owned_by_user: !!row.is_owned_by_user, // Konvertiere zu boolean
            share_uuid: row.share_uuid
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error listing activities for user:`, error);
        throw error;
    }
}
async function countActivitiesForUser(userId, filters = {}) {
    const fnLogPrefix = `[Repo CountActForUser:${userId}]`;
    let countSql = `SELECT COUNT(DISTINCT a.id) as total FROM activities a`;
    const queryParams = [];
    let whereClauses = [];
    if (filters.participation === 'owned') {
        whereClauses.push(`a.user_id = ?`);
        queryParams.push(userId);
    }
    else if (filters.participation === 'shared_with_me') {
        countSql += ` JOIN shared_activities sa ON a.id = sa.activity_id `;
        whereClauses.push(`sa.shared_with_user_id = ?`);
        queryParams.push(userId);
        // Optional: whereClauses.push(`a.user_id != ?`); queryParams.push(userId);
    }
    else {
        countSql += ` LEFT JOIN shared_activities sa ON a.id = sa.activity_id `;
        whereClauses.push(`(a.user_id = ? OR sa.shared_with_user_id = ?)`);
        queryParams.push(userId, userId);
    }
    // Restliche Filter (exakt wie in listActivitiesForUser)
    if (filters.searchTerm) {
        whereClauses.push(`(CAST(a.strava_id AS CHAR) = ? OR a.activity_name LIKE ?)`);
        queryParams.push(filters.searchTerm, `%${filters.searchTerm}%`);
    }
    if (filters.activityType) {
        whereClauses.push(`a.sport_type = ?`);
        queryParams.push(filters.activityType);
    }
    if (filters.dateFrom) {
        whereClauses.push(`DATE(a.start_date_local) >= ?`);
        queryParams.push(filters.dateFrom);
    }
    if (filters.dateTo) {
        whereClauses.push(`DATE(a.start_date_local) <= ?`);
        queryParams.push(filters.dateTo);
    }
    if (filters.distMin !== undefined) {
        whereClauses.push(`a.distance >= ?`);
        queryParams.push(filters.distMin * 1000);
    }
    if (filters.distMax !== undefined) {
        whereClauses.push(`a.distance <= ?`);
        queryParams.push(filters.distMax * 1000);
    }
    if (filters.elevMin !== undefined) {
        whereClauses.push(`a.total_elevation_gain >= ?`);
        queryParams.push(filters.elevMin);
    }
    if (filters.elevMax !== undefined) {
        whereClauses.push(`a.total_elevation_gain <= ?`);
        queryParams.push(filters.elevMax);
    }
    if (whereClauses.length > 0) {
        countSql += ` WHERE ${whereClauses.join(' AND ')}`;
    }
    // VirtualRide-Filter (angepasst, um die WHERE-Klausel korrekt zu erweitern)
    const virtualRideCondition = `(a.sport_type != 'VirtualRide' OR a.sport_type IS NULL)`;
    if (filters.activityType !== 'VirtualRide' && (!filters.activityType || filters.activityType.toLowerCase() !== 'virtualride')) {
        if (whereClauses.length > 0) {
            countSql += ` AND ${virtualRideCondition}`;
        }
        else {
            countSql += ` WHERE ${virtualRideCondition}`;
        }
    }
    log.debug(`${fnLogPrefix} Count SQL: ${countSql.replace(/\s\s+/g, ' ')}`);
    log.debug(`${fnLogPrefix} Count Params: ${JSON.stringify(queryParams)}`);
    try {
        const [results] = await connection_1.default.query(countSql, queryParams);
        return results[0].total || 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error counting activities for user:`, error);
        throw error;
    }
}
// mapDbRowToActivity Funktion (Beispiel, passen Sie sie an Ihre genaue Definition an)
function mapDbRowToActivity(dbRow) {
    if (!dbRow)
        return null;
    return {
        id: dbRow.id,
        user_id: dbRow.user_id,
        strava_id: dbRow.strava_id,
        activity_name: dbRow.activity_name,
        sport_type: dbRow.sport_type,
        distance: dbRow.distance,
        total_elevation_gain: dbRow.total_elevation_gain,
        moving_time: dbRow.moving_time,
        elapsed_time: dbRow.elapsed_time,
        start_lat: dbRow.start_lat,
        start_lng: dbRow.start_lng,
        end_lat: dbRow.end_lat,
        end_lng: dbRow.end_lng,
        location_country: dbRow.location_country,
        private_note: dbRow.private_note,
        summary_polyline: dbRow.summary_polyline,
        detailed_polyline: dbRow.detailed_polyline,
        average_speed: dbRow.average_speed,
        max_speed: dbRow.max_speed,
        average_heartrate: dbRow.average_heartrate,
        max_heartrate: dbRow.max_heartrate,
        photo_count: dbRow.photo_count,
        resource_state: dbRow.resource_state,
        some_json: dbRow.some_json,
        stream_json: dbRow.stream_json,
        gpx: dbRow.gpx,
        processed_track_geojson: dbRow.processed_track_geojson,
        pause_intervals_json: dbRow.pause_intervals_json,
        downhill_segments_json: dbRow.downhill_segments_json,
        start_date: dbRow.start_date ? new Date(dbRow.start_date) : null,
        start_date_local: dbRow.start_date_local ? new Date(dbRow.start_date_local) : null,
        created_at: dbRow.created_at ? new Date(dbRow.created_at) : undefined,
        updated_at: dbRow.updated_at ? new Date(dbRow.updated_at) : undefined,
        is_trainer: !!dbRow.is_trainer,
        share_uuid: dbRow.share_uuid,
    };
}
function _calculateCumulativeDistances(coords) {
    if (!Array.isArray(coords) || coords.length === 0)
        return [];
    const distances = [0];
    let totalDistance = 0;
    for (let i = 1; i < coords.length; i++) {
        const c1 = coords[i - 1];
        const c2 = coords[i];
        if (c1 && c2 && c1.length >= 2 && c2.length >= 2) {
            totalDistance += (0, geoGpxUtils_1.haversineDistance)(c1, c2);
        }
        distances.push(totalDistance);
    }
    return distances;
}
/**
 * Prüft, ob eine spezifische Aktivität mit einem spezifischen Benutzer geteilt wurde.
 * @param activityDbId Die Datenbank-ID der Aktivität.
 * @param userId Die ID des Benutzers.
 * @returns Promise<boolean> True, wenn die Aktivität mit dem Benutzer geteilt wurde, sonst false.
 */
async function isActivitySharedWithUser(activityDbId, userId) {
    const fnLogPrefix = `[ActivityRepo IsShared Act:${activityDbId} User:${userId}]`;
    const sql = "SELECT 1 FROM shared_activities WHERE activity_id = ? AND shared_with_user_id = ? LIMIT 1";
    try {
        const [results] = await connection_1.default.query(sql, [activityDbId, userId]);
        if (results.length > 0) {
            log.debug(`${fnLogPrefix} Activity IS shared.`);
            return true;
        }
        log.debug(`${fnLogPrefix} Activity IS NOT shared.`);
        return false;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error checking if activity is shared:`, error);
        throw error; // Fehler weiterwerfen für zentrale Behandlung
    }
}
/**
 * Holt alle Aktivitäten (eigene und mit dem User geteilte) für einen bestimmten Benutzer,
 * die eine summary_polyline für die Kartenanzeige haben.
 * @param userId Die ID des Benutzers.
 * @param filterOptions Optionale Filter für sport_type oder Jahr (können später hinzugefügt werden)
 * @returns Promise<ActivityDataForGeoJsonFeature[]>
 */
async function getActivitiesForUserMap(userId, filterType, filterValue) {
    const fnLogPrefix = `[Repo GetActForUserMap User:${userId}]`;
    log.info(`${fnLogPrefix} Fetching activities for user map (own and shared)...`);
    log.info(`${fnLogPrefix} FilterType: ${filterType}, FilterValue: ${filterValue}`);
    let sql = `
        SELECT
            a.id,
            a.strava_id,
            a.summary_polyline,
            a.sport_type,
            a.activity_name, -- Für Popups ggf. nützlich
            a.start_date_local, -- Für Popups
            a.distance, -- Für Popups
            a.total_elevation_gain, -- Für Popups
            a.moving_time, -- Für Popups
            CASE
                WHEN a.user_id = ? THEN 'own'
                ELSE 'shared'
            END AS ownership_type
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE
            (a.user_id = ? OR sa.shared_with_user_id = ?)
            AND a.summary_polyline IS NOT NULL AND a.summary_polyline != ''
    `;
    // Parameter: [userId für ownership_type, userId für eigene, userId für geteilte]
    const params = [userId, userId, userId];
    if (filterType && filterValue !== null && filterValue !== undefined) {
        if (filterType === 'Year') {
            const year = parseInt(String(filterValue), 10);
            if (!isNaN(year)) {
                sql += ' AND YEAR(a.start_date_local) = ?';
                params.push(year);
            }
            else {
                log.warn(`${fnLogPrefix} Invalid year value for filter: ${filterValue}`);
            }
        }
        else if (filterType === 'Type') {
            sql += ' AND a.sport_type = ?';
            params.push(String(filterValue));
        }
        else if (filterType === 'Group') {
            log.info(`${fnLogPrefix} Filter Typ ist Gruppe `);
            sql += ' AND a.sport_type IN (';
            sql += 'Select c.sport_type from `activity_sport_group_types` c LEFT JOIN `activity_sport_groups` b  ON b.id = c.group_id Where b.name = ?)';
            params.push(String(filterValue));
        }
    }
    sql += ' ORDER BY a.start_date_local DESC';
    log.info(`${fnLogPrefix} SQL: ${sql.replace(/\s\s+/g, ' ')} `);
    try {
        const [results] = await connection_1.default.query(sql, params);
        log.info(`${fnLogPrefix} Found ${results.length} activities for user map.`);
        return results.map(row => ({
            id: row.id,
            strava_id: row.strava_id,
            summary_polyline: row.summary_polyline,
            sport_type: row.sport_type,
            // Zusätzliche Felder für Popups, falls direkt hier verwendet
            activity_name: row.activity_name,
            start_date_local: row.start_date_local ? new Date(row.start_date_local) : null,
            distance: row.distance,
            total_elevation_gain: row.total_elevation_gain,
            moving_time: row.moving_time,
            ownership_type: row.ownership_type, // 'own' oder 'shared'
            activity_id: row.id // Explizit die Aktivitäts-ID hinzufügen
        })); // Ggf. Typ erweitern für Popup-Daten
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching activities for user map:`, error);
        throw error;
    }
}
async function getDistinctActivityTypesForUser(userId) {
    const sql = `
        SELECT DISTINCT a.sport_type as type
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE (a.user_id = ? OR sa.shared_with_user_id = ?)
          AND (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
          AND a.sport_type IS NOT NULL AND a.sport_type != ''
        ORDER BY type ASC
    `;
    const [results] = await connection_1.default.query(sql, [userId, userId]);
    return results.map((r) => r.type);
}
async function getDistinctActivityYearsForUser(userId) {
    const sql = `
        SELECT DISTINCT YEAR(a.start_date_local) as year
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE (a.user_id = ? OR sa.shared_with_user_id = ?)
          AND (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
          AND a.start_date_local IS NOT NULL
        ORDER BY year DESC
    `;
    const [results] = await connection_1.default.query(sql, [userId, userId]);
    return results.map((r) => parseInt(r.year, 10)).filter(year => !isNaN(year));
}
async function getActivitySummaryById(activityId) {
    const sql = `
        SELECT activity_name, start_date_local, distance, total_elevation_gain, moving_time
        FROM activities WHERE id = ? LIMIT 1`;
    const [results] = await connection_1.default.query(sql, [activityId]);
    if (results.length > 0) {
        const summary = results[0];
        return {
            activity_name: summary.activity_name,
            start_date_local: summary.start_date_local ? new Date(summary.start_date_local) : null,
            distance: parseFloat(summary.distance || 0),
            total_elevation_gain: parseFloat(summary.total_elevation_gain || 0),
            moving_time: parseInt(summary.moving_time || 0, 10)
        };
    }
    return null;
}
// In Ihren Funktionen, die Aktivitäten laden, z.B. getActivityByPrimaryKey oder listActivities:
async function getActivityByPrimaryKey(id) {
    const fnLogPrefix = `[ActivityRepo GetByPK ID:${id}]`;
    const sql = "SELECT * FROM activities WHERE id = ?";
    try {
        const [rows] = await connection_1.default.query(sql, [id]);
        if (rows.length > 0) {
            const dbActivity = rows[0];
            if (dbActivity.user_id === null || dbActivity.user_id === undefined) {
                log.error(`${fnLogPrefix} Activity ${id} has null or undefined user_id from DB! This should not happen for a NOT NULL column.`);
                throw new Error(`Activity ${id} has invalid user_id.`);
            }
            return mapDbRowToActivity(dbActivity);
        }
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function getActivityByStravaId(stravaId) {
    const fnLogPrefix = `[ActivityRepo GetByStravaID:${stravaId}]`;
    const sql = `SELECT * FROM activities WHERE strava_id = ? LIMIT 1`;
    try {
        const [results] = await connection_1.default.query(sql, [stravaId]);
        if (results.length > 0) {
            log.debug(`${fnLogPrefix} Activity found.`);
            return mapDbRowToActivity(results[0]); // Gibt einzelnes Objekt oder null zurück
        }
        log.debug(`${fnLogPrefix} Activity not found.`);
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
/**
 * Holt eine Aktivität anhand ihrer Share-UUID für öffentliche Links
 */
async function getActivityByShareUuid(shareUuid) {
    const fnLogPrefix = `[ActivityRepo GetByShareUUID:${shareUuid}]`;
    const sql = `SELECT * FROM activities WHERE share_uuid = ? LIMIT 1`;
    try {
        const [results] = await connection_1.default.query(sql, [shareUuid]);
        if (results.length > 0) {
            log.debug(`${fnLogPrefix} Activity found.`);
            return mapDbRowToActivity(results[0]);
        }
        log.debug(`${fnLogPrefix} Activity not found.`);
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function decrementActivityPhotoCount(activityId) {
    const sql = "UPDATE activities SET photo_count = GREATEST(0, COALESCE(photo_count, 0) - 1) WHERE id = ?";
    const [result] = await connection_1.default.execute(sql, [activityId]);
    return result.affectedRows > 0;
}
async function incrementActivityPhotoCount(activityId) {
    const sql = "UPDATE activities SET photo_count = COALESCE(photo_count, 0) + 1 WHERE id = ?";
    const [result] = await connection_1.default.execute(sql, [activityId]);
    return result.affectedRows > 0;
}
async function getProcessedTrackJsonById(activityId) {
    const sql = "SELECT processed_track_geojson FROM activities WHERE id = ?";
    const [results] = await connection_1.default.query(sql, [activityId]);
    return results.length > 0 ? results[0].processed_track_geojson : null;
}
async function findActivitiesMissingProcessing() {
    const sql = "SELECT strava_id FROM activities WHERE processed_track_geojson IS NULL ORDER BY start_date_local DESC";
    const [results] = await connection_1.default.query(sql);
    return results.map((row) => row.strava_id);
}
async function setGpxStatus(activityId, hasGpx) {
    const sql = 'UPDATE `activities` SET `gpx` = ? WHERE `id` = ?';
    const [result] = await connection_1.default.query(sql, [hasGpx ? 1 : 0, activityId]);
    return result.affectedRows > 0;
}
async function setResource_state(activityId, state) {
    const sql = 'UPDATE `activities` SET `resource_state` = ? WHERE `id` = ?';
    const [result] = await connection_1.default.query(sql, [state, activityId]);
    return result.affectedRows > 0;
}
async function updateActivityPhotoCount(dbId, newCount) {
    const sql = "UPDATE activities SET photo_count = ? WHERE id = ?";
    const [result] = await connection_1.default.execute(sql, [newCount, dbId]);
    return result.affectedRows > 0;
}
async function findActivitiesWithMissingCoords() {
    const sql = `SELECT * FROM activities WHERE (is_trainer IS NULL OR is_trainer = 0) AND (start_lat IS NULL OR start_lng IS NULL OR end_lat IS NULL OR end_lng IS NULL) ORDER BY start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(dbRow => mapDbRowToActivity(dbRow));
}
async function findActivitiesWithGpxStatusSet() {
    const sql = `SELECT * FROM activities WHERE gpx = 1 ORDER BY start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(dbRow => mapDbRowToActivity(dbRow));
}
/**
 * Holt alle Aktivitäten eines Benutzers, die GPX-Dateien haben
 * @param userId Die ID des Benutzers
 * @returns Ein Array von Aktivitäten mit GPX-Dateien
 */
async function getActivitiesWithGpxForUser(userId) {
    const fnLogPrefix = `[Repo GetActivitiesWithGpxForUser:${userId}]`;
    log.info(`${fnLogPrefix} Fetching activities with GPX for user`);
    const sql = `
        SELECT * FROM activities
        WHERE user_id = ? AND gpx = 1
        ORDER BY start_date_local DESC
    `;
    try {
        const [results] = await connection_1.default.query(sql, [userId]);
        return results.map(dbRow => mapDbRowToActivity(dbRow));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function findActivitiesWithStreamsButNoGpx() {
    const sql = `SELECT * FROM activities WHERE stream_json IS NOT NULL AND (gpx IS NULL OR gpx != 1) AND (is_trainer IS NULL OR is_trainer = 0) ORDER BY start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(dbRow => mapDbRowToActivity(dbRow));
}
async function findActivitiesWithMissingStreams() {
    const sql = `
        SELECT a.* FROM activities a
        WHERE a.stream_json IS NULL
          AND NOT ( JSON_UNQUOTE(JSON_EXTRACT(a.some_json, "$.device_watts")) IS NULL AND
                JSON_UNQUOTE(JSON_EXTRACT(a.some_json, "$.has_heartrate")) = 'false' AND
                JSON_UNQUOTE(JSON_EXTRACT(a.some_json, "$.average_cadence")) IS NULL AND
                JSON_UNQUOTE(JSON_EXTRACT(a.some_json, "$.average_temp")) IS NULL AND
                a.distance = 0 )
          AND (a.is_trainer IS NULL OR a.is_trainer = 0)
        ORDER BY a.start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(dbRow => mapDbRowToActivity(dbRow));
}
async function getActivityPiDataById(activityId) {
    const sql = `
        SELECT processed_track_geojson, pause_intervals_json, downhill_segments_json
        FROM activities WHERE id = ?`;
    const [results] = await connection_1.default.query(sql, [activityId]);
    return results.length > 0 ? results[0] : null;
}
// --- getYearlySummaryStatsForUser ---
// Diese Funktion liefert die Jahresübersicht für einen spezifischen User
// (eigene Aktivitäten + mit diesem User geteilte Aktivitäten)
async function getYearlySummaryStatsForUser(userId) {
    const fnLogPrefix = `[Repo GetYearlySummaryForUser:${userId}]`;
    log.info(`${fnLogPrefix} Fetching yearly summary stats...`);
    // Sportart-Obergruppen aus der Datenbank abrufen
    // Wir verwenden hier noch die Standardwerte aus der Konfiguration,
    // da wir die Sportart-Obergruppen erst später im Code abrufen werden
    const fussTypes = config_1.default.activities.processing.fussTypes;
    const radTypes = config_1.default.activities.processing.radTypes;
    // Die SQL Aliase hier (total_count, total_distance etc.)
    // müssen zu den Properties in UserYearlySummaryStat passen.
    const sql = `
        SELECT
            YEAR(a.start_date_local) AS year,
            COUNT(DISTINCT a.id) AS total_count,                 -- Passt zu UserYearlySummaryStat.total_count
            SUM(IFNULL(a.distance,0)) AS total_distance,         -- Passt zu UserYearlySummaryStat.total_distance
            SUM(IFNULL(a.total_elevation_gain,0)) AS total_elevation, -- Passt zu UserYearlySummaryStat.total_elevation
            SUM(IFNULL(a.moving_time,0)) AS total_moving_time,   -- Passt zu UserYearlySummaryStat.total_moving_time

            SUM(CASE WHEN a.sport_type IN (?) THEN 1 ELSE 0 END) AS count_fuss,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.distance,0) ELSE 0 END) AS distance_fuss,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.total_elevation_gain,0) ELSE 0 END) AS elevation_fuss,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.moving_time,0) ELSE 0 END) AS moving_time_fuss,

            SUM(CASE WHEN a.sport_type IN (?) THEN 1 ELSE 0 END) AS count_rad,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.distance,0) ELSE 0 END) AS distance_rad,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.total_elevation_gain,0) ELSE 0 END) AS elevation_rad,
            SUM(CASE WHEN a.sport_type IN (?) THEN IFNULL(a.moving_time,0) ELSE 0 END) AS moving_time_rad
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE
            (a.user_id = ? OR sa.shared_with_user_id = ?) -- Eigene UND mit User geteilte Aktivitäten
            AND YEAR(a.start_date_local) IS NOT NULL
            AND (a.sport_type != 'VirtualRide' OR a.sport_type IS NULL)
        GROUP BY YEAR(a.start_date_local)
        ORDER BY year DESC;`;
    const params = [
        fussTypes, fussTypes, fussTypes, fussTypes,
        radTypes, radTypes, radTypes, radTypes,
        userId, userId
    ];
    try {
        const [results] = await connection_1.default.query(sql, params);
        log.info(`${fnLogPrefix} Found ${results.length} yearly summary rows for user.`);
        // Das Mapping muss jetzt die korrekten Property-Namen verwenden (ohne "_all")
        return results.map(row => ({
            year: parseInt(row.year, 10),
            total_count: parseInt(row.total_count, 10),
            total_distance: parseFloat(row.total_distance),
            total_elevation: parseFloat(row.total_elevation),
            total_moving_time: parseInt(row.total_moving_time, 10),
            count_fuss: parseInt(row.count_fuss, 10),
            distance_fuss: parseFloat(row.distance_fuss),
            elevation_fuss: parseFloat(row.elevation_fuss),
            moving_time_fuss: parseInt(row.moving_time_fuss, 10),
            count_rad: parseInt(row.count_rad, 10),
            distance_rad: parseFloat(row.distance_rad),
            elevation_rad: parseFloat(row.elevation_rad),
            moving_time_rad: parseInt(row.moving_time_rad, 10)
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function getElevationAndVerticalAnchors(activityId) {
    const displayJsonString = await getProcessedTrackJsonById(activityId);
    if (!displayJsonString)
        return null;
    try {
        const displayTrackGeoJson = JSON.parse(displayJsonString);
        const displayCoords = displayTrackGeoJson?.features?.[0]?.geometry?.coordinates;
        if (!displayCoords || !Array.isArray(displayCoords) || displayCoords.length < 2)
            return null;
        const cumulativeDistances = _calculateCumulativeDistances(displayCoords);
        const elevationProfile = displayCoords
            .map((coord, index) => ({
            d: cumulativeDistances[index], alt: coord[2] ?? null
        }))
            .filter((p) => typeof p.alt === 'number' && !isNaN(p.alt));
        // Placeholder for vertical anchors, as their generation logic is complex
        const verticalAnchors = [];
        return { elevationProfile, verticalAnchors };
    }
    catch (error) {
        log.error(`Error getElevationAndVerticalAnchors for ${activityId}:`, error);
        throw error;
    }
}
async function deleteActivityById(id) {
    const sql = 'DELETE FROM activities WHERE id = ?';
    const [result] = await connection_1.default.execute(sql, [id]);
    return result.affectedRows > 0;
}
// --- getMonthlyStats: targetUserId ist jetzt ein Pflichtparameter ---
async function getMonthlyStats(year, targetUserId // Nicht mehr optional
) {
    const fnLogPrefix = `[Repo GetMonthlyStats Y:${year} User:${targetUserId}]`; // Log angepasst
    let sql = `
        SELECT MONTH(a.start_date_local) as month_number, SUM(IFNULL(a.distance, 0)) / 1000 as total_km
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE (a.user_id = ? OR sa.shared_with_user_id = ?)
          AND YEAR(a.start_date_local) = ? `; // User-Filter immer aktiv
    const params = [targetUserId, targetUserId, year];
    sql += ` AND (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
             GROUP BY MONTH(a.start_date_local) ORDER BY month_number ASC;`;
    log.debug(`${fnLogPrefix} SQL: ${sql.replace(/\s\s+/g, ' ')}, Params: ${JSON.stringify(params)}`);
    try {
        const [results] = await connection_1.default.query(sql, params);
        return results.map(row => ({ month_number: row.month_number, total_km: parseFloat(row.total_km || 0) }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function checkActivityExists(activityId) {
    const sql = 'SELECT EXISTS (SELECT 1 FROM `activities` WHERE `id` = ?) as activityExists';
    const [results] = await connection_1.default.query(sql, [activityId]);
    return results[0].activityExists === 1;
}
/**
 * Fügt eine vom Benutzer hochgeladene Aktivität in die Datenbank ein.
 * @param activityData - Die Aktivitätsdaten
 * @param gpxFilename - Der Dateiname der GPX-Datei
 * @returns Die ResultSetHeader mit der insertId
 */
async function insertUserUploadedActivity(activityData, gpxFilename) {
    const fnLogPrefix = `[Repo InsertUserUploadedActivity]`;
    log.info(`${fnLogPrefix} Inserting user uploaded activity...`);
    // Prüfe, ob erweiterte Daten vorhanden sind
    const hasExtendedData = activityData.distance !== undefined ||
        activityData.total_elevation_gain !== undefined ||
        activityData.moving_time !== undefined ||
        activityData.elapsed_time !== undefined ||
        activityData.start_lat !== undefined ||
        activityData.start_lng !== undefined ||
        activityData.end_lat !== undefined ||
        activityData.end_lng !== undefined ||
        activityData.average_speed !== undefined ||
        activityData.max_speed !== undefined ||
        activityData.average_heartrate !== undefined ||
        activityData.max_heartrate !== undefined ||
        activityData.summary_polyline !== undefined ||
        activityData.detailed_polyline !== undefined ||
        activityData.stream_json !== undefined;
    let sql;
    let params;
    if (hasExtendedData) {
        // Erweiterte Daten vorhanden, verwende alle Felder
        sql = `
            INSERT INTO activities (
                user_id, share_uuid, activity_name, start_date, start_date_local, sport_type,
                private_note, gpx, distance, total_elevation_gain, moving_time, elapsed_time,
                start_lat, start_lng, end_lat, end_lng, average_speed, max_speed,
                average_heartrate, max_heartrate, summary_polyline, detailed_polyline, stream_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        params = [
            activityData.user_id,
            (0, shareUuidUtils_1.ensureShareUuid)(activityData.share_uuid),
            activityData.activity_name || 'Hochgeladene Aktivität',
            formatToMySQLDateTime(activityData.start_date) || formatToMySQLDateTime(new Date()),
            formatToMySQLDateTime(activityData.start_date_local) || formatToMySQLDateTime(new Date()),
            activityData.sport_type || null,
            activityData.private_note || null,
            1, // GPX-Datei vorhanden
            activityData.distance || null,
            activityData.total_elevation_gain || null,
            activityData.moving_time || null,
            activityData.elapsed_time || null,
            activityData.start_lat || null,
            activityData.start_lng || null,
            activityData.end_lat || null,
            activityData.end_lng || null,
            activityData.average_speed || null,
            activityData.max_speed || null,
            activityData.average_heartrate || null,
            activityData.max_heartrate || null,
            activityData.summary_polyline || null,
            activityData.detailed_polyline || null,
            activityData.stream_json || null
        ];
    }
    else {
        // Nur Basisdaten vorhanden, verwende minimale Felder
        sql = `
            INSERT INTO activities (
                user_id, share_uuid, activity_name, start_date, start_date_local, sport_type,
                private_note, gpx
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        params = [
            activityData.user_id,
            (0, shareUuidUtils_1.ensureShareUuid)(activityData.share_uuid),
            activityData.activity_name || 'Hochgeladene Aktivität',
            formatToMySQLDateTime(activityData.start_date) || formatToMySQLDateTime(new Date()),
            formatToMySQLDateTime(activityData.start_date_local) || formatToMySQLDateTime(new Date()),
            activityData.sport_type || null,
            activityData.private_note || null,
            1 // GPX-Datei vorhanden
        ];
    }
    try {
        const [result] = await connection_1.default.execute(sql, params);
        log.info(`${fnLogPrefix} Activity inserted with ID: ${result.insertId}`);
        return result;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error inserting user uploaded activity:`, error);
        throw error;
    }
}
async function insertActivityFromStrava(stravaData) {
    const sql = `
        INSERT INTO activities (
            strava_id, user_id, share_uuid, activity_name, start_date, start_date_local, sport_type,
            distance, total_elevation_gain, moving_time, elapsed_time,
            start_lat, start_lng, end_lat, end_lng,
            location_country, private_note, summary_polyline, detailed_polyline,
            average_speed, max_speed, average_heartrate, max_heartrate,
            photo_count, resource_state, gpx, stream_json, some_json, is_trainer
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NULL, ?, ?, ?)`;
    const params = [
        stravaData.id ?? null, stravaData.user_id ?? null,
        (0, shareUuidUtils_1.ensureShareUuid)(null), // Neue UUID für Strava-Aktivitäten generieren
        stravaData.name ?? null,
        formatToMySQLDateTime(stravaData.start_date || null), formatToMySQLDateTime(stravaData.start_date_local || null),
        stravaData.sport_type ?? null, stravaData.distance ?? null, stravaData.total_elevation_gain ?? null,
        stravaData.moving_time ?? null, stravaData.elapsed_time ?? null,
        stravaData.start_latlng?.[0] ?? null, stravaData.start_latlng?.[1] ?? null,
        stravaData.end_latlng?.[0] ?? null, stravaData.end_latlng?.[1] ?? null,
        stravaData.location_country ?? null, stravaData.private_note ?? null,
        stravaData.map?.summary_polyline ?? null, stravaData.map?.polyline ?? null,
        stravaData.average_speed ?? null, stravaData.max_speed ?? null,
        stravaData.average_heartrate ?? null, stravaData.max_heartrate ?? null,
        stravaData.total_photo_count ?? 0, stravaData.resource_state ?? null,
        JSON.stringify(stravaData),
        stravaData.trainer ? 1 : 0,
    ];
    const [result] = await connection_1.default.execute(sql, params);
    return result;
}
async function updateActivityStreamJson(activityId, streamJson) {
    const sql = 'UPDATE `activities` SET `stream_json` = ? WHERE `id` = ?';
    const [result] = await connection_1.default.query(sql, [streamJson, activityId]);
    return result;
}
async function getActivityCheckData(activityId) {
    const sql = `
        SELECT resource_state, photo_count, some_json, distance, stream_json,
               strava_id as activityID, gpx as gpx_file_status
        FROM activities WHERE id = ?`;
    const [results] = await connection_1.default.query(sql, [activityId]);
    if (results.length > 0) {
        const data = results[0];
        let someJsonParsed = {};
        try {
            if (data.some_json)
                someJsonParsed = JSON.parse(data.some_json);
        }
        catch (e) { }
        let streamState = null;
        if (data.stream_json) {
            try {
                const parsedStreams = JSON.parse(data.stream_json); // Ist ein Objekt
                if (typeof parsedStreams === 'object' && parsedStreams !== null) {
                    // Prüfe auf die Existenz eines gängigen Stream-Typs, z.B. 'latlng' oder 'time'
                    // Oder nimm den ersten Key, der nicht 'series_type' oder 'original_size' ist etc.
                    // Um es einfach zu halten, wenn latlng da ist, ist es gut:
                    if (parsedStreams.latlng && Array.isArray(parsedStreams.latlng.data) && parsedStreams.latlng.data.length > 0) {
                        streamState = 'latlng'; // Oder einen anderen Indikator, dass Streams vorhanden sind
                    }
                    else if (parsedStreams.time && Array.isArray(parsedStreams.time.data) && parsedStreams.time.data.length > 0) {
                        streamState = 'time'; // Fallback, falls kein latlng aber time da ist
                    }
                    else {
                        // Logge, welche Streams da sind, wenn die Struktur anders ist
                        log.warn(`[Repo GetActivityCheckData DB_ID:${activityId}] stream_json parsed, but no 'latlng' or 'time' array found with data. Keys: ${Object.keys(parsedStreams).join(', ')}`);
                        // streamState bleibt null oder du setzt einen spezifischen Status wie 'streams_present_no_track'
                    }
                }
                else {
                    log.warn(`[Repo GetActivityCheckData DB_ID:${activityId}] stream_json for ID ${activityId} did not parse to an object.`);
                }
            }
            catch (e) {
                log.warn(`[Repo GetActivityCheckData DB_ID:${activityId}] Failed to parse stream_json: ${e.message}. stream_json content: ${data.stream_json ? data.stream_json.substring(0, 200) + '...' : 'NULL'}`);
            }
        }
        else {
            log.debug(`[Repo GetActivityCheckData DB_ID:${activityId}] No stream_json found for ID ${activityId}.`);
        }
        const checkData = {
            resource_state: data.resource_state, photo_count: data.photo_count,
            device_watts: someJsonParsed.device_watts ?? null,
            has_heartrate: String(someJsonParsed.has_heartrate ?? 'false'),
            average_cadence: someJsonParsed.average_cadence ?? null,
            average_temp: someJsonParsed.average_temp ?? null,
            distance: data.distance, stream_state: streamState,
            activityID: data.activityID, gpx_file_status: data.gpx
        };
        checkData.stream_possible = !((checkData.device_watts == null) &&
            (checkData.has_heartrate == "false") &&
            (checkData.average_cadence == null) &&
            (checkData.average_temp == null) &&
            (checkData.distance == 0));
        return checkData;
    }
    return null;
}
async function getActivityCheckDataByStravaId(stravaActivityId) {
    const sql = `
        SELECT resource_state, photo_count, some_json, distance, stream_json,
               strava_id as activityID, gpx as gpx_file_status
        FROM activities WHERE strava_id = ?`;
    const [results] = await connection_1.default.query(sql, [stravaActivityId]);
    if (results.length > 0) {
        const data = results[0];
        let someJsonParsed = {};
        try {
            if (data.some_json)
                someJsonParsed = JSON.parse(data.some_json);
        }
        catch (e) { }
        let streamState = null;
        if (data.stream_json) {
            try {
                const s = JSON.parse(data.stream_json);
                if (Array.isArray(s) && s.length > 0)
                    streamState = s[0].type;
            }
            catch (e) { }
        }
        const checkData = {
            resource_state: data.resource_state, photo_count: data.photo_count,
            device_watts: someJsonParsed.device_watts ?? null,
            has_heartrate: String(someJsonParsed.has_heartrate ?? 'false'),
            average_cadence: someJsonParsed.average_cadence ?? null,
            average_temp: someJsonParsed.average_temp ?? null,
            distance: data.distance, stream_state: streamState,
            activityID: data.activityID, gpx_file_status: data.gpx
        };
        checkData.stream_possible = !((checkData.device_watts == null) &&
            (checkData.has_heartrate == "false") &&
            (checkData.average_cadence == null) &&
            (checkData.average_temp == null) &&
            (checkData.distance == 0));
        return checkData;
    }
    return null;
}
async function getActivityPopupDetails(activityId) {
    const fnLogPrefix = `[ActivityRepo GetPopupDetails ID:${activityId}]`;
    log.debug(`${fnLogPrefix} Fetching popup details.`);
    // Versuche zuerst, die Aktivität anhand der ID zu finden
    let sql = `
        SELECT id, strava_id, activity_name, start_date_local, distance, total_elevation_gain, moving_time, sport_type
        FROM activities WHERE id = ? LIMIT 1`;
    let [results] = await connection_1.default.query(sql, [activityId]);
    // Wenn keine Aktivität gefunden wurde, versuche es mit der Strava-ID
    if (results.length === 0) {
        log.debug(`${fnLogPrefix} Activity not found by ID, trying Strava ID.`);
        sql = `
            SELECT id, strava_id, activity_name, start_date_local, distance, total_elevation_gain, moving_time, sport_type
            FROM activities WHERE strava_id = ? LIMIT 1`;
        [results] = await connection_1.default.query(sql, [activityId]);
    }
    if (results.length > 0) {
        const db = results[0];
        log.debug(`${fnLogPrefix} Activity found. Name: ${db.activity_name}, ID: ${db.id}, Strava ID: ${db.strava_id}, Sport Type: ${db.sport_type}`);
        // Jetzt holen wir die Fotos separat
        let thumbnailUrl = null;
        let fullImageUrl = null;
        try {
            const photoSql = `
                SELECT computed_url_small, computed_url_medium, computed_url_original
                FROM activity_photos
                WHERE activity_id = ?
                LIMIT 1`;
            const [photoResults] = await connection_1.default.query(photoSql, [db.id]);
            if (photoResults.length > 0) {
                const photo = photoResults[0];
                thumbnailUrl = photo.computed_url_small || photo.computed_url_medium;
                fullImageUrl = photo.computed_url_original;
            }
        }
        catch (error) {
            log.error(`${fnLogPrefix} Error fetching photos: ${error}`);
        }
        return {
            id: db.id,
            strava_id: db.strava_id,
            activity_name: db.activity_name,
            start_date_local: db.start_date_local ? new Date(db.start_date_local) : null,
            distance: db.distance,
            total_elevation_gain: db.total_elevation_gain,
            moving_time: db.moving_time,
            thumbnail_url: thumbnailUrl,
            full_image_url: fullImageUrl,
            sport_type: db.sport_type // Sport-Typ hinzufügen
        };
    }
    log.warn(`${fnLogPrefix} Activity not found by ID or Strava ID.`);
    return null;
}
/**
 * Teilt eine Aktivität mit einem anderen Benutzer.
 * @param activityDbId Die Datenbank-ID der Aktivität.
 * @param sharedWithUserId Die ID des Benutzers, mit dem die Aktivität geteilt werden soll.
 * @returns Promise<boolean> True, wenn die Aktivität erfolgreich geteilt wurde, sonst false.
 */
async function shareActivityWithUser(activityDbId, sharedWithUserId) {
    const fnLogPrefix = `[ActivityRepo ShareActivity Act:${activityDbId} User:${sharedWithUserId}]`;
    log.info(`${fnLogPrefix} Sharing activity with user.`);
    try {
        // Hole die Aktivität, um den Besitzer zu ermitteln
        const activity = await getActivityByPrimaryKey(activityDbId);
        if (!activity) {
            log.warn(`${fnLogPrefix} Activity not found.`);
            return false;
        }
        // Prüfe, ob die Aktivität bereits mit dem Benutzer geteilt wurde
        const isAlreadyShared = await isActivitySharedWithUser(activityDbId, sharedWithUserId);
        if (isAlreadyShared) {
            log.info(`${fnLogPrefix} Activity is already shared with this user.`);
            return true; // Bereits geteilt gilt als Erfolg
        }
        // Teile die Aktivität
        const sql = `
            INSERT INTO shared_activities (activity_id, owner_user_id, shared_with_user_id, shared_at)
            VALUES (?, ?, ?, NOW())
        `;
        const [result] = await connection_1.default.execute(sql, [activityDbId, activity.user_id, sharedWithUserId]);
        log.info(`${fnLogPrefix} Activity shared successfully.`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error sharing activity:`, error);
        throw error;
    }
}
async function deleteSharedEntriesForActivity(activityDbId) {
    const fnLogPrefix = `[ActivityRepo DeleteSharedEntries Act:${activityDbId}]`;
    // Löscht Einträge, wo die Aktivität die geteilte ist ODER die eigene Aktivität ist, die geteilt wurde
    const sql = "DELETE FROM shared_activities WHERE activity_id = ? OR (owner_user_id = (SELECT user_id FROM activities WHERE id = ?) AND activity_id = ?)";
    try {
        const [result] = await connection_1.default.execute(sql, [activityDbId, activityDbId, activityDbId]);
        log.info(`${fnLogPrefix} Deleted ${result.affectedRows} entries from shared_activities.`);
        return result.affectedRows >= 0; // Auch 0 ist ein Erfolg, wenn nichts zu löschen war
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error deleting shared entries:`, error);
        throw error;
    }
}
async function getActivitiesForCategoryGeoJson(baseFilterType, // Nur noch 'Year' oder 'Type'
categoryValue, applySylvieFilter = false // Neuer optionaler Parameter
) {
    const fnLogPrefix = `[Repo GetActForCatGeoJSON BaseF:${baseFilterType} V:${categoryValue} Sylvie:${applySylvieFilter}]`;
    log.info(`${fnLogPrefix} Fetching activities...`);
    let sqlBase = `
        SELECT id, strava_id, summary_polyline, sport_type FROM activities
        WHERE (sport_type != "VirtualRide" OR sport_type IS NULL)
          AND summary_polyline IS NOT NULL AND summary_polyline != ''`;
    const sqlParams = [];
    if (baseFilterType === 'Year') {
        const year = parseInt(categoryValue, 10);
        if (isNaN(year)) {
            log.warn(`${fnLogPrefix} Invalid year value: ${categoryValue}`);
            return [];
        }
        sqlBase += ' AND YEAR(start_date_local) = ?';
        sqlParams.push(year);
    }
    else if (baseFilterType === 'Type') {
        sqlBase += ' AND sport_type = ?';
        sqlParams.push(categoryValue);
    }
    else {
        log.warn(`${fnLogPrefix} Invalid baseFilterType: ${baseFilterType}`);
        return []; // Sollte nicht passieren, wenn vom Controller korrekt aufgerufen
    }
    if (applySylvieFilter) {
        sqlBase += ' AND private_note LIKE ?';
        sqlParams.push('%sylvie%');
    }
    sqlBase += ' ORDER BY start_date_local DESC'; // Sinnvolle Sortierung hinzufügen
    try {
        const [results] = await connection_1.default.execute(sqlBase, sqlParams);
        log.info(`${fnLogPrefix} Found ${results.length} activities.`);
        return results.map(row => ({
            strava_id: row.strava_id,
            summary_polyline: row.summary_polyline,
            sport_type: row.sport_type,
            id: row.strava_id, // Strava-ID als ID verwenden
            activity_id: row.id // Explizit die Aktivitäts-ID hinzufügen
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
async function getDistinctActivityTypes(filter = 'All') {
    let sql = 'SELECT DISTINCT sport_type as type FROM `activities` WHERE (sport_type != "VirtualRide" OR sport_type IS NULL) AND sport_type IS NOT NULL AND sport_type != ""';
    const params = [];
    sql += ' ORDER BY type ASC';
    const [results] = await connection_1.default.query(sql, params);
    return results.map((r) => r.type);
}
async function getDistinctActivityYears(filter = 'All') {
    let sql = 'SELECT DISTINCT YEAR(start_date_local) as year FROM `activities` WHERE (sport_type != "VirtualRide" OR sport_type IS NULL) AND start_date_local IS NOT NULL';
    const params = [];
    sql += ' ORDER BY year DESC';
    const [results] = await connection_1.default.query(sql, params);
    return results.map((r) => parseInt(r.year, 10)).filter(year => !isNaN(year));
}
async function getActivitiesForGeoJson(filterType, filterValue) {
    const fnLogPrefix = `[Repo GetActForGeoJSON FilterType:${filterType} Val:${filterValue}]`;
    log.info(`${fnLogPrefix} Fetching activities for GeoJSON...`);
    let sql = `
        SELECT
            a.strava_id,
            a.summary_polyline,
            a.sport_type,
            a.activity_name,
            a.start_date_local,
            a.distance,
            a.total_elevation_gain,
            a.moving_time,
            a.user_id -- Wichtig, um ggf. den Besitzer zu kennen
        FROM activities a
        WHERE (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
          AND a.summary_polyline IS NOT NULL AND a.summary_polyline != ''
          -- Hier könnte ein globaler Filter hin, z.B. nur Aktivitäten von Usern, deren Karte öffentlich ist:
          -- AND a.user_id IN (SELECT id FROM users WHERE map_visibility = 'public')
    `;
    const params = [];
    if (filterType === 'Year') {
        const year = parseInt(String(filterValue), 10);
        if (!isNaN(year)) {
            sql += ' AND YEAR(a.start_date_local) = ?';
            params.push(year);
        }
        else {
            log.warn(`${fnLogPrefix} Invalid year value for filter: ${filterValue}`);
            return [];
        }
    }
    else if (filterType === 'Type') {
        sql += ' AND a.sport_type = ?';
        params.push(String(filterValue));
    }
    else {
        log.warn(`${fnLogPrefix} Invalid filterType: ${filterType}`);
        return [];
    }
    sql += ' ORDER BY a.start_date_local DESC';
    try {
        const [results] = await connection_1.default.query(sql, params);
        log.info(`${fnLogPrefix} Found ${results.length} activities for GeoJSON category.`);
        return results.map(row => ({
            strava_id: row.strava_id,
            summary_polyline: row.summary_polyline,
            sport_type: row.sport_type,
            activity_name: row.activity_name,
            start_date_local: row.start_date_local ? new Date(row.start_date_local) : null,
            distance: row.distance,
            total_elevation_gain: row.total_elevation_gain,
            moving_time: row.moving_time,
            // ownership_type wird hier nicht direkt gesetzt, da es keine User-ID des Betrachters gibt.
            // Es könnte 'owned' sein, wenn man die user_id des Activity-Owners mit einer Kontext-ID vergleicht.
            // Für eine generische Funktion ist es schwierig, dies sinnvoll zu setzen.
            id: row.strava_id, // Strava-ID als ID verwenden
            activity_id: row.id // Explizit die Aktivitäts-ID hinzufügen
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching activities for GeoJSON:`, error);
        throw error;
    }
}
async function listActivities(options = {}) {
    const { filters = {}, sortBy = 'date', sortOrder = 'DESC', page = 1, limit = config_1.default.activities.adminBrowserPageLimit || 25 } = options;
    let sql = `
        SELECT id, user_id, strava_id, activity_name, start_date_local, sport_type,
               distance AS distance_m, total_elevation_gain AS elevation_gain_m,
               gpx AS gpx_status, downhill_segments_json IS NOT NULL AS has_downhill, photo_count
        FROM activities WHERE 1=1`;
    const paramsSql = [];
    if (filters.searchTerm) {
        sql += ' AND (CAST(`strava_id` AS CHAR) = ? OR `activity_name` LIKE ?)';
        paramsSql.push(filters.searchTerm, `%${filters.searchTerm}%`);
    }
    if (filters.activityType) {
        sql += ' AND sport_type = ?';
        paramsSql.push(filters.activityType);
    }
    if (filters.dateFrom) {
        sql += ' AND DATE(start_date_local) >= ?';
        paramsSql.push(filters.dateFrom);
    }
    if (filters.dateTo) {
        sql += ' AND DATE(start_date_local) <= ?';
        paramsSql.push(filters.dateTo);
    }
    if (filters.sylvie_filter === 'yes') {
        sql += ' AND private_note LIKE ?';
        paramsSql.push('%sylvie%');
    }
    else if (filters.sylvie_filter === 'no') {
        sql += ' AND (private_note NOT LIKE ? OR private_note IS NULL)';
        paramsSql.push('%sylvie%');
    }
    if (filters.dist_min !== undefined && String(filters.dist_min) !== '') {
        sql += ' AND distance >= ?';
        paramsSql.push(parseFloat(String(filters.dist_min)));
    }
    if (filters.dist_max !== undefined && String(filters.dist_max) !== '') {
        sql += ' AND distance <= ?';
        paramsSql.push(parseFloat(String(filters.dist_max)));
    }
    if (filters.elev_min !== undefined && String(filters.elev_min) !== '') {
        sql += ' AND total_elevation_gain >= ?';
        paramsSql.push(parseFloat(String(filters.elev_min)));
    }
    if (filters.elev_max !== undefined && String(filters.elev_max) !== '') {
        sql += ' AND total_elevation_gain <= ?';
        paramsSql.push(parseFloat(String(filters.elev_max)));
    }
    const sortColumnMap = {
        'id': '`id`', 'strava_id': 'CAST(`strava_id` AS UNSIGNED)', 'name': 'activity_name', 'date': 'start_date_local',
        'type': 'sport_type', 'distance': 'distance', 'elevation': 'total_elevation_gain',
        'photo_count': 'photo_count', 'downhill_status': '(downhill_segments_json IS NOT NULL)', 'gpx_status': 'gpx'
    };
    const sortCol = sortColumnMap[sortBy] || sortColumnMap['date'];
    const orderDir = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    sql += ` ORDER BY ${sortCol} ${orderDir}, id ${orderDir}`;
    const offset = (page - 1) * limit;
    sql += ' LIMIT ? OFFSET ?';
    paramsSql.push(limit, offset);
    const [results] = await connection_1.default.query(sql, paramsSql);
    return results.map(row => ({
        id: row.id, user_id: row.user_id, strava_id: row.strava_id, activity_name: row.activity_name,
        start_date_local: row.start_date_local ? new Date(row.start_date_local) : null,
        sport_type: row.sport_type, gpx: row.gpx, photo_count: row.photo_count,
        distance_m: row.distance_m, elevation_gain_m: row.elevation_gain_m,
        has_downhill: row.has_downhill === 1
    }));
}
async function countActivities(filters = {}) {
    let sql = 'SELECT COUNT(*) as total FROM `activities` WHERE 1=1';
    const paramsSql = [];
    if (filters.searchTerm) {
        sql += ' AND (CAST(`strava_id` AS CHAR) = ? OR `activity_name` LIKE ?)';
        paramsSql.push(filters.searchTerm, `%${filters.searchTerm}%`);
    }
    if (filters.activityType) {
        sql += ' AND sport_type = ?';
        paramsSql.push(filters.activityType);
    }
    if (filters.dateFrom) {
        sql += ' AND DATE(start_date_local) >= ?';
        paramsSql.push(filters.dateFrom);
    }
    if (filters.dateTo) {
        sql += ' AND DATE(start_date_local) <= ?';
        paramsSql.push(filters.dateTo);
    }
    if (filters.sylvie_filter === 'yes') {
        sql += ' AND private_note LIKE ?';
        paramsSql.push('%sylvie%');
    }
    else if (filters.sylvie_filter === 'no') {
        sql += ' AND (private_note NOT LIKE ? OR private_note IS NULL)';
        paramsSql.push('%sylvie%');
    }
    if (filters.dist_min !== undefined && String(filters.dist_min) !== '') {
        sql += ' AND distance >= ?';
        paramsSql.push(parseFloat(String(filters.dist_min)));
    }
    if (filters.dist_max !== undefined && String(filters.dist_max) !== '') {
        sql += ' AND distance <= ?';
        paramsSql.push(parseFloat(String(filters.dist_max)));
    }
    if (filters.elev_min !== undefined && String(filters.elev_min) !== '') {
        sql += ' AND total_elevation_gain >= ?';
        paramsSql.push(parseFloat(String(filters.elev_min)));
    }
    if (filters.elev_max !== undefined && String(filters.elev_max) !== '') {
        sql += ' AND total_elevation_gain <= ?';
        paramsSql.push(parseFloat(String(filters.elev_max)));
    }
    const [results] = await connection_1.default.query(sql, paramsSql);
    return results[0].total || 0;
}
async function getStreamJsonById(activityId) {
    const sql = "SELECT stream_json FROM activities WHERE id = ?";
    const [results] = await connection_1.default.query(sql, [activityId]);
    return results.length > 0 ? results[0].stream_json : null;
}
async function getActivityWithStreams(activityId) {
    const sql = `
        SELECT id, strava_id, activity_name, private_note, start_date_local, start_date, stream_json, is_trainer
        FROM activities WHERE id = ?`;
    const [results] = await connection_1.default.query(sql, [activityId]);
    if (results.length > 0) {
        const dbActivity = results[0];
        return {
            id: dbActivity.id,
            strava_id: dbActivity.strava_id,
            activity_name: dbActivity.activity_name,
            private_note: dbActivity.private_note,
            start_date_local: dbActivity.start_date_local ? new Date(dbActivity.start_date_local) : null,
            start_date: dbActivity.start_date ? new Date(dbActivity.start_date) : null,
            stream_json: dbActivity.stream_json,
            is_trainer: dbActivity.is_trainer === 1
        };
    }
    return null;
}
async function updateActivityFromStrava(stravaData, userIdToSet) {
    const fnLogPrefix = `[Repo UpdateActivity Strava:${stravaData.id}]`;
    log.info(`${fnLogPrefix} Updating from Strava payload. User to set: ${userIdToSet === undefined ? ' (unchanged)' : userIdToSet}`);
    let sql = `UPDATE activities SET
            activity_name = ?, start_date = ?, start_date_local = ?, sport_type = ?,
            distance = ?, total_elevation_gain = ?, moving_time = ?, elapsed_time = ?,
            start_lat = ?, start_lng = ?, end_lat = ?, end_lng = ?,
            location_country = ?, private_note = ?, summary_polyline = ?, detailed_polyline = ?,
            average_speed = ?, max_speed = ?, average_heartrate = ?, max_heartrate = ?,
            photo_count = ?, resource_state = ?, some_json = ?, is_trainer = ?`;
    const params = [
        stravaData.name ?? null,
        formatToMySQLDateTime(stravaData.start_date || null), formatToMySQLDateTime(stravaData.start_date_local || null),
        stravaData.sport_type ?? null, stravaData.distance ?? null, stravaData.total_elevation_gain ?? null,
        stravaData.moving_time ?? null, stravaData.elapsed_time ?? null,
        stravaData.start_latlng?.[0] ?? null, stravaData.start_latlng?.[1] ?? null,
        stravaData.end_latlng?.[0] ?? null, stravaData.end_latlng?.[1] ?? null,
        stravaData.location_country ?? null, stravaData.private_note ?? null,
        stravaData.map?.summary_polyline ?? null, stravaData.map?.polyline ?? null,
        stravaData.average_speed ?? null, stravaData.max_speed ?? null,
        stravaData.average_heartrate ?? null, stravaData.max_heartrate ?? null,
        stravaData.total_photo_count ?? 0, stravaData.resource_state ?? null,
        JSON.stringify(stravaData),
        stravaData.trainer ? 1 : 0,
    ];
    if (userIdToSet !== undefined) {
        sql += `, user_id = ?`;
        params.push(userIdToSet);
    }
    sql += ` WHERE strava_id = ?`;
    params.push(stravaData.id);
    const [result] = await connection_1.default.execute(sql, params);
    return result;
}
async function updateActivityProcessingResults(activityId, processedTrackJson, pauseIntervalsJson, downhillSegmentsJson) {
    const sql = `UPDATE activities SET
                    processed_track_geojson = ?, pause_intervals_json = ?, downhill_segments_json = ?
                 WHERE id = ?`;
    const [result] = await connection_1.default.execute(sql, [
        processedTrackJson, pauseIntervalsJson, downhillSegmentsJson, activityId
    ]);
    return result;
}
async function findActivitiesWithUnrealisticSpeeds() {
    const defaultMaxSpeedMps = config_1.default.activities.speedThresholds.default.max_speed_mps;
    const defaultAvgSpeedMps = config_1.default.activities.speedThresholds.default.average_speed_mps;
    const sql = `SELECT *, (max_speed * 3.6) AS max_speed_kmh, (average_speed * 3.6) AS average_speed_kmh
        FROM activities WHERE (is_trainer IS NULL OR is_trainer = 0) AND (max_speed > ? OR average_speed > ?) ORDER BY start_date_local DESC;`;
    const params = [defaultMaxSpeedMps, defaultAvgSpeedMps];
    const [results] = await connection_1.default.query(sql, params);
    return results.map(dbRow => {
        const activityPart = mapDbRowToActivity(dbRow);
        let checkReason = "Unbekannt";
        if (1 > defaultMaxSpeedMps && 1 > defaultAvgSpeedMps)
            checkReason = "Max. & Durchschn. Geschw. überschritten";
        else if (1 > defaultMaxSpeedMps)
            checkReason = "Max. Geschwindigkeit überschritten";
        else if (1 > defaultAvgSpeedMps)
            checkReason = "Durchschnittsgeschwindigkeit überschritten";
        return { ...activityPart, max_speed_kmh: (1 * 3.6).toFixed(1), average_speed_kmh: (1 * 3.6).toFixed(1), check_reason: checkReason, threshold_type: 'default' };
    });
}
async function findActivitiesWithInconsistentTimes() {
    const sql = `SELECT * FROM activities WHERE moving_time > elapsed_time AND elapsed_time IS NOT NULL AND moving_time IS NOT NULL AND (is_trainer IS NULL OR is_trainer = 0) ORDER BY start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(dbRow => mapDbRowToActivity(dbRow));
}
async function findActivitiesWithPhotoCountMismatch() {
    const sql = `
        SELECT a.id, a.strava_id, a.activity_name, a.start_date_local,
               a.photo_count AS expected_count, COALESCE(pc.actual_count, 0) AS actual_count
        FROM activities a
        LEFT JOIN (SELECT activity_strava_id, COUNT(*) as actual_count FROM activity_photos GROUP BY activity_strava_id) pc
        ON a.strava_id = pc.activity_strava_id
        WHERE COALESCE(a.photo_count, 0) != COALESCE(pc.actual_count, 0)
        ORDER BY a.start_date_local DESC;`;
    const [results] = await connection_1.default.query(sql);
    return results.map(row => ({
        id: row.id, strava_id: row.strava_id, activity_name: row.activity_name,
        start_date_local: row.start_date_local ? new Date(row.start_date_local) : null,
        expected_count: row.expected_count !== null ? parseInt(row.expected_count, 10) : null,
        actual_count: row.actual_count !== null ? parseInt(row.actual_count, 10) : null,
    }));
}
// --- getActivitiesForYearGroup: targetUserId ist jetzt ein Pflichtparameter ---
async function getActivitiesForYearGroup(year, activityGroup = 'all', targetUserId // Nicht mehr optional
) {
    const fnLogPrefix = `[Repo GetAct4YearGroup Y:${year} G:${activityGroup} U:${targetUserId}]`; // Log angepasst
    log.info(`${fnLogPrefix} Fetching activities...`);
    // Sportart-Obergruppen aus der Datenbank abrufen
    // Wir verwenden hier noch die Standardwerte aus der Konfiguration,
    // da wir die Sportart-Obergruppen erst später im Code abrufen werden
    const fussTypes = config_1.default.activities.processing.fussTypes;
    const radTypes = config_1.default.activities.processing.radTypes;
    let sql = `
        SELECT a.id, DATE_FORMAT(a.start_date_local, '%Y-%m-%d') as activityDate, a.distance
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
    `;
    const params = [];
    // WHERE-Klauseln immer mit User-Filter beginnen
    let whereClauses = [`(a.user_id = ? OR sa.shared_with_user_id = ?)`];
    params.push(targetUserId, targetUserId);
    whereClauses.push(`YEAR(a.start_date_local) = ?`);
    params.push(year);
    switch (activityGroup) {
        case 'rad':
            if (radTypes.length > 0) {
                whereClauses.push(`a.sport_type IN (?)`);
                params.push(radTypes);
            }
            else {
                whereClauses.push(`1=0`);
            }
            break;
        case 'fuss':
            if (fussTypes.length > 0) {
                whereClauses.push(`a.sport_type IN (?)`);
                params.push(fussTypes);
            }
            else {
                whereClauses.push(`1=0`);
            }
            break;
        case 'all':
        default:
            whereClauses.push(`(a.sport_type != 'VirtualRide' OR a.sport_type IS NULL)`);
            break;
    }
    whereClauses.push(`a.distance > 0 AND a.start_date_local IS NOT NULL`);
    sql += ` WHERE ${whereClauses.join(' AND ')} ORDER BY a.start_date_local ASC;`;
    try {
        log.debug(`${fnLogPrefix} SQL: ${sql.replace(/\s\s+/g, ' ')}`);
        log.debug(`${fnLogPrefix} Params: ${JSON.stringify(params)}`);
        const [results] = await connection_1.default.query(sql, params);
        log.info(`${fnLogPrefix} Found ${results.length} activities.`);
        return results.map(row => ({
            id: row.id,
            activityDate: row.activityDate,
            distance: parseFloat(row.distance || 0)
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
/**
 * Holt Aktivitäten für ein bestimmtes Jahr und einen bestimmten Sporttyp
 * @param year Das Jahr
 * @param sportType Der Sporttyp
 * @param userId Die Benutzer-ID
 * @returns Promise<Array<{ activityDate: string, distance: number, id?: number }>>
 */
async function getActivitiesForYearType(year, sportType, userId) {
    const fnLogPrefix = `[Repo GetActForYearType Y:${year} T:${sportType} User:${userId}]`;
    log.debug(`${fnLogPrefix} Fetching activities for year and type...`);
    const sql = `
        SELECT a.id, DATE_FORMAT(a.start_date_local, '%Y-%m-%d') as activityDate, a.distance
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE (a.user_id = ? OR sa.shared_with_user_id = ?)
          AND YEAR(a.start_date_local) = ?
          AND a.sport_type = ?
          AND (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
          AND a.distance > 0 AND a.start_date_local IS NOT NULL
        ORDER BY a.start_date_local ASC
    `;
    const params = [userId, userId, year, sportType];
    try {
        const [results] = await connection_1.default.query(sql, params);
        log.debug(`${fnLogPrefix} Found ${results.length} activities.`);
        return results.map(row => ({
            id: row.id,
            activityDate: row.activityDate,
            distance: parseFloat(row.distance || 0)
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
/**
 * Holt Aktivitäten eines Benutzers in einem bestimmten Datumsbereich
 * @param userId Die Benutzer-ID
 * @param startDate Startdatum (YYYY-MM-DD)
 * @param endDate Enddatum (YYYY-MM-DD)
 * @returns Promise<ActivityForUserListing[]>
 */
async function getActivitiesInDateRange(userId, startDate, endDate) {
    const fnLogPrefix = `[Repo GetActivitiesInDateRange User:${userId} ${startDate} - ${endDate}]`;
    log.info(`${fnLogPrefix} Fetching activities in date range...`);
    const sql = `
        SELECT
            a.id, a.strava_id, a.activity_name, a.start_date_local, a.sport_type,
            a.distance AS distance_m, a.total_elevation_gain AS elevation_gain_m,
            a.gpx, a.share_uuid,
            (SELECT COUNT(*) FROM activity_photos ap WHERE ap.activity_id = a.id) as photo_count_from_db,
            (a.user_id = ?) AS is_owned_by_user
        FROM activities a
        LEFT JOIN shared_activities sa ON a.id = sa.activity_id
        WHERE (a.user_id = ? OR sa.shared_with_user_id = ?)
          AND DATE(a.start_date_local) >= ?
          AND DATE(a.start_date_local) <= ?
          AND (a.sport_type != "VirtualRide" OR a.sport_type IS NULL)
          AND a.start_date_local IS NOT NULL
        ORDER BY a.start_date_local DESC
    `;
    try {
        const [results] = await connection_1.default.query(sql, [userId, userId, userId, startDate, endDate]);
        log.info(`${fnLogPrefix} Found ${results.length} activities in date range.`);
        return results.map(row => ({
            id: row.id,
            strava_id: row.strava_id,
            activity_name: row.activity_name,
            start_date_local: row.start_date_local,
            sport_type: row.sport_type,
            distance_m: row.distance_m,
            elevation_gain_m: row.elevation_gain_m,
            gpx_status: row.gpx,
            share_uuid: row.share_uuid,
            photo_count_from_db: row.photo_count_from_db,
            is_owned_by_user: Boolean(row.is_owned_by_user)
        }));
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        throw error;
    }
}
//# sourceMappingURL=activityRepository.js.map