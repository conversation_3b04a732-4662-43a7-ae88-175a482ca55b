"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTrip = createTrip;
exports.getTripsForUser = getTripsForUser;
exports.getTripById = getTripById;
exports.getTripByShareUuid = getTripByShareUuid;
exports.updateTrip = updateTrip;
exports.deleteTrip = deleteTrip;
exports.addActivityToTrip = addActivityToTrip;
exports.removeActivityFromTrip = removeActivityFromTrip;
exports.addPlannedRouteToTrip = addPlannedRouteToTrip;
exports.removePlannedRouteFromTrip = removePlannedRouteFromTrip;
exports.addPoiToTrip = addPoiToTrip;
exports.removePoiFromTrip = removePoiFromTrip;
exports.getTripActivities = getTripActivities;
exports.getTripPlannedRoutes = getTripPlannedRoutes;
exports.getTripPois = getTripPois;
exports.getTripPublicStats = getTripPublicStats;
exports.getTripImages = getTripImages;
exports.getTripImagesCount = getTripImagesCount;
exports.getTripGeoData = getTripGeoData;
// src/db/tripRepository.ts
const connection_1 = __importDefault(require("./connection"));
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Erstellt eine neue Reise
 */
async function createTrip(tripData) {
    const fnLogPrefix = `[TripRepo CreateTrip]`;
    const sql = `
    INSERT INTO trips (user_id, share_uuid, title, description, start_date, end_date, is_public)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;
    try {
        const [result] = await connection_1.default.execute(sql, [
            tripData.user_id,
            tripData.share_uuid,
            tripData.title,
            tripData.description || null,
            tripData.start_date || null,
            tripData.end_date || null,
            tripData.is_public ? 1 : 0
        ]);
        log.info(`${fnLogPrefix} Trip created with ID: ${result.insertId}`);
        return result.insertId;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error creating trip:`, error);
        throw error;
    }
}
/**
 * Holt alle Reisen eines Benutzers mit Statistiken
 */
async function getTripsForUser(userId) {
    const fnLogPrefix = `[TripRepo GetTripsForUser:${userId}]`;
    const sql = `
    SELECT
      t.*,
      COUNT(DISTINCT ta.activity_id) as activities_count,
      COUNT(DISTINCT tpr.planned_route_id) as planned_routes_count,
      COUNT(DISTINCT tp.poi_id) as pois_count,
      COALESCE(SUM(a.distance), 0) as total_distance,
      COALESCE(SUM(a.total_elevation_gain), 0) as total_elevation_gain,
      COALESCE(SUM(a.moving_time), 0) as total_moving_time
    FROM trips t
    LEFT JOIN trip_activities ta ON t.id = ta.trip_id
    LEFT JOIN trip_planned_routes tpr ON t.id = tpr.trip_id
    LEFT JOIN trip_pois tp ON t.id = tp.trip_id
    LEFT JOIN activities a ON ta.activity_id = a.id
    WHERE t.user_id = ?
    GROUP BY t.id
    ORDER BY t.updated_at DESC
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [userId]);
        log.info(`${fnLogPrefix} Found ${results.length} trips`);
        return results;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trips:`, error);
        throw error;
    }
}
/**
 * Holt eine Reise anhand der ID
 */
async function getTripById(tripId, userId) {
    const fnLogPrefix = `[TripRepo GetTripById:${tripId}]`;
    let sql = `SELECT * FROM trips WHERE id = ?`;
    const params = [tripId];
    if (userId) {
        sql += ` AND user_id = ?`;
        params.push(userId);
    }
    try {
        const [results] = await connection_1.default.execute(sql, params);
        if (results.length > 0) {
            log.info(`${fnLogPrefix} Trip found`);
            return results[0];
        }
        log.info(`${fnLogPrefix} Trip not found`);
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip:`, error);
        throw error;
    }
}
/**
 * Holt eine Reise anhand der Share-UUID
 */
async function getTripByShareUuid(shareUuid) {
    const fnLogPrefix = `[TripRepo GetTripByShareUuid:${shareUuid}]`;
    const sql = `SELECT * FROM trips WHERE share_uuid = ? AND is_public = 1`;
    try {
        const [results] = await connection_1.default.execute(sql, [shareUuid]);
        if (results.length > 0) {
            log.info(`${fnLogPrefix} Public trip found`);
            return results[0];
        }
        log.info(`${fnLogPrefix} Public trip not found`);
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip by UUID:`, error);
        throw error;
    }
}
/**
 * Aktualisiert eine Reise
 */
async function updateTrip(tripId, userId, updateData) {
    const fnLogPrefix = `[TripRepo UpdateTrip:${tripId}]`;
    const fields = [];
    const values = [];
    if (updateData.title !== undefined) {
        fields.push('title = ?');
        values.push(updateData.title);
    }
    if (updateData.description !== undefined) {
        fields.push('description = ?');
        values.push(updateData.description);
    }
    if (updateData.start_date !== undefined) {
        fields.push('start_date = ?');
        values.push(updateData.start_date);
    }
    if (updateData.end_date !== undefined) {
        fields.push('end_date = ?');
        values.push(updateData.end_date);
    }
    if (updateData.is_public !== undefined) {
        fields.push('is_public = ?');
        values.push(updateData.is_public ? 1 : 0);
    }
    if (fields.length === 0) {
        log.warn(`${fnLogPrefix} No fields to update`);
        return false;
    }
    const sql = `UPDATE trips SET ${fields.join(', ')} WHERE id = ? AND user_id = ?`;
    values.push(tripId, userId);
    try {
        const [result] = await connection_1.default.execute(sql, values);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Update ${success ? 'successful' : 'failed'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating trip:`, error);
        throw error;
    }
}
/**
 * Löscht eine Reise
 */
async function deleteTrip(tripId, userId) {
    const fnLogPrefix = `[TripRepo DeleteTrip:${tripId}]`;
    const sql = `DELETE FROM trips WHERE id = ? AND user_id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, userId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Delete ${success ? 'successful' : 'failed'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error deleting trip:`, error);
        throw error;
    }
}
// === Verknüpfungs-Funktionen ===
/**
 * Fügt eine Aktivität zu einer Reise hinzu
 */
async function addActivityToTrip(tripId, activityId) {
    const fnLogPrefix = `[TripRepo AddActivity:${tripId}-${activityId}]`;
    const sql = `INSERT IGNORE INTO trip_activities (trip_id, activity_id) VALUES (?, ?)`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, activityId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Add ${success ? 'successful' : 'already exists'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error adding activity:`, error);
        throw error;
    }
}
/**
 * Entfernt eine Aktivität von einer Reise
 */
async function removeActivityFromTrip(tripId, activityId) {
    const fnLogPrefix = `[TripRepo RemoveActivity:${tripId}-${activityId}]`;
    const sql = `DELETE FROM trip_activities WHERE trip_id = ? AND activity_id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, activityId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Remove ${success ? 'successful' : 'not found'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error removing activity:`, error);
        throw error;
    }
}
/**
 * Fügt eine geplante Route zu einer Reise hinzu
 */
async function addPlannedRouteToTrip(tripId, plannedRouteId) {
    const fnLogPrefix = `[TripRepo AddPlannedRoute:${tripId}-${plannedRouteId}]`;
    const sql = `INSERT IGNORE INTO trip_planned_routes (trip_id, planned_route_id) VALUES (?, ?)`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, plannedRouteId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Add ${success ? 'successful' : 'already exists'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error adding planned route:`, error);
        throw error;
    }
}
/**
 * Entfernt eine geplante Route von einer Reise
 */
async function removePlannedRouteFromTrip(tripId, plannedRouteId) {
    const fnLogPrefix = `[TripRepo RemovePlannedRoute:${tripId}-${plannedRouteId}]`;
    const sql = `DELETE FROM trip_planned_routes WHERE trip_id = ? AND planned_route_id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, plannedRouteId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Remove ${success ? 'successful' : 'not found'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error removing planned route:`, error);
        throw error;
    }
}
/**
 * Fügt einen POI zu einer Reise hinzu
 */
async function addPoiToTrip(tripId, poiId) {
    const fnLogPrefix = `[TripRepo AddPoi:${tripId}-${poiId}]`;
    const sql = `INSERT IGNORE INTO trip_pois (trip_id, poi_id) VALUES (?, ?)`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, poiId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Add ${success ? 'successful' : 'already exists'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error adding POI:`, error);
        throw error;
    }
}
/**
 * Entfernt einen POI von einer Reise
 */
async function removePoiFromTrip(tripId, poiId) {
    const fnLogPrefix = `[TripRepo RemovePoi:${tripId}-${poiId}]`;
    const sql = `DELETE FROM trip_pois WHERE trip_id = ? AND poi_id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, [tripId, poiId]);
        const success = result.affectedRows > 0;
        log.info(`${fnLogPrefix} Remove ${success ? 'successful' : 'not found'}`);
        return success;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error removing POI:`, error);
        throw error;
    }
}
// === Detail-Funktionen ===
/**
 * Holt alle Aktivitäten einer Reise mit Details
 */
async function getTripActivities(tripId) {
    const fnLogPrefix = `[TripRepo GetTripActivities:${tripId}]`;
    const sql = `
    SELECT
      ta.trip_id,
      ta.activity_id,
      a.activity_name,
      a.sport_type,
      a.distance,
      a.total_elevation_gain,
      a.moving_time,
      a.start_date_local,
      a.share_uuid,
      ta.added_at
    FROM trip_activities ta
    JOIN activities a ON ta.activity_id = a.id
    WHERE ta.trip_id = ?
    ORDER BY a.start_date_local DESC
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId]);
        log.info(`${fnLogPrefix} Found ${results.length} activities`);
        return results;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip activities:`, error);
        throw error;
    }
}
/**
 * Holt alle geplanten Routen einer Reise mit Details
 */
async function getTripPlannedRoutes(tripId) {
    const fnLogPrefix = `[TripRepo GetTripPlannedRoutes:${tripId}]`;
    const sql = `
    SELECT
      tpr.trip_id,
      tpr.planned_route_id,
      pr.name,
      pr.sport_type,
      pr.distance_m,
      pr.elevation_gain_m,
      pr.duration,
      tpr.added_at
    FROM trip_planned_routes tpr
    JOIN planned_routes pr ON tpr.planned_route_id = pr.id
    WHERE tpr.trip_id = ?
    ORDER BY pr.name ASC
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId]);
        log.info(`${fnLogPrefix} Found ${results.length} planned routes`);
        return results;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip planned routes:`, error);
        throw error;
    }
}
/**
 * Holt alle POIs einer Reise mit Details
 */
async function getTripPois(tripId) {
    const fnLogPrefix = `[TripRepo GetTripPois:${tripId}]`;
    const sql = `
    SELECT
      tp.trip_id,
      tp.poi_id,
      p.title,
      p.poi_type,
      p.latitude,
      p.longitude,
      p.description,
      tp.added_at
    FROM trip_pois tp
    JOIN pois p ON tp.poi_id = p.id
    WHERE tp.trip_id = ?
    ORDER BY p.title ASC
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId]);
        log.info(`${fnLogPrefix} Found ${results.length} POIs`);
        return results;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip POIs:`, error);
        throw error;
    }
}
/**
 * Holt Statistiken für eine öffentliche Reise
 */
async function getTripPublicStats(tripId) {
    const fnLogPrefix = `[TripRepo GetTripPublicStats:${tripId}]`;
    const sql = `
    SELECT
      COUNT(DISTINCT ta.activity_id) as activities_count,
      COUNT(DISTINCT tpr.planned_route_id) as planned_routes_count,
      COUNT(DISTINCT tp.poi_id) as pois_count,
      COALESCE(SUM(a.distance), 0) as total_distance,
      COALESCE(SUM(a.total_elevation_gain), 0) as total_elevation_gain,
      COALESCE(SUM(a.moving_time), 0) as total_moving_time,
      COUNT(DISTINCT a.id) as total_activities
    FROM trips t
    LEFT JOIN trip_activities ta ON t.id = ta.trip_id
    LEFT JOIN trip_planned_routes tpr ON t.id = tpr.trip_id
    LEFT JOIN trip_pois tp ON t.id = tp.trip_id
    LEFT JOIN activities a ON ta.activity_id = a.id
    WHERE t.id = ?
    GROUP BY t.id
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId]);
        if (results.length > 0) {
            log.info(`${fnLogPrefix} Stats calculated`);
            return results[0];
        }
        // Fallback wenn keine Daten vorhanden
        return {
            activities_count: 0,
            planned_routes_count: 0,
            pois_count: 0,
            total_distance: 0,
            total_elevation_gain: 0,
            total_moving_time: 0,
            total_activities: 0
        };
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip stats:`, error);
        throw error;
    }
}
/**
 * Holt Bilder von verknüpften Aktivitäten einer Reise
 */
async function getTripImages(tripId, limit = 20, offset = 0) {
    const fnLogPrefix = `[TripRepo GetTripImages:${tripId}]`;
    const sql = `
    SELECT
      ap.id,
      ap.activity_id,
      ap.base_filename,
      ap.caption,
      ap.downloaded_at,
      a.activity_name,
      a.start_date_local
    FROM trip_activities ta
    JOIN activities a ON ta.activity_id = a.id
    JOIN activity_photos ap ON a.id = ap.activity_id
    WHERE ta.trip_id = ? AND ap.base_filename IS NOT NULL
    ORDER BY a.start_date_local DESC, ap.downloaded_at DESC
    LIMIT ? OFFSET ?
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId, limit, offset]);
        log.info(`${fnLogPrefix} Found ${results.length} images (limit: ${limit}, offset: ${offset})`);
        return results;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip images:`, error);
        throw error;
    }
}
/**
 * Holt die Gesamtanzahl der Bilder einer Reise
 */
async function getTripImagesCount(tripId) {
    const fnLogPrefix = `[TripRepo GetTripImagesCount:${tripId}]`;
    const sql = `
    SELECT COUNT(*) as total
    FROM trip_activities ta
    JOIN activities a ON ta.activity_id = a.id
    JOIN activity_photos ap ON a.id = ap.activity_id
    WHERE ta.trip_id = ? AND ap.base_filename IS NOT NULL
  `;
    try {
        const [results] = await connection_1.default.execute(sql, [tripId]);
        const total = results[0]?.total || 0;
        log.info(`${fnLogPrefix} Total images: ${total}`);
        return total;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip images count:`, error);
        throw error;
    }
}
/**
 * Holt GeoJSON-Daten für eine Reise (Aktivitäten und geplante Routen)
 */
async function getTripGeoData(tripId) {
    const fnLogPrefix = `[TripRepo GetTripGeoData:${tripId}]`;
    try {
        // Aktivitäten mit GPX-Daten
        const activitiesSql = `
      SELECT
        a.id,
        a.activity_name,
        a.sport_type,
        a.distance,
        a.start_date_local,
        a.gpx
      FROM trip_activities ta
      JOIN activities a ON ta.activity_id = a.id
      WHERE ta.trip_id = ? AND a.gpx = 1
      ORDER BY a.start_date_local DESC
    `;
        // Geplante Routen mit GPX-Daten
        const plannedRoutesSql = `
      SELECT
        pr.id,
        pr.name,
        pr.sport_type,
        pr.distance_m,
        pr.gpx_filename
      FROM trip_planned_routes tpr
      JOIN planned_routes pr ON tpr.planned_route_id = pr.id
      WHERE tpr.trip_id = ? AND pr.gpx_filename IS NOT NULL
      ORDER BY pr.name ASC
    `;
        // POIs
        const poisSql = `
      SELECT
        p.id,
        p.title,
        p.poi_type,
        p.latitude,
        p.longitude,
        p.description
      FROM trip_pois tp
      JOIN pois p ON tp.poi_id = p.id
      WHERE tp.trip_id = ?
      ORDER BY p.title ASC
    `;
        const [activitiesResults, plannedRoutesResults, poisResults] = await Promise.all([
            connection_1.default.execute(activitiesSql, [tripId]),
            connection_1.default.execute(plannedRoutesSql, [tripId]),
            connection_1.default.execute(poisSql, [tripId])
        ]);
        log.info(`${fnLogPrefix} Found ${activitiesResults[0].length} activities, ${plannedRoutesResults[0].length} planned routes, ${poisResults[0].length} POIs with geo data`);
        return {
            activities: activitiesResults[0],
            plannedRoutes: plannedRoutesResults[0],
            pois: poisResults[0]
        };
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching trip geo data:`, error);
        throw error;
    }
}
//# sourceMappingURL=tripRepository.js.map