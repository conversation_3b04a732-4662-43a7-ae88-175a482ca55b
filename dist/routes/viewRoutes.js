"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// routes/viewRoutes.js
const express_1 = __importDefault(require("express"));
const viewController = __importStar(require("../controllers/viewController"));
const publicActivityController = __importStar(require("../controllers/publicActivityController"));
const publicTripController = __importStar(require("../controllers/publicTripController"));
const router = express_1.default.Router();
// DEPRECATED: Route entfernt - verwende /user/activity/:id stattdessen
// router.get('/activity/:id', viewController.showActivityDetail); // 2D View
// Legacy route for backward compatibility
router.get('/activity_pi_control/:id', viewController.renderActivityPiControl); // <-- NEUE ROUTE
router.get('/pi_control_compare/:id', viewController.renderPiControlComparePage); // Neuer Endpunkt für die PI Debug-Ansicht
router.get('/elevation_graph_v2/:id', viewController.renderElevationGraphPageV2); // Neuer Endpunkt für V2
// Öffentliche Aktivitätsansicht über Share-UUID (ohne Login-Requirement)
router.get('/activity/:shareUuid', publicActivityController.showPublicActivityByUuid);
// Öffentliche Reiseansicht über Share-UUID (ohne Login-Requirement)
router.get('/trip/:shareUuid', publicTripController.showPublicTripByUuid);
//router.get('/activity_motion_pro/:id', viewController.renderActivityMotionPro);
router.get('/map/:username', viewController.showPublicUserMap); // Oder der importierte Name der Funktion
exports.default = router;
//# sourceMappingURL=viewRoutes.js.map