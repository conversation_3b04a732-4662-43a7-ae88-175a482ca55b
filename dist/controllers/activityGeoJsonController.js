"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOriginalTrackGeoJson = exports.getProcessedTrackGeoJson = exports.getElevationAnchorData = exports.getActivityDataForPiControl = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const activityRepository = __importStar(require("../db/activityRepository"));
const photoRepository = __importStar(require("../db/photoRepository"));
const log = logger_1.default.getLogger(__filename);
/**
 * API: Holt Track, Fotos, Pausen und Downhill-Segmente für PI Controller.
 *
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 *
 * @route GET /api/geojson/pi_control_data/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 *
 * @returns {Promise<void>} - JSON-Antwort mit Track, Fotos, Pausen und Downhill-Segmenten
 */
const getActivityDataForPiControl = async (req, res, next) => {
    const activityIdParam = req.params.activityId; // Dies ist die Activity ID aus der URL
    const fnName = "[Ctrl API GetActivityDataForPiControl]";
    log.info(`${fnName} Request for activity ${activityIdParam}`);
    if (!activityIdParam) { // Vereinfachte Prüfung, da Strava IDs auch nicht-numerisch sein können
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }
    const numericActiviyId = parseInt(activityIdParam, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }
    try {
        // Hole die Aktivität, um an die interne ID und andere Basisdaten zu kommen
        const activity = await activityRepository.getActivityByPrimaryKey(numericActiviyId);
        if (!activity || !activity.id) {
            res.status(404).json({ message: `Aktivität ${numericActiviyId} nicht gefunden (Basisdaten).` });
            return;
        }
        const internalActivityId = activity.id;
        // Hole die verarbeiteten Track-Daten etc. direkt mit der internen ID oder Strava ID
        const basePiData = await activityRepository.getActivityPiDataById(numericActiviyId);
        if (!basePiData) {
            // Sollte nicht passieren, wenn 'activity' gefunden wurde, aber zur Sicherheit
            res.status(404).json({ message: `Verarbeitete Trackdaten für ${numericActiviyId} nicht gefunden.` });
            return;
        }
        const result = { track: null, photos: [], pauseIntervals: [], downhillSegments: [] };
        if (basePiData.processed_track_geojson)
            try {
                result.track = JSON.parse(basePiData.processed_track_geojson);
            }
            catch (e) {
                log.error(`${fnName}: Fehler Parsen Track PI: ${e.message}`);
            }
        if (basePiData.pause_intervals_json)
            try {
                result.pauseIntervals = JSON.parse(basePiData.pause_intervals_json);
                if (!Array.isArray(result.pauseIntervals))
                    result.pauseIntervals = [];
            }
            catch (e) {
                log.error(`${fnName}: Fehler Parsen Pausen PI: ${e.message}`);
            }
        if (basePiData.downhill_segments_json)
            try {
                result.downhillSegments = JSON.parse(basePiData.downhill_segments_json);
                if (!Array.isArray(result.downhillSegments))
                    result.downhillSegments = [];
            }
            catch (e) {
                log.error(`${fnName}: Fehler Parsen Downhill PI: ${e.message}`);
            }
        try {
            const photosFromDb = await photoRepository.getPhotosForActivity(internalActivityId); // Nutzt interne ID
            result.photos = photosFromDb.map(photo => {
                // Das 'photo'-Objekt von mapDbRowToActivityPhoto enthält bereits computed_url_...
                // und die korrekte 'source_photo_id'
                let photo_id_to_use = photo.source_photo_id || photo.id; // Fallback auf DB ID, falls source_photo_id fehlt
                return {
                    photo_id: photo_id_to_use, // Ist jetzt die source_photo_id (Google oder Strava)
                    source: photo.source,
                    location: (photo.location_lat != null && photo.location_lng != null) ? [photo.location_lat, photo.location_lng] : null,
                    caption: photo.caption,
                    urls: {
                        small: photo.computed_url_small,
                        medium: photo.computed_url_medium,
                        original: photo.computed_url_original,
                        googleBase: photo.google_base_url, // Für direkten Google Zugriff, falls benötigt
                        external: photo.external_url // Link zur Quellplattform
                    },
                    dimensions: {
                        small: { width: photo.width_small, height: photo.height_small },
                        medium: { width: photo.width_medium, height: photo.height_medium },
                        original: { width: photo.width_original, height: photo.height_original }
                    },
                    created_at: photo.created_at_utc ? photo.created_at_utc.toISOString() : null
                };
            }).filter((p) => p.location !== null); // Filtere Fotos ohne Location für die Karte
        }
        catch (photoDbError) {
            log.error(`${fnName}: Fehler beim Holen/Formatieren der Fotos für Aktivität ${activityIdParam} (interne ID ${internalActivityId}): ${photoDbError.message}`);
        }
        res.json(result);
    }
    catch (error) {
        log.error(`API Fehler in ${fnName} für ${activityIdParam}:`, error);
        next(error);
    }
};
exports.getActivityDataForPiControl = getActivityDataForPiControl;
/**
 * API: Liefert Daten für das Höhenprofil-Diagramm mit vertikalen Ankern.
 *
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 *
 * @route GET /api/debug/elevation_anchor_data/:activityId
 * @route GET /api/geojson/debug/elevation_anchor_data/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 *
 * @returns {Promise<void>} - JSON-Antwort mit Höhenprofildaten
 */
const getElevationAnchorData = async (req, res, next) => {
    const activityId = req.params.activityId;
    const fnName = "[Ctrl API GetElevationAnchorData]";
    log.info(`${fnName} Request for activity ${activityId}`);
    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }
    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }
    try {
        const data = await activityRepository.getElevationAndVerticalAnchors(numericActiviyId);
        if (data && data.elevationProfile) {
            res.json(data);
        }
        else {
            res.status(404).json({ message: `Keine Höhendaten für ${activityId}.` });
        }
    }
    catch (error) {
        log.error(`${fnName} Fehler für ${activityId}:`, error);
        next(error);
    }
};
exports.getElevationAnchorData = getElevationAnchorData;
/**
 * API: Liefert den Inhalt der Spalte processed_track_geojson.
 *
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 *
 * @route GET /api/geojson/processed_track/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 *
 * @returns {Promise<void>} - GeoJSON-Antwort mit verarbeitetem Track
 */
const getProcessedTrackGeoJson = async (req, res, next) => {
    const activityId = req.params.activityId;
    const fnLogPrefix = `[Ctrl API GetProcessedTrack ${activityId}]`;
    log.info(`${fnLogPrefix} Request`);
    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }
    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }
    try {
        const processedJsonString = await activityRepository.getProcessedTrackJsonById(numericActiviyId);
        if (processedJsonString) {
            res.header('Content-Type', 'application/json');
            res.send(processedJsonString);
        }
        else {
            log.warn(`${fnLogPrefix} Kein processed_track_geojson in DB gefunden.`);
            res.status(404).json({ message: `Kein verarbeiteter Track für Aktivität ${numericActiviyId} gefunden.` });
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};
exports.getProcessedTrackGeoJson = getProcessedTrackGeoJson;
/**
 * API: Liefert eine GeoJSON FeatureCollection mit einem LineString basierend auf stream_json.
 *
 * @param req - Express Request-Objekt
 * @param res - Express Response-Objekt
 * @param next - Express NextFunction
 *
 * @route GET /api/geojson/original_track/:activityId
 * @param {string} req.params.activityId - Die Strava-ID der Aktivität
 *
 * @returns {Promise<void>} - GeoJSON-Antwort mit Original-Track
 */
const getOriginalTrackGeoJson = async (req, res, next) => {
    const activityId = req.params.activityId;
    const fnLogPrefix = `[Ctrl API GetOriginalTrack ${activityId}]`;
    log.info(`${fnLogPrefix} Request`);
    if (!activityId || !/^\d+$/.test(activityId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }
    const numericActiviyId = parseInt(activityId, 10);
    if (isNaN(numericActiviyId)) {
        // Fehler: activityIdParam war keine Zahl
        res.status(400).json({ message: "Ungültige Aktivitäts-ID (Strava)." });
        return;
    }
    try {
        const streamJsonString = await activityRepository.getStreamJsonById(numericActiviyId);
        if (!streamJsonString) {
            res.status(404).json({ message: `Keine Stream-Daten für Aktivität ${numericActiviyId} gefunden.` });
            return;
        }
        let resultTrack = null;
        try {
            const streamDataParsed = JSON.parse(streamJsonString);
            const findStream = (type) => {
                if (!Array.isArray(streamDataParsed))
                    return [];
                const stream = streamDataParsed.find(s => s.type === type);
                return (stream && Array.isArray(stream.data)) ? stream.data : [];
            };
            const latlngStream = findStream('latlng');
            const altitudeStream = findStream('altitude');
            const timeStream = findStream('time');
            if (latlngStream.length >= 2 && latlngStream.length === timeStream.length) {
                const coordinates = latlngStream.map((ll, index) => {
                    const alt = altitudeStream[index];
                    return (typeof alt === 'number' && !isNaN(alt)) ? [ll[1], ll[0], alt] : [ll[1], ll[0]];
                });
                resultTrack = {
                    type: "FeatureCollection",
                    features: [{
                            type: "Feature",
                            properties: { coordinateTimes: timeStream },
                            geometry: { type: "LineString", coordinates: coordinates }
                        }]
                };
            }
        }
        catch (parseError) {
            log.error(`${fnLogPrefix} Fehler beim Parsen/Verarbeiten von stream_json:`, parseError);
            res.status(500).json({ message: "Fehler beim Verarbeiten der Stream-Daten." });
            return;
        }
        if (resultTrack) {
            res.json(resultTrack);
        }
        else {
            res.status(404).json({ message: `Konnte keinen gültigen Original-Track für ${activityId} erstellen.` });
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} API Fehler:`, error);
        next(error);
    }
};
exports.getOriginalTrackGeoJson = getOriginalTrackGeoJson;
//# sourceMappingURL=activityGeoJsonController.js.map