{"version": 3, "file": "activityGeoJsonController.js", "sourceRoot": "", "sources": ["../../src/controllers/activityGeoJsonController.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,6DAAqC;AACrC,6EAA+D;AAC/D,uEAAyD;AAGzD,MAAM,GAAG,GAAG,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAEzC;;;;;;;;;;;GAWG;AACI,MAAM,2BAA2B,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAChH,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,uCAAuC;IACtF,MAAM,MAAM,GAAG,wCAAwC,CAAC;IACxD,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,yBAAyB,eAAe,EAAE,CAAC,CAAC;IAE9D,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,uEAAuE;QAC3F,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAC9D,OAAO;IACX,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;IACvD,IAAI,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC1B,yCAAyC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;QACvE,OAAO;IACX,CAAC;IAED,IAAI,CAAC;QACD,2EAA2E;QAC3E,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,aAAa,gBAAgB,+BAA+B,EAAE,CAAC,CAAC;YAChG,OAAO;QACX,CAAC;QACD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,EAAE,CAAC;QAEvC,oFAAoF;QACpF,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,8EAA8E;YAC9E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,+BAA+B,gBAAgB,kBAAkB,EAAE,CAAC,CAAC;YACrG,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAKR,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;QAE1E,IAAI,UAAU,CAAC,uBAAuB;YAAE,IAAI,CAAC;gBAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;YAAC,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,6BAA6B,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAAC,CAAC;QAC/L,IAAI,UAAU,CAAC,oBAAoB;YAAE,IAAI,CAAC;gBAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;gBAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC;oBAAE,MAAM,CAAC,cAAc,GAAG,EAAE,CAAC;YAAC,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,8BAA8B,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAAC,CAAC;QAC1Q,IAAI,UAAU,CAAC,sBAAsB;YAAE,IAAI,CAAC;gBAAC,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;gBAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;oBAAE,MAAM,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAAC,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,gCAAgC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAAC,CAAC;QAEtR,IAAI,CAAC;YACD,MAAM,YAAY,GAAoB,MAAM,eAAe,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB;YAEzH,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACrC,kFAAkF;gBAClF,qCAAqC;gBACrC,IAAI,eAAe,GAAoB,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,kDAAkD;gBAE5H,OAAO;oBACH,QAAQ,EAAE,eAAe,EAAE,qDAAqD;oBAChF,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,QAAQ,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,IAAI,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;oBACtH,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE;wBACF,KAAK,EAAE,KAAK,CAAC,kBAAkB;wBAC/B,MAAM,EAAE,KAAK,CAAC,mBAAmB;wBACjC,QAAQ,EAAE,KAAK,CAAC,qBAAqB;wBACrC,UAAU,EAAE,KAAK,CAAC,eAAe,EAAE,8CAA8C;wBACjF,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAM,0BAA0B;qBAC/D;oBACD,UAAU,EAAE;wBACR,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE;wBAC/D,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE;wBAClE,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,eAAe,EAAE;qBAC3E;oBACD,UAAU,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI;iBAC/E,CAAC;YACN,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,4CAA4C;QAC5F,CAAC;QAAC,OAAO,YAAiB,EAAE,CAAC;YACzB,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,2DAA2D,eAAe,gBAAgB,kBAAkB,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;QACjK,CAAC;QACD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,iBAAiB,MAAM,QAAQ,eAAe,GAAG,EAAE,KAAK,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;AACL,CAAC,CAAC;AAjFW,QAAA,2BAA2B,+BAiFtC;AAEF;;;;;;;;;;;;GAYG;AACI,MAAM,sBAAsB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC3G,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC;IACzC,MAAM,MAAM,GAAG,mCAAmC,CAAC;IACnD,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,yBAAyB,UAAU,EAAE,CAAC,CAAC;IAEzD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAC9D,OAAO;IACX,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAClD,IAAI,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC1B,yCAAyC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;QACvE,OAAO;IACX,CAAC;IAED,IAAI,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,8BAA8B,CAAC,gBAAgB,CAAC,CAAC;QACvF,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,wBAAwB,UAAU,GAAG,EAAE,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,eAAe,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;AACL,CAAC,CAAC;AA5BW,QAAA,sBAAsB,0BA4BjC;AAEF;;;;;;;;;;;GAWG;AACI,MAAM,wBAAwB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC7G,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC;IACzC,MAAM,WAAW,GAAG,+BAA+B,UAAU,GAAG,CAAC;IACjE,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,UAAU,CAAC,CAAC;IAEnC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAC9D,OAAO;IACX,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAClD,IAAI,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC1B,yCAAyC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;QACvE,OAAO;IACX,CAAC;IAED,IAAI,CAAC;QACD,MAAM,mBAAmB,GAAG,MAAM,kBAAkB,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;QACjG,IAAI,mBAAmB,EAAE,CAAC;YACtB,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAC/C,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,+CAA+C,CAAC,CAAC;YACxE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0CAA0C,gBAAgB,YAAY,EAAE,CAAC,CAAC;QAC9G,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,SAAS,EAAE,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;AACL,CAAC,CAAC;AA9BW,QAAA,wBAAwB,4BA8BnC;AAEF;;;;;;;;;;;GAWG;AACI,MAAM,uBAAuB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC5G,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC;IACzC,MAAM,WAAW,GAAG,8BAA8B,UAAU,GAAG,CAAC;IAChE,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,UAAU,CAAC,CAAC;IAEnC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAC9D,OAAO;IACX,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAClD,IAAI,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC1B,yCAAyC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;QACvE,OAAO;IACX,CAAC;IAED,IAAI,CAAC;QACD,MAAM,gBAAgB,GAAG,MAAM,kBAAkB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QACtF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,oCAAoC,gBAAgB,YAAY,EAAE,CAAC,CAAC;YACpG,OAAO;QACX,CAAC;QACD,IAAI,WAAW,GAAqC,IAAI,CAAC;QACzD,IAAI,CAAC;YACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtD,MAAM,UAAU,GAAG,CAAC,IAAY,EAAS,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAChD,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;gBAC3D,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACrE,CAAC,CAAC;YACF,MAAM,YAAY,GAA4B,UAAU,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,cAAc,GAAa,UAAU,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,UAAU,GAAa,UAAU,CAAC,MAAM,CAAC,CAAC;YAEhD,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;gBACxE,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;oBAC/C,MAAM,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;oBAClC,OAAO,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3F,CAAC,CAAC,CAAC;gBACH,WAAW,GAAG;oBACV,IAAI,EAAE,mBAAmB;oBACzB,QAAQ,EAAE,CAAC;4BACP,IAAI,EAAE,SAAS;4BACf,UAAU,EAAE,EAAE,eAAe,EAAE,UAAU,EAAE;4BAC3C,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE;yBAC7D,CAAC;iBACL,CAAC;YACN,CAAC;QACL,CAAC;QAAC,OAAO,UAAe,EAAE,CAAC;YACvB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,kDAAkD,EAAE,UAAU,CAAC,CAAC;YACxF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC,CAAC;YAC/E,OAAO;QACX,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,6CAA6C,UAAU,aAAa,EAAE,CAAC,CAAC;QAC5G,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,cAAc,EAAE,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;AACL,CAAC,CAAC;AAhEW,QAAA,uBAAuB,2BAgElC"}