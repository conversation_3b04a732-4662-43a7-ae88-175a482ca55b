"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.showPublicActivityByUuid = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
const activityRepository = __importStar(require("../db/activityRepository"));
const activityEquipmentRepository = __importStar(require("../db/activityEquipmentRepository"));
/**
 * Zeigt eine öffentliche Aktivitätsdetailseite anhand der Share-UUID an.
 * Diese Route ist ohne Login-Requirement zugänglich.
 */
const showPublicActivityByUuid = async (req, res, next) => {
    const shareUuid = req.params.shareUuid;
    const fnLogPrefix = `[Ctrl PublicActivity UUID:${shareUuid}]`;
    if (!shareUuid || shareUuid.length !== 36) {
        res.status(400).render('error', {
            pageTitle: 'Fehler',
            message: 'Ungültige Aktivitäts-UUID.',
            statusCode: 400,
            layout: 'layouts/simple_layout'
        });
        return;
    }
    try {
        log.info(`${fnLogPrefix} Fetching public activity by UUID...`);
        // Hole die Aktivität anhand der UUID
        const activity = await activityRepository.getActivityByShareUuid(shareUuid);
        if (!activity) {
            log.warn(`${fnLogPrefix} Activity not found for UUID.`);
            res.status(404).render('error', {
                pageTitle: 'Aktivität nicht gefunden',
                message: 'Die angeforderte Aktivität wurde nicht gefunden oder ist nicht mehr verfügbar.',
                statusCode: 404,
                layout: 'layouts/simple_layout'
            });
            return;
        }
        log.info(`${fnLogPrefix} Activity found: ${activity.activity_name || `ID ${activity.id}`}`);
        // Hole verknüpfte Ausrüstung für die Anzeige (nur Details, keine Bearbeitungsmöglichkeit)
        let linkedEquipmentDetails = [];
        try {
            linkedEquipmentDetails = await activityEquipmentRepository.getEquipmentForActivity(activity.id);
        }
        catch (equipmentError) {
            log.warn(`${fnLogPrefix} Error fetching linked equipment:`, equipmentError);
            // Fehler beim Laden der Ausrüstung ist nicht kritisch für die öffentliche Ansicht
        }
        log.info(`${fnLogPrefix} Rendering public activity page.`);
        res.render('users/activity', {
            pageTitle: `Aktivität: ${activity.activity_name || `ID ${activity.id}`}`,
            activity: activity,
            userEquipment: [], // Keine Ausrüstungsliste für öffentliche Ansicht
            linkedEquipmentIds: [], // Keine IDs für öffentliche Ansicht
            linkedEquipmentDetails: linkedEquipmentDetails, // Details der verknüpften Ausrüstung für die Anzeige
            isOwner: false, // Immer false für öffentliche Ansicht
            isPublicView: true // Flag für die View, um öffentliche Ansicht zu kennzeichnen
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error rendering public activity page:`, error);
        next(error);
    }
};
exports.showPublicActivityByUuid = showPublicActivityByUuid;
//# sourceMappingURL=publicActivityController.js.map