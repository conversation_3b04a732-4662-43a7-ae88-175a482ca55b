"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.showPublicTripByUuid = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const tripRepository = __importStar(require("../db/tripRepository"));
const log = logger_1.default.getLogger(__filename);
/**
 * Zeigt eine öffentliche Reise anhand der Share-UUID an.
 * Diese Route ist ohne Login-Requirement zugänglich.
 * GET /show/trip/:shareUuid
 */
const showPublicTripByUuid = async (req, res, next) => {
    const shareUuid = req.params.shareUuid;
    const fnLogPrefix = `[Ctrl PublicTrip UUID:${shareUuid}]`;
    if (!shareUuid || shareUuid.length !== 36) {
        res.status(400).render('error', {
            pageTitle: 'Fehler',
            message: 'Ungültige Reise-UUID.',
            statusCode: 400,
            layout: 'layouts/simple_layout'
        });
        return;
    }
    try {
        log.info(`${fnLogPrefix} Fetching public trip by UUID...`);
        // Hole die Reise anhand der UUID (nur öffentliche)
        const trip = await tripRepository.getTripByShareUuid(shareUuid);
        if (!trip) {
            log.warn(`${fnLogPrefix} Trip not found for UUID.`);
            res.status(404).render('error', {
                pageTitle: 'Reise nicht gefunden',
                message: 'Die angeforderte Reise wurde nicht gefunden oder ist nicht öffentlich verfügbar.',
                statusCode: 404,
                layout: 'layouts/simple_layout'
            });
            return;
        }
        log.info(`${fnLogPrefix} Public trip found: ${trip.title}`);
        // Hole verknüpfte Daten und Statistiken
        const [activities, plannedRoutes, pois, stats] = await Promise.all([
            tripRepository.getTripActivities(trip.id),
            tripRepository.getTripPlannedRoutes(trip.id),
            tripRepository.getTripPois(trip.id),
            tripRepository.getTripPublicStats(trip.id)
        ]);
        log.info(`${fnLogPrefix} Trip data loaded: ${activities.length} activities, ${plannedRoutes.length} planned routes, ${pois.length} POIs`);
        // Formatiere Statistiken für die Anzeige
        const formattedStats = {
            ...stats,
            total_distance_km: stats.total_distance ? (stats.total_distance / 1000).toFixed(1) : '0',
            total_elevation_gain_formatted: stats.total_elevation_gain ? Math.round(stats.total_elevation_gain).toString() : '0',
            total_moving_time_formatted: formatMovingTime(stats.total_moving_time)
        };
        log.info(`${fnLogPrefix} Rendering public trip page.`);
        res.render('public/trip', {
            pageTitle: `Reise: ${trip.title}`,
            trip: trip,
            activities: activities,
            plannedRoutes: plannedRoutes,
            pois: pois,
            stats: formattedStats,
            isPublicView: true,
            layout: 'layouts/simple_layout'
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error rendering public trip page:`, error);
        next(error);
    }
};
exports.showPublicTripByUuid = showPublicTripByUuid;
/**
 * Formatiert die Bewegungszeit in ein lesbares Format
 */
function formatMovingTime(seconds) {
    if (!seconds || seconds === 0)
        return '0h 0m';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    }
    else {
        return `${minutes}m`;
    }
}
//# sourceMappingURL=publicTripController.js.map