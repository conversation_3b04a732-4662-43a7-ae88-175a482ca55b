"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// src/scripts/migrateShareUuids.ts
const shareUuidUtils_1 = require("../utils/shareUuidUtils");
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Migrations-Skript zum Hinzufügen von UUIDs zu bestehenden Aktivitäten
 */
async function runMigration() {
    const fnLogPrefix = '[Migration ShareUUIDs]';
    try {
        log.info(`${fnLogPrefix} Starte Migration für Share-UUIDs...`);
        const result = await (0, shareUuidUtils_1.migrateExistingActivitiesWithUuids)();
        if (result.success) {
            log.info(`${fnLogPrefix} Migration erfolgreich abgeschlossen: ${result.message}`);
            console.log(`✅ Migration erfolgreich: ${result.message}`);
        }
        else {
            log.error(`${fnLogPrefix} Migration fehlgeschlagen: ${result.message}`);
            console.error(`❌ Migration fehlgeschlagen: ${result.message}`);
            process.exit(1);
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} Unerwarteter Fehler bei der Migration:`, error);
        console.error(`❌ Unerwarteter Fehler: ${error.message}`);
        process.exit(1);
    }
    process.exit(0);
}
// Skript ausführen, wenn direkt aufgerufen
if (require.main === module) {
    runMigration();
}
//# sourceMappingURL=migrateShareUuids.js.map